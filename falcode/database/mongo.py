import json
import logging
from datetime import datetime, timezone
from typing import Optional, cast
from uuid import UUID

from pymongo import MongoClient
from pymongo.collection import Collection
from pymongo.database import Database
from pymongo.errors import PyMongoError

from falcode._params import MONGO_DB_CONNECTION_STRING

logger = logging.getLogger(__name__)


class DateTimeEncoder(json.JSONEncoder):
    """Custom JSON encoder for datetime and UUID objects"""

    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, UUID):
            return str(obj)
        return super().default(obj)


class MongoDB:
    def __init__(self):
        self.connection_string = str(MONGO_DB_CONNECTION_STRING)
        self.client: MongoClient
        self.database: Database
        self.users_collection: Collection

        self._initialized = False

    def initialize(self):
        if self._initialized:
            logger.debug("Database already initialized, skipping initialization")
            return
        try:
            self.client = MongoClient(self.connection_string)
            self.client.admin.command("ping")
            logger.info("Successfully connected to MongoDB")
            self.database = self.client.get_database("falcode")
            logger.info(f"Using database: falcode")
            self.users_collection = self.database.get_collection("user")
            self._initialized = True
        except PyMongoError as e:
            logger.error(f"Failed to initialize MongoDB: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error initializing MongoDB: {str(e)}")
            raise

    def _serialize_data(self, data: dict[str, object]) -> dict[str, object]:
        """Serialize data to JSON-compatible format"""
        return json.loads(json.dumps(data, cls=DateTimeEncoder))

    def add_user(self, user: dict[str, object]) -> None:
        if not self._initialized:
            self.initialize()

        try:
            serialized_user = self._serialize_data(user)
            # Add timestamp
            serialized_user["created_at"] = datetime.now(timezone.utc)
            self.users_collection.insert_one(serialized_user)
            logger.info(f"Added user: {user.get('id')}")
        except PyMongoError as e:
            logger.error(f"Failed to add user: {str(e)}")
            raise

    def get_user_by_id(self, user_id: int) -> Optional[dict]:
        """Get user by GitHub user ID."""
        if not self._initialized:
            self.initialize()
        try:
            user = self.users_collection.find_one({"id": user_id})
            return user
        except PyMongoError as e:
            logger.error(f"Failed to get user: {str(e)}")
            return None

    def update_user_token(self, user_id: int, encrypted_token: str) -> bool:
        """Update user's encrypted GitHub token."""
        if not self._initialized:
            self.initialize()
        try:
            result = self.users_collection.update_one(
                {"id": user_id},
                {
                    "$set": {
                        "github_token_encrypted": encrypted_token,
                        "updated_at": datetime.now(timezone.utc),
                    }
                },
            )
            return result.modified_count > 0
        except PyMongoError as e:
            logger.error(f"Failed to update user token: {str(e)}")
            return False
