import logging
import os
from typing import Optional
from urllib.parse import urlenco<PERSON>

from fastapi import API<PERSON><PERSON><PERSON>, <PERSON><PERSON>, HTTPEx<PERSON>, status
from fastapi.responses import RedirectResponse

from falcode._params import GITHUB_OAUTH_CLIENT_ID, JWT_EXPIRATION_HOURS
from falcode.database.mongo import MongoDB
from falcode.utils.encryption import get_encryption_service
from falcode.utils.github_utils import exchange_code, get_user_info
from falcode.utils.jwt_service import get_jwt_service

FRONTEND_URL = os.getenv("FRONTEND_URL", "https://harmless-fit-mink.ngrok-free.app")
db = MongoDB()

logger = logging.getLogger(__name__)
login_router = APIRouter()


@login_router.get("/github/authorize")
async def github_authorize(redirect_uri: Optional[str] = None):
    """Initiate GitHub OAuth flow."""
    if not GITHUB_OAUTH_CLIENT_ID:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="GitHub OAuth is not configured",
        )

    # Backend callback URL (this FastAPI endpoint)
    callback_uri = (
        redirect_uri
        or "https://harmless-fit-mink.ngrok-free.app/api/v1/login/github/callback"
    )
    scope = "read:user user:email"

    params = {
        "client_id": GITHUB_OAUTH_CLIENT_ID,
        "redirect_uri": callback_uri,
        "scope": scope,
        "state": "random_state_string",
    }

    authorize_url = f"https://github.com/login/oauth/authorize?{urlencode(params)}"
    logger.info(f"Redirecting to GitHub OAuth: {authorize_url}")

    return RedirectResponse(url=authorize_url, status_code=status.HTTP_302_FOUND)


@login_router.get("/github/callback")
async def github_callback(code: Optional[str] = None, error: Optional[str] = None):
    """Handle GitHub OAuth callback."""
    if error:
        logger.error(f"GitHub OAuth error: {error}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"GitHub OAuth error: {error}",
        )

    if not code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No authorization code provided",
        )

    try:
        # Exchange code for access token
        token_data = exchange_code(code)

        if "error" in token_data:
            logger.error(f"Token exchange error: {token_data}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to exchange code: {token_data.get('error_description', 'Unknown error')}",
            )

        access_token = token_data.get("access_token")
        if not access_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No access token received from GitHub",
            )

        # Get user information
        user_data = get_user_info(access_token)
        if not user_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to retrieve user information",
            )

        user_id = user_data.get("id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User ID not found in GitHub response",
            )

        # Check if user already exists
        existing_user = db.get_user_by_id(user_id)

        # Encrypt the GitHub access token
        encryption_service = get_encryption_service()
        encrypted_token = encryption_service.encrypt(access_token)

        if existing_user:
            # User exists - update their token and log them in
            logger.info(f"Existing user logging in: {user_data.get('login')}")
            db.update_user_token(int(user_id), encrypted_token)
        else:
            # New user - create account
            logger.info(f"New user registering: {user_data.get('login')}")
            db.add_user(user_data)
            db.update_user_token(int(user_id), encrypted_token)

        # Create JWT session token (only contains user ID and basic info)
        jwt_service = get_jwt_service()
        session_token = jwt_service.create_token(
            {
                "user_id": user_id,
                "login": user_data.get("login"),
                "email": user_data.get("email"),
            }
        )

        # Redirect to frontend without token in URL
        # The session token will be set as an HTTP-only cookie by the frontend
        redirect_url = f"{FRONTEND_URL}/auth/callback"
        logger.info(f"Redirecting to frontend for user: {user_data.get('login')}")

        response = RedirectResponse(url=redirect_url, status_code=status.HTTP_302_FOUND)

        # Set HTTP-only, secure cookie with the session token
        # The cookie will be automatically sent with subsequent requests
        response.set_cookie(
            key="session_token",
            value=session_token,
            max_age=JWT_EXPIRATION_HOURS * 3600,  # Convert hours to seconds
            httponly=True,  # Prevent JavaScript access
            secure=True,  # Only send over HTTPS
            samesite="lax",  # CSRF protection
            path="/",
        )

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Unexpected error during GitHub OAuth: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred during authentication",
        )


@login_router.get("/validate-session")
async def validate_session(authorization: Optional[str] = Header(None)):
    """Validate session token and return user data.

    This endpoint is called by the frontend after OAuth callback to:
    1. Validate the JWT session token from Authorization header
    2. Retrieve user data from the database
    3. Return user info (without sensitive tokens)

    The session token should be passed in the Authorization header as:
    Authorization: Bearer <session_token>
    """
    try:
        # Extract token from Authorization header
        if not authorization or not authorization.startswith("Bearer "):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Missing or invalid Authorization header",
            )

        session_token = authorization[7:]  # Remove "Bearer " prefix

        jwt_service = get_jwt_service()
        payload = jwt_service.verify_token(session_token)

        user_id = payload.get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid session token",
            )

        # Retrieve user from database
        user = db.get_user_by_id(int(user_id))
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found",
            )

        # Return user data (without sensitive tokens)
        return {
            "id": user.get("id"),
            "login": user.get("login"),
            "name": user.get("name"),
            "email": user.get("email"),
            "avatar_url": user.get("avatar_url"),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Session validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired session token",
        )
