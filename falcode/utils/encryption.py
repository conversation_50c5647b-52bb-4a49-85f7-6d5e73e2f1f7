"""Encryption utilities for sensitive data like GitHub tokens."""

import base64
import logging
from typing import Optional

from cryptography.fernet import <PERSON><PERSON><PERSON>, InvalidToken

from falcode._params import ENCRYPTION_KEY

logger = logging.getLogger(__name__)


class EncryptionService:
    """Service for encrypting and decrypting sensitive data."""

    def __init__(self, key: Optional[str] = None):
        """Initialize encryption service with a key.

        Args:
            key: Encryption key. If None, uses ENCRYPTION_KEY from environment.
        """
        self.key = key or ENCRYPTION_KEY

        # Ensure key is properly formatted for <PERSON>rne<PERSON>
        if isinstance(self.key, str):
            # If key is a string, encode it and create a valid Fernet key
            key_bytes = self.key.encode()
            # Pad or truncate to 32 bytes, then base64 encode
            key_bytes = (key_bytes + b"\0" * 32)[:32]
            self.key = base64.urlsafe_b64encode(key_bytes)

        try:
            self.cipher = Fernet(self.key)
        except Exception as e:
            logger.error(f"Failed to initialize encryption cipher: {e}")
            raise

    def encrypt(self, data: str) -> str:
        """Encrypt a string.

        Args:
            data: String to encrypt

        Returns:
            Encrypted string (base64 encoded)
        """
        try:
            encrypted = self.cipher.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted).decode()
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise

    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt a string.

        Args:
            encrypted_data: Encrypted string (base64 encoded)

        Returns:
            Decrypted string

        Raises:
            InvalidToken: If decryption fails
        """
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted = self.cipher.decrypt(encrypted_bytes)
            return decrypted.decode()
        except InvalidToken:
            logger.error("Decryption failed: Invalid token")
            raise
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise


# Global encryption service instance
_encryption_service: Optional[EncryptionService] = None


def get_encryption_service() -> EncryptionService:
    """Get or create the global encryption service instance."""
    global _encryption_service
    if _encryption_service is None:
        _encryption_service = EncryptionService()
    return _encryption_service
