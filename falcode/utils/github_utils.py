import logging
from typing import Dict, Optional

import requests

from falcode._params import GITHUB_OAUTH_CLIENT_ID, GITHUB_OAUTH_CLIENT_SECRET

logger = logging.getLogger(__name__)


def exchange_code(code: str, redirect_uri: Optional[str] = None) -> Dict:
    """Exchange GitHub OAuth code for access token."""
    url = "https://github.com/login/oauth/access_token"
    headers = {"Accept": "application/json"}

    data = {
        "client_id": GITHUB_OAUTH_CLIENT_ID,
        "client_secret": GITHUB_OAUTH_CLIENT_SECRET,
        "code": code,
    }

    if redirect_uri:
        data["redirect_uri"] = redirect_uri

    try:
        response = requests.post(url, headers=headers, data=data, timeout=10)
        response.raise_for_status()
        token_data = response.json()

        if "error" in token_data:
            logger.error(f"GitHub token exchange error: {token_data}")
            return token_data

        logger.info("Successfully exchanged code for access token")
        return token_data

    except requests.RequestException as e:
        logger.error(f"Failed to exchange code: {e}")
        return {"error": "request_failed", "error_description": str(e)}


def get_user_info(token: str) -> Dict:
    """Get GitHub user information using access token."""
    url = "https://api.github.com/user"
    headers = {
        "Accept": "application/json",
        "Authorization": f"Bearer {token}",
    }

    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        user_data = response.json()

        scopes = response.headers.get("X-OAuth-Scopes", "")
        logger.info(f"User authenticated with scopes: {scopes}")

        return user_data

    except requests.RequestException as e:
        logger.error(f"Failed to get user info: {e}")
        return {}
