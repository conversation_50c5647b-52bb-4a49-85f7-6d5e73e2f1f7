"""JWT token generation and validation service."""

import logging
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional

import jwt

from falcode._params import JWT_ALGORITHM, JWT_EXPIRATION_HOURS, JWT_SECRET_KEY

logger = logging.getLogger(__name__)


class JWTService:
    """Service for creating and validating JWT tokens."""

    def __init__(
        self,
        secret_key: str = JWT_SECRET_KEY,
        algorithm: str = JWT_ALGORITHM,
        expiration_hours: int = JWT_EXPIRATION_HOURS,
    ):
        """Initialize JWT service.

        Args:
            secret_key: Secret key for signing tokens
            algorithm: JWT algorithm (default: HS256)
            expiration_hours: Token expiration time in hours
        """
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.expiration_hours = expiration_hours

    def create_token(self, data: Dict[str, Any]) -> str:
        """Create a JWT token.

        Args:
            data: Dictionary of claims to include in the token

        Returns:
            Encoded JWT token
        """
        try:
            # Create a copy to avoid modifying the original
            payload = data.copy()

            # Add standard claims
            now = datetime.now(timezone.utc)
            payload.update(
                {
                    "iat": now,
                    "exp": now + timedelta(hours=self.expiration_hours),
                }
            )

            token = jwt.encode(
                payload,
                self.secret_key,
                algorithm=self.algorithm,
            )
            logger.info(f"JWT token created for user: {data.get('user_id')}")
            return token
        except Exception as e:
            logger.error(f"Failed to create JWT token: {e}")
            raise

    def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify and decode a JWT token.

        Args:
            token: JWT token to verify

        Returns:
            Decoded token payload

        Raises:
            jwt.InvalidTokenError: If token is invalid or expired
        """
        try:
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm],
            )
            logger.info(f"JWT token verified for user: {payload.get('user_id')}")
            return payload
        except jwt.ExpiredSignatureError:
            logger.error("JWT token has expired")
            raise
        except jwt.InvalidTokenError as e:
            logger.error(f"Invalid JWT token: {e}")
            raise
        except Exception as e:
            logger.error(f"Failed to verify JWT token: {e}")
            raise


# Global JWT service instance
_jwt_service: Optional[JWTService] = None


def get_jwt_service() -> JWTService:
    """Get or create the global JWT service instance."""
    global _jwt_service
    if _jwt_service is None:
        _jwt_service = JWTService()
    return _jwt_service
