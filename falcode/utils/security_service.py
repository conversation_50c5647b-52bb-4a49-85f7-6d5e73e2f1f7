import os

from fastapi import HTT<PERSON><PERSON>x<PERSON>, Query, Security, status
from fastapi.security import <PERSON><PERSON>eyHeader

from falcode._params import API_KEY

api_key_header = APIKeyHeader(name="X-API-Key", auto_error=False)


class SecurityService:
    @staticmethod
    async def get_api_key(
        api_key_header: str = Security(api_key_header),
        api_key_query: str = Query(None, alias="api_key"),
    ):
        api_key = api_key_header or api_key_query
        if not api_key or api_key != API_KEY:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or missing API Key",
            )
        return api_key
