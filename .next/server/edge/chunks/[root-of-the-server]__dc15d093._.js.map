{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\n\nexport function middleware() {\n  // For dashboard routes, we rely on the AuthProvider in the client to check authentication\n  // The middleware here is just a basic check - the real validation happens on the client\n  // and when making API calls to the backend with the JWT token\n\n  // If accessing dashboard, allow it through - the client will handle auth redirects\n  // This prevents middleware from blocking legitimate requests\n  return NextResponse.next();\n}\n\nexport const config = {\n  matcher: [\"/dashboard/:path*\"],\n};\n\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEO,SAAS;IACd,0FAA0F;IAC1F,wFAAwF;IACxF,8DAA8D;IAE9D,mFAAmF;IACnF,6DAA6D;IAC7D,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QAAC;KAAoB;AAChC"}}]}