{"version": 3, "file": "../pages/_error.js", "mappings": "+bACA,uDAAyF,eCCzF,cACA,0CAEA,kBACA,cAEA,qBACA,YACA,EAAK,GACL,CA0BA,GAAS,CAzBT,cACA,gCACA,6DAAuF,WAEvF,WAEA,+BAEA,OAAmB,gBACnB,yDAEA,eACA,6DACA,iDACA,gDACA,UAQA,OAJA,YAEA,cAEA,CACA,YCpCA,2ICoBA,qCAAwBA,aAnBuC,OAgBzDC,EAAuC,KAAO,EAC9CC,EAAiC,KAAO,EAE/B,EAFmCC,OAAAA,CADhBC,CAGCC,CAAsB,CAF7BD,EAD2C,GACZ,EAGzD,GAAM,aAAEE,CAAW,yBAAEC,CAAuB,CAAE,CAAGF,EAEjD,SAASG,IACP,GAAIF,GAAeA,EAAYG,gBAAgB,CAAE,CAC/C,IAAMC,EAAeC,EAAAA,QAAQ,CAACC,OAAO,CACnCC,MAAMC,IAAI,CAACR,EAAYG,gBAAgB,EAA0BM,MAAM,CACrEC,UAGJV,EAAYW,UAAU,CAACV,EAAwBG,EAAcL,GAC/D,CACF,CA2CA,OAxCEC,OAAAA,EAAAA,GAAAA,GAAAA,CAAAA,EAAAA,EAAaG,gBAAAA,GAAbH,EAA+BY,GAAG,CAACb,EAAMc,QAAQ,EACjDX,IAGFP,EAA0B,SACxBK,EACA,OADAA,MAAAA,CAAAA,EAA6B,GAA7BA,IAAAA,EAAAA,EAAaG,gBAAAA,GAAbH,EAA+BY,GAAG,CAACb,EAAMc,QAAQ,EAC1C,SACLb,CAAAA,OAAAA,GAAAA,EAAAA,KAAAA,EAAAA,EAAaG,gBAAAA,GAAbH,EAA+Bc,MAAM,CAACf,EAAMc,QAAQ,CACtD,CACF,GAOAlB,EAA0B,KACpBK,GACFA,GAAYe,OADG,OACW,CAAGb,CAAAA,EAExB,KACDF,IACFA,EAAYe,OADG,OACW,CAAGb,CAAAA,CAEjC,IAGFN,EAAoB,KACdI,GAAeA,EAAYe,cAAc,EAAE,CAC7Cf,EAAYe,cAAc,GAC1Bf,EAAYe,cAAc,CAAG,MAExB,KACDf,GAAeA,EAAYe,cAAc,EAAE,CAC7Cf,EAAYe,cAAc,GAC1Bf,EAAYe,cAAc,CAAG,KAEjC,IAGK,IACT,qGCjESC,qCAAAA,KAXT,IAAIA,EAAW,IAAgB,aCA/B,yCCCA,qCAA6C,CAC7C,QACA,CAAC,EAAC,CAQF,cACA,0CACA,cACA,QACA,CAAK,CACL,EACA,GACA,6BACA,QACA,CAAK,CACL,0BACA,QACA,CAAK,CACL,0BACA,QACA,CAAK,CACL,6BACA,QACA,CAAK,CACL,0BACA,QACA,CACA,CAAC,EACD,4CACA,gBACA,eACA,+BACA,CACA,gBAEA,OADA,OACA,CACA,CACA,kBACA,WAEA,OADA,OACA,MACA,CACA,gBACA,WAEA,OADA,YACA,MACA,2ICnBqBC,sCAjCH,YAWkB,OAcpC,eAAeC,EAAmB,CAGrB,EAHqB,IAChCC,WAAS,KACTC,CAAG,CACQ,CAHqB,EAKhC,MAAO,CAAEC,UADS,MAAMC,CAAAA,EAAAA,EAAAA,mBAAAA,EAAoBH,EAAWC,EACpC,CACrB,CAEe,MAAMH,UAAsCM,EAAAA,OAAK,CAACJ,SAAS,CAOxEK,QAAS,CACP,GAAM,CAAEL,WAAS,WAAEE,CAAS,CAAE,CAAG,IAAI,CAACtB,KAAK,CAE3C,MAAO,UAACoB,EAAAA,CAAW,GAAGE,CAAS,EACjC,CACF,CAZqBJ,EAIZQ,mBAAAA,CAAsBP,EAJVD,EAKZS,eAAAA,CAAkBR,oOCtC3B,qFCAA,yDCCA,+CAAiF,gBCD1E,SAASS,EAAY,OAC1BC,YAAW,CAAK,QAChBC,GAAS,CAAK,UACdC,GAAW,CAAK,CACjB,CAJ2B,WAIxB,CAAC,EAJuB,EAK1B,OAAOF,GAAaC,GAAUC,CAChC,wFANgBH,qCAAAA,oBCIhB,4BAA4C,CAC5C,cACA,eACA,QACA,CACA,CAAC,EAAC,IACF,cAkBA,OAfA,gBAGA,wBAIA,sBAIA,wBAGA,gBACA,CACA,CAAC,GAAG,4ZCpBJ,MAAe,OAAK,CAAC,EAAQ,UAAY,CAElC,CAFmC,CAEZ,OAAK,CAAC,EAAQ,kBACrC,EAAuB,OAAK,CAAC,EAAQ,kBADA,EAEV,OAAK,CAAC,EAAQ,kBADJ,IAErC,EAAe,OAAK,CAAC,EAAQ,UAC7B,EAAwB,EAFiB,CAEjB,IAAK,CAAC,EAAQ,mBAEtC,EAAgC,OAAK,CAAC,EAAQ,iBAFR,UAGtC,EAAgC,OAAK,CAAC,EAAQ,SADA,kBAE9C,EAAiC,OAAK,CAAC,EAAQ,SADD,mBAE9C,EAAgC,OAAK,CAAC,EAAQ,QADC,mBAE/C,EAAoC,OAAK,CAAC,EAAQ,SADJ,sBAG9C,MAAwB,WAF0B,OAEV,EAC/C,YACA,KAAc,GAAS,OACvB,eACA,mBAEA,cACA,WACA,CAAK,CACL,YAEA,IAAa,IACb,SAAkB,GAClB,CAAK,CACL,QAAY,EACZ,CAAC,iBC5BD,aDwBwB,QCxBxB,OAAwC,CACxC,EDwBkC,SCxBlC,GACA,eACA,OAGA,uBAEA,OACA,KAIA,sCACA,kBAIA,oCACA,QAIA,CAnBA,CACA,CAAC,EAAC,WCfF,mLCuMA,OAAmB,mBAAnB,GA1LgBI,WAAW,mBAAXA,gDAX4B,gBACzB,YACa,WACG,UACP,OAOrB,SAASA,EAAYC,CAAiB,EAAjBA,KAAAA,IAAAA,IAAAA,GAAY,GACtC,IAAMC,EAAO,CAAC,UAACC,OAAAA,CAAKC,QAAQ,SAAY,WAAa,CAMrD,OALI,GACFF,EAAKG,IAAI,CACP,UAACF,OAAAA,CAAKG,KAAK,WAAWC,QAAQ,sBAAyB,aAGpDL,CACT,CAEA,SAASM,EACPC,CAAoC,CACpCC,CAA2C,QAG3C,UAAI,OAAOA,GAAuC,UAAjB,OAAOA,EAC/BD,EAGLC,EAAMC,IAAI,GAAKnB,EAAAA,OAAK,CAACoB,QAAQ,CACxBH,CAD0B,CACrBI,MAAM,CAChB,EACArB,OAAK,CAAClB,QAAQ,CAACC,OAAO,CAACmC,EAAM1C,KAAK,CAACc,QAAQ,EAAEgC,MAAM,CACjD,CAEEC,EACAC,IAEA,UACE,OAAOA,GACkB,UAAzB,OAAOA,EAEAD,EAEFA,EAAaF,MAAM,CAACG,GAE7B,EAAE,GAIDP,EAAKI,MAAM,CAACH,EACrB,GA/CyB,OAiDzB,IAAMO,EAAY,CAAC,OAAQ,YAAa,UAAW,WAAW,CAsE9D,SAASC,EACPC,CAAoD,CACpDnD,CAAQ,EAER,GAAM,WAAEiC,CAAS,CAAE,CAAGjC,EACtB,OAAOmD,EACJL,MAAM,CAACN,EAAkB,EAAE,EAC3BY,OAAO,GACPP,MAAM,CAACb,EAAYC,GAAWmB,OAAO,IACrC1C,MAAM,CAAC2C,SAxEHA,EACP,IAAMC,EAAO,IAAIC,IACXC,EAAO,IAAID,IACXE,EAAY,IAAIF,IAChBG,EAAsD,CAAC,EAE7D,OAAO,IACL,IAAIC,GAAW,EACXC,GAAS,EAEb,GAAIC,EAAEC,GAAG,EAAqB,UAAjB,OAAOD,EAAEC,GAAG,EAAiBD,EAAEC,GAAG,CAACC,OAAO,CAAC,KAAO,EAAG,CAChEH,GAAS,EACT,IAAME,EAAMD,EAAEC,GAAG,CAACE,KAAK,CAACH,EAAEC,GAAG,CAACC,OAAO,CAAC,KAAO,GACzCT,EAAKW,GAAG,CAACH,GACXH,GADiB,EAGjBL,EAAKzC,GAAG,CAACiD,EAEb,CAGA,OAAQD,EAAElB,IAAI,EACZ,IAAK,QACL,IAAK,OACCa,EAAKS,GAAG,CAACJ,EAAElB,IAAI,EACjBgB,CADoB,CACT,GAEXH,EAAK3C,GAAG,CAACgD,EAAElB,IAAI,EAEjB,KACF,KAAK,OACH,IAAK,IAAIuB,EAAI,EAAGC,EAAMlB,EAAUmB,MAAM,CAAEF,EAAIC,EAAKD,IAAK,CACpD,IAAMG,EAAWpB,CAAS,CAACiB,EAAE,CAC7B,GAAKL,CAAD,CAAG7D,KAAK,CAACsE,cAAc,CAACD,GAE5B,GAAiB,KAFsB,MAEX,CAAxBA,EACEZ,EAAUQ,GAAG,CAACI,GAChBV,GAAW,EAEXF,EAAU5C,CAHiB,EAGd,CAACwD,OAEX,CACL,IAAME,EAAWV,EAAE7D,KAAK,CAACqE,EAAS,CAC5BG,EAAad,CAAc,CAACW,EAAS,EAAI,IAAId,GAC9Cc,EAAa,SAAbA,GAAuB,CAACT,CAAAA,CAAAA,CAAK,CAAMY,EAAWP,GAAG,CAACM,GACrDZ,GAAW,GAEXa,EAAW3D,GAAG,CAAC0D,GACfb,CAAc,CAACW,EAAS,CAAGG,EAE/B,CACF,CAEJ,CAEA,OAAOb,CACT,CACF,KAgBKP,OAAO,GACPqB,GAAG,CAAC,CAACC,EAA4BR,KAChC,IAAMJ,EAAMY,EAAEZ,GAAG,EAAII,EACrB,GAEES,CADAA,GAAoB,IACZC,GAAG,CAACC,qBAAqB,EACjC,CAAC5C,GAGY,QAFb,CAEEyC,EAAE/B,IAAI,EACN+B,EAAE1E,KAAK,CAAC,IAAO,EAEf,CAAC,CADD,kCACqC,2BAA2B,CAAC8E,IAAI,CAClEC,GAAQL,EAAE1E,KAAK,CAAC,IAAO,CAACgF,OAF+D,GAErD,CAACD,IAEtC,CACA,IAAME,EAAW,CAAE,GAAIP,EAAE1E,KAAK,EAAI,CAAC,CAAG,EAOtC,OANAiF,CAAQ,CAAC,YAAY,CAAGA,EAAS,IAAO,CACxCA,CADgC,CACvB,IAAO,EAAR,IAAWC,EAGnBD,CAAQ,CAAC,uBAAuB,EAAG,EAE5BzD,EAAAA,OAAK,CAAC2D,YAAY,CAACT,EAAGO,EAC/B,CAiBF,OAAOzD,EAAAA,OAAK,CAAC2D,YAAY,CAACT,EAAG,KAAEZ,CAAI,EACrC,EACJ,KAoBA,EAdA,SAAc,CAA2C,EAA3C,aAAEhD,CAAQ,CAAiC,CAA3C,EACNsE,EAAWC,CAAAA,EAAAA,EAAAA,UAAAA,EAAWC,EAAAA,eAAe,EACrCrF,EAAcoF,CAAAA,EAAAA,EAAAA,UAAAA,EAAWE,EAAAA,kBAAkB,EACjD,MACE,UAACC,EAAAA,OAAM,EACLtF,wBAAyBgD,EACzBjD,YAAaA,EACbgC,UAAWL,CAAAA,EAAAA,EAAAA,WAAAA,EAAYwD,YAEtBtE,GAGP,kWCrHqB2E,sCAhFH,gBACD,QAGXC,EAA0C,CAC9C,IAAK,cACL,IAAK,+BACL,IAAK,qBACL,IAAK,uBACP,EASA,SAASC,EAAiB,CAIR,EAJQ,IAQpBC,EARoB,KACxBC,CAAG,KACHC,CAAG,KACHC,CAAG,CACa,CAJQ,EAKlBC,EACJF,GAAOA,EAAIE,UAAU,CAAGF,EAAIE,UAAU,CAAGD,EAAMA,EAAIC,UAAU,CAAI,IAM5D,GAAIH,EAAK,CACd,GAAM,gBAAEI,CAAc,CAAE,CACtBC,EAAQ,KAAwB,EAE5BC,EAAUF,EAAeJ,EAAK,IAF3BK,OAGLC,IAEFP,EADY,GADD,CACKQ,IAAID,GACLP,QAAQ,CAE3B,CAEA,MAAO,YAAEI,EAAYJ,UAAS,CAChC,CAEA,IAAMS,EAA8C,CAClDC,MAAO,CAELC,WACE,8FACFC,OAAQ,QACRC,UAAW,SACXC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,QAClB,EACAC,KAAM,CACJC,WAAY,MACd,EACAC,GAAI,CACFN,QAAS,eACTO,OAAQ,aACRC,aAAc,GACdC,SAAU,GACVC,WAAY,IACZC,cAAe,KACjB,EACAC,GAAI,CACFH,SAAU,GACVC,WAAY,IACZL,WAAY,MACd,EACAQ,KAAM,CACJb,QAAS,cACX,CACF,CAKe,OAAMjB,UAAsBjE,EAAAA,OAAK,CAACJ,SAAS,CAMxDK,QAAS,CACP,GAAM,YAAEuE,CAAU,cAAEwB,GAAe,CAAI,CAAE,CAAG,IAAI,CAACxH,KAAK,CAChDyH,EACJ,IAAI,CAACzH,KAAK,CAACyH,KAAK,EAChB/B,CAAW,CAACM,EAAW,EACvB,mCAEF,MACE,WAAC0B,MAAAA,CAAIC,MAAOtB,EAAOC,KAAK,WACtB,UAACsB,EAAAA,OAAI,WACH,UAACH,QAAAA,UACEzB,EACMA,EAAW,KAAIyB,EAClB,8DAGR,WAACC,MAAAA,CAAIC,MAAOtB,EAAOS,IAAI,WACrB,UAACa,QAAAA,CACCE,wBAAyB,CAkBvBC,OAAS,kGACPN,CAAAA,CACI,kIACA,GAER,CAFS,GAKVxB,EACC,UAACgB,CADFhB,IACEgB,CAAGe,MADL/B,IACe,gBAAgB2B,MAAOtB,EAAOW,EAAE,UAC3ChB,IAED,KACJ,UAAC0B,MAAAA,CAAIC,MAAOtB,EAAOkB,IAAI,UACrB,WAACD,KAAAA,CAAGK,MAAOtB,EAAOiB,EAAE,WACjB,IAAI,CAACtH,KAAK,CAACyH,KAAK,EAAIzB,EACnByB,EAEA,iBAFAA,IAEA,YAAE,0DACwD,KACvD9G,CAAQ,IAAI,CAACX,KAAK,CAAC4F,QAAQ,EAC1B,cAD0B,OAC1B,YAAE,iBAAe,IAAI,CAAC5F,KAAK,CAAC4F,QAAQ,IACnC,IAAI,oDAGT,cAOd,CACF,CA3EqBH,EACZuC,WAAAA,CAAc,YADFvC,EAGZ9D,eAAAA,CAAkBgE,EAHNF,EAIZ/D,mBAAAA,CAAsBiE", "sources": ["webpack://platyfend/./node_modules/next/dist/server/route-modules/pages/vendored/contexts/head-manager-context.js", "webpack://platyfend/./node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs", "webpack://platyfend/external commonjs \"react/jsx-runtime\"", "webpack://platyfend/../../../src/shared/lib/side-effect.tsx", "webpack://platyfend/../../../../src/shared/lib/utils/warn-once.ts", "webpack://platyfend/external commonjs2 \"path\"", "webpack://platyfend/./node_modules/next/dist/server/request-meta.js", "webpack://platyfend/../../src/pages/_app.tsx", "webpack://platyfend/external commonjs \"next/dist/compiled/next-server/pages.runtime.prod.js\"", "webpack://platyfend/external commonjs \"@opentelemetry/api\"", "webpack://platyfend/./node_modules/next/dist/server/route-modules/pages/vendored/contexts/amp-context.js", "webpack://platyfend/../../../src/shared/lib/amp-mode.ts", "webpack://platyfend/./node_modules/next/dist/server/route-kind.js", "webpack://platyfend/?990d", "webpack://platyfend/./node_modules/next/dist/build/templates/helpers.js", "webpack://platyfend/external commonjs \"react\"", "webpack://platyfend/../../../src/shared/lib/head.tsx", "webpack://platyfend/../../src/pages/_error.tsx"], "sourcesContent": ["\"use strict\";\nmodule.exports = require('../../module.compiled').vendored['contexts'].HeadManagerContext;\n\n//# sourceMappingURL=head-manager-context.js.map", "\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n", "module.exports = require(\"react/jsx-runtime\");", "import type React from 'react'\nimport { Children, useEffect, useLayoutEffect, type JSX } from 'react'\n\ntype State = JSX.Element[] | undefined\n\nexport type SideEffectProps = {\n  reduceComponentsToState: <T extends {}>(\n    components: Array<React.ReactElement<any>>,\n    props: T\n  ) => State\n  handleStateChange?: (state: State) => void\n  headManager: any\n  inAmpMode?: boolean\n  children: React.ReactNode\n}\n\nconst isServer = typeof window === 'undefined'\nconst useClientOnlyLayoutEffect = isServer ? () => {} : useLayoutEffect\nconst useClientOnlyEffect = isServer ? () => {} : useEffect\n\nexport default function SideEffect(props: SideEffectProps) {\n  const { headManager, reduceComponentsToState } = props\n\n  function emitChange() {\n    if (headManager && headManager.mountedInstances) {\n      const headElements = Children.toArray(\n        Array.from(headManager.mountedInstances as Set<React.ReactNode>).filter(\n          Boolean\n        )\n      ) as React.ReactElement[]\n      headManager.updateHead(reduceComponentsToState(headElements, props))\n    }\n  }\n\n  if (isServer) {\n    headManager?.mountedInstances?.add(props.children)\n    emitChange()\n  }\n\n  useClientOnlyLayoutEffect(() => {\n    headManager?.mountedInstances?.add(props.children)\n    return () => {\n      headManager?.mountedInstances?.delete(props.children)\n    }\n  })\n\n  // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n  // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n  // being rendered, we only trigger the method from the last one.\n  // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n  // singleton in the layout effect pass, and actually trigger it in the effect pass.\n  useClientOnlyLayoutEffect(() => {\n    if (headManager) {\n      headManager._pendingUpdate = emitChange\n    }\n    return () => {\n      if (headManager) {\n        headManager._pendingUpdate = emitChange\n      }\n    }\n  })\n\n  useClientOnlyEffect(() => {\n    if (headManager && headManager._pendingUpdate) {\n      headManager._pendingUpdate()\n      headManager._pendingUpdate = null\n    }\n    return () => {\n      if (headManager && headManager._pendingUpdate) {\n        headManager._pendingUpdate()\n        headManager._pendingUpdate = null\n      }\n    }\n  })\n\n  return null\n}\n", "let warnOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const warnings = new Set<string>()\n  warnOnce = (msg: string) => {\n    if (!warnings.has(msg)) {\n      console.warn(msg)\n    }\n    warnings.add(msg)\n  }\n}\n\nexport { warnOnce }\n", "module.exports = require(\"path\");", "/* eslint-disable no-redeclare */ \"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\n0 && (module.exports = {\n    NEXT_REQUEST_META: null,\n    addRequestMeta: null,\n    getRequestMeta: null,\n    removeRequestMeta: null,\n    setRequestMeta: null\n});\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    NEXT_REQUEST_META: function() {\n        return NEXT_REQUEST_META;\n    },\n    addRequestMeta: function() {\n        return addRequestMeta;\n    },\n    getRequestMeta: function() {\n        return getRequestMeta;\n    },\n    removeRequestMeta: function() {\n        return removeRequestMeta;\n    },\n    setRequestMeta: function() {\n        return setRequestMeta;\n    }\n});\nconst NEXT_REQUEST_META = Symbol.for('NextInternalRequestMeta');\nfunction getRequestMeta(req, key) {\n    const meta = req[NEXT_REQUEST_META] || {};\n    return typeof key === 'string' ? meta[key] : meta;\n}\nfunction setRequestMeta(req, meta) {\n    req[NEXT_REQUEST_META] = meta;\n    return meta;\n}\nfunction addRequestMeta(request, key, value) {\n    const meta = getRequestMeta(request);\n    meta[key] = value;\n    return setRequestMeta(request, meta);\n}\nfunction removeRequestMeta(request, key) {\n    const meta = getRequestMeta(request);\n    delete meta[key];\n    return setRequestMeta(request, meta);\n}\n\n//# sourceMappingURL=request-meta.js.map", "import React from 'react'\n\nimport type {\n  AppContextType,\n  AppInitialProps,\n  AppPropsType,\n  NextWebVitalsMetric,\n  AppType,\n} from '../shared/lib/utils'\nimport type { Router } from '../client/router'\n\nimport { loadGetInitialProps } from '../shared/lib/utils'\n\nexport type { AppInitialProps, AppType }\n\nexport type { NextWebVitalsMetric }\n\nexport type AppContext = AppContextType<Router>\n\nexport type AppProps<P = any> = AppPropsType<Router, P>\n\n/**\n * `App` component is used for initialize of pages. It allows for overwriting and full control of the `page` initialization.\n * This allows for keeping state between navigation, custom error handling, injecting additional data.\n */\nasync function appGetInitialProps({\n  Component,\n  ctx,\n}: AppContext): Promise<AppInitialProps> {\n  const pageProps = await loadGetInitialProps(Component, ctx)\n  return { pageProps }\n}\n\nexport default class App<P = any, CP = {}, S = {}> extends React.Component<\n  P & AppProps<CP>,\n  S\n> {\n  static origGetInitialProps = appGetInitialProps\n  static getInitialProps = appGetInitialProps\n\n  render() {\n    const { Component, pageProps } = this.props as AppProps<CP>\n\n    return <Component {...pageProps} />\n  }\n}\n", "module.exports = require(\"next/dist/compiled/next-server/pages.runtime.prod.js\");", "module.exports = require(\"@opentelemetry/api\");", "\"use strict\";\nmodule.exports = require('../../module.compiled').vendored['contexts'].AmpContext;\n\n//# sourceMappingURL=amp-context.js.map", "export function isInAmpMode({\n  ampFirst = false,\n  hybrid = false,\n  hasQuery = false,\n} = {}): boolean {\n  return ampFirst || (hybrid && hasQuery)\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"RouteKind\", {\n    enumerable: true,\n    get: function() {\n        return RouteKind;\n    }\n});\nvar RouteKind = /*#__PURE__*/ function(RouteKind) {\n    /**\n   * `PAGES` represents all the React pages that are under `pages/`.\n   */ RouteKind[\"PAGES\"] = \"PAGES\";\n    /**\n   * `PAGES_API` represents all the API routes under `pages/api/`.\n   */ RouteKind[\"PAGES_API\"] = \"PAGES_API\";\n    /**\n   * `APP_PAGE` represents all the React pages that are under `app/` with the\n   * filename of `page.{j,t}s{,x}`.\n   */ RouteKind[\"APP_PAGE\"] = \"APP_PAGE\";\n    /**\n   * `APP_ROUTE` represents all the API routes and metadata routes that are under `app/` with the\n   * filename of `route.{j,t}s{,x}`.\n   */ RouteKind[\"APP_ROUTE\"] = \"APP_ROUTE\";\n    /**\n   * `IMAGE` represents all the images that are generated by `next/image`.\n   */ RouteKind[\"IMAGE\"] = \"IMAGE\";\n    return RouteKind;\n}({});\n\n//# sourceMappingURL=route-kind.js.map", "import { PagesRouteModule } from \"next/dist/server/route-modules/pages/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { hoist } from \"next/dist/build/templates/helpers\";\n// Import the app and document modules.\nimport * as document from \"next/dist/pages/_document\";\nimport * as app from \"next/dist/pages/_app\";\n// Import the userland code.\nimport * as userland from \"next/dist/pages/_error\";\n// Re-export the component (should be the default export).\nexport default hoist(userland, 'default');\n// Re-export methods.\nexport const getStaticProps = hoist(userland, 'getStaticProps');\nexport const getStaticPaths = hoist(userland, 'getStaticPaths');\nexport const getServerSideProps = hoist(userland, 'getServerSideProps');\nexport const config = hoist(userland, 'config');\nexport const reportWebVitals = hoist(userland, 'reportWebVitals');\n// Re-export legacy methods.\nexport const unstable_getStaticProps = hoist(userland, 'unstable_getStaticProps');\nexport const unstable_getStaticPaths = hoist(userland, 'unstable_getStaticPaths');\nexport const unstable_getStaticParams = hoist(userland, 'unstable_getStaticParams');\nexport const unstable_getServerProps = hoist(userland, 'unstable_getServerProps');\nexport const unstable_getServerSideProps = hoist(userland, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nexport const routeModule = new PagesRouteModule({\n    definition: {\n        kind: RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: app.default,\n        Document: document.default\n    },\n    userland\n});\n\n//# sourceMappingURL=pages.js.map", "/**\n * Hoists a name from a module or promised module.\n *\n * @param module the module to hoist the name from\n * @param name the name to hoist\n * @returns the value on the module (or promised module)\n */ \"use strict\";\nObject.defineProperty(exports, \"__esModule\", {\n    value: true\n});\nObject.defineProperty(exports, \"hoist\", {\n    enumerable: true,\n    get: function() {\n        return hoist;\n    }\n});\nfunction hoist(module, name) {\n    // If the name is available in the module, return it.\n    if (name in module) {\n        return module[name];\n    }\n    // If a property called `then` exists, assume it's a promise and\n    // return a promise that resolves to the name.\n    if ('then' in module && typeof module.then === 'function') {\n        return module.then((mod)=>hoist(mod, name));\n    }\n    // If we're trying to hoise the default export, and the module is a function,\n    // return the module itself.\n    if (typeof module === 'function' && name === 'default') {\n        return module;\n    }\n    // Otherwise, return undefined.\n    return undefined;\n}\n\n//# sourceMappingURL=helpers.js.map", "module.exports = require(\"react\");", "'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport Effect from './side-effect'\nimport { AmpStateContext } from './amp-context.shared-runtime'\nimport { HeadManagerContext } from './head-manager-context.shared-runtime'\nimport { isInAmpMode } from './amp-mode'\nimport { warnOnce } from './utils/warn-once'\n\ntype WithInAmpMode = {\n  inAmpMode?: boolean\n}\n\nexport function defaultHead(inAmpMode = false): JSX.Element[] {\n  const head = [<meta charSet=\"utf-8\" key=\"charset\" />]\n  if (!inAmpMode) {\n    head.push(\n      <meta name=\"viewport\" content=\"width=device-width\" key=\"viewport\" />\n    )\n  }\n  return head\n}\n\nfunction onlyReactElement(\n  list: Array<React.ReactElement<any>>,\n  child: React.ReactElement | number | string\n): Array<React.ReactElement<any>> {\n  // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n  if (typeof child === 'string' || typeof child === 'number') {\n    return list\n  }\n  // Adds support for React.Fragment\n  if (child.type === React.Fragment) {\n    return list.concat(\n      // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n      React.Children.toArray(child.props.children).reduce(\n        // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n        (\n          fragmentList: Array<React.ReactElement<any>>,\n          fragmentChild: React.ReactElement | number | string\n        ): Array<React.ReactElement<any>> => {\n          if (\n            typeof fragmentChild === 'string' ||\n            typeof fragmentChild === 'number'\n          ) {\n            return fragmentList\n          }\n          return fragmentList.concat(fragmentChild)\n        },\n        []\n      )\n    )\n  }\n  return list.concat(child)\n}\n\nconst METATYPES = ['name', 'httpEquiv', 'charSet', 'itemProp']\n\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/\nfunction unique() {\n  const keys = new Set()\n  const tags = new Set()\n  const metaTypes = new Set()\n  const metaCategories: { [metatype: string]: Set<string> } = {}\n\n  return (h: React.ReactElement<any>) => {\n    let isUnique = true\n    let hasKey = false\n\n    if (h.key && typeof h.key !== 'number' && h.key.indexOf('$') > 0) {\n      hasKey = true\n      const key = h.key.slice(h.key.indexOf('$') + 1)\n      if (keys.has(key)) {\n        isUnique = false\n      } else {\n        keys.add(key)\n      }\n    }\n\n    // eslint-disable-next-line default-case\n    switch (h.type) {\n      case 'title':\n      case 'base':\n        if (tags.has(h.type)) {\n          isUnique = false\n        } else {\n          tags.add(h.type)\n        }\n        break\n      case 'meta':\n        for (let i = 0, len = METATYPES.length; i < len; i++) {\n          const metatype = METATYPES[i]\n          if (!h.props.hasOwnProperty(metatype)) continue\n\n          if (metatype === 'charSet') {\n            if (metaTypes.has(metatype)) {\n              isUnique = false\n            } else {\n              metaTypes.add(metatype)\n            }\n          } else {\n            const category = h.props[metatype]\n            const categories = metaCategories[metatype] || new Set()\n            if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n              isUnique = false\n            } else {\n              categories.add(category)\n              metaCategories[metatype] = categories\n            }\n          }\n        }\n        break\n    }\n\n    return isUnique\n  }\n}\n\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */\nfunction reduceComponents<T extends {} & WithInAmpMode>(\n  headChildrenElements: Array<React.ReactElement<any>>,\n  props: T\n) {\n  const { inAmpMode } = props\n  return headChildrenElements\n    .reduce(onlyReactElement, [])\n    .reverse()\n    .concat(defaultHead(inAmpMode).reverse())\n    .filter(unique())\n    .reverse()\n    .map((c: React.ReactElement<any>, i: number) => {\n      const key = c.key || i\n      if (\n        process.env.NODE_ENV !== 'development' &&\n        process.env.__NEXT_OPTIMIZE_FONTS &&\n        !inAmpMode\n      ) {\n        if (\n          c.type === 'link' &&\n          c.props['href'] &&\n          // TODO(prateekbh@): Replace this with const from `constants` when the tree shaking works.\n          ['https://fonts.googleapis.com/css', 'https://use.typekit.net/'].some(\n            (url) => c.props['href'].startsWith(url)\n          )\n        ) {\n          const newProps = { ...(c.props || {}) }\n          newProps['data-href'] = newProps['href']\n          newProps['href'] = undefined\n\n          // Add this attribute to make it easy to identify optimized tags\n          newProps['data-optimized-fonts'] = true\n\n          return React.cloneElement(c, newProps)\n        }\n      }\n      if (process.env.NODE_ENV === 'development') {\n        // omit JSON-LD structured data snippets from the warning\n        if (c.type === 'script' && c.props['type'] !== 'application/ld+json') {\n          const srcMessage = c.props['src']\n            ? `<script> tag with src=\"${c.props['src']}\"`\n            : `inline <script>`\n          warnOnce(\n            `Do not add <script> tags using next/head (see ${srcMessage}). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component`\n          )\n        } else if (c.type === 'link' && c.props['rel'] === 'stylesheet') {\n          warnOnce(\n            `Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"${c.props['href']}\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component`\n          )\n        }\n      }\n      return React.cloneElement(c, { key })\n    })\n}\n\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */\nfunction Head({ children }: { children: React.ReactNode }) {\n  const ampState = useContext(AmpStateContext)\n  const headManager = useContext(HeadManagerContext)\n  return (\n    <Effect\n      reduceComponentsToState={reduceComponents}\n      headManager={headManager}\n      inAmpMode={isInAmpMode(ampState)}\n    >\n      {children}\n    </Effect>\n  )\n}\n\nexport default Head\n", "import React from 'react'\nimport Head from '../shared/lib/head'\nimport type { NextPageContext } from '../shared/lib/utils'\n\nconst statusCodes: { [code: number]: string } = {\n  400: 'Bad Request',\n  404: 'This page could not be found',\n  405: 'Method Not Allowed',\n  500: 'Internal Server Error',\n}\n\nexport type ErrorProps = {\n  statusCode: number\n  hostname?: string\n  title?: string\n  withDarkMode?: boolean\n}\n\nfunction _getInitialProps({\n  req,\n  res,\n  err,\n}: NextPageContext): Promise<ErrorProps> | ErrorProps {\n  const statusCode =\n    res && res.statusCode ? res.statusCode : err ? err.statusCode! : 404\n\n  let hostname\n\n  if (typeof window !== 'undefined') {\n    hostname = window.location.hostname\n  } else if (req) {\n    const { getRequestMeta } =\n      require('../server/request-meta') as typeof import('../server/request-meta')\n\n    const initUrl = getRequestMeta(req, 'initURL')\n    if (initUrl) {\n      const url = new URL(initUrl)\n      hostname = url.hostname\n    }\n  }\n\n  return { statusCode, hostname }\n}\n\nconst styles: Record<string, React.CSSProperties> = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  desc: {\n    lineHeight: '48px',\n  },\n  h1: {\n    display: 'inline-block',\n    margin: '0 20px 0 0',\n    paddingRight: 23,\n    fontSize: 24,\n    fontWeight: 500,\n    verticalAlign: 'top',\n  },\n  h2: {\n    fontSize: 14,\n    fontWeight: 400,\n    lineHeight: '28px',\n  },\n  wrap: {\n    display: 'inline-block',\n  },\n}\n\n/**\n * `Error` component used for handling errors.\n */\nexport default class Error<P = {}> extends React.Component<P & ErrorProps> {\n  static displayName = 'ErrorPage'\n\n  static getInitialProps = _getInitialProps\n  static origGetInitialProps = _getInitialProps\n\n  render() {\n    const { statusCode, withDarkMode = true } = this.props\n    const title =\n      this.props.title ||\n      statusCodes[statusCode] ||\n      'An unexpected error has occurred'\n\n    return (\n      <div style={styles.error}>\n        <Head>\n          <title>\n            {statusCode\n              ? `${statusCode}: ${title}`\n              : 'Application error: a client-side exception has occurred'}\n          </title>\n        </Head>\n        <div style={styles.desc}>\n          <style\n            dangerouslySetInnerHTML={{\n              /* CSS minified from\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                ${\n                  withDarkMode\n                    ? `@media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }`\n                    : ''\n                }\n               */\n              __html: `body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}${\n                withDarkMode\n                  ? '@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}'\n                  : ''\n              }`,\n            }}\n          />\n\n          {statusCode ? (\n            <h1 className=\"next-error-h1\" style={styles.h1}>\n              {statusCode}\n            </h1>\n          ) : null}\n          <div style={styles.wrap}>\n            <h2 style={styles.h2}>\n              {this.props.title || statusCode ? (\n                title\n              ) : (\n                <>\n                  Application error: a client-side exception has occurred{' '}\n                  {Boolean(this.props.hostname) && (\n                    <>while loading {this.props.hostname}</>\n                  )}{' '}\n                  (see the browser console for more information)\n                </>\n              )}\n              .\n            </h2>\n          </div>\n        </div>\n      </div>\n    )\n  }\n}\n"], "names": ["SideEffect", "useClientOnlyLayoutEffect", "useClientOnlyEffect", "useEffect", "isServer", "props", "headManager", "reduceComponentsToState", "emitChange", "mountedInstances", "headElements", "Children", "toArray", "Array", "from", "filter", "Boolean", "updateHead", "add", "children", "delete", "_pendingUpdate", "warnOnce", "App", "appGetInitialProps", "Component", "ctx", "pageProps", "loadGetInitialProps", "React", "render", "origGetInitialProps", "getInitialProps", "isInAmpMode", "ampFirs<PERSON>", "hybrid", "<PERSON><PERSON><PERSON><PERSON>", "defaultHead", "inAmpMode", "head", "meta", "charSet", "push", "name", "content", "onlyReactElement", "list", "child", "type", "Fragment", "concat", "reduce", "fragmentList", "fragmentChild", "METATYPES", "reduceComponents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "unique", "keys", "Set", "tags", "metaTypes", "metaCategories", "isUnique", "<PERSON><PERSON><PERSON>", "h", "key", "indexOf", "slice", "has", "i", "len", "length", "metatype", "hasOwnProperty", "category", "categories", "map", "c", "process", "env", "__NEXT_OPTIMIZE_FONTS", "some", "url", "startsWith", "newProps", "undefined", "cloneElement", "ampState", "useContext", "AmpStateContext", "HeadManagerContext", "Effect", "Error", "statusCodes", "_getInitialProps", "hostname", "req", "res", "err", "statusCode", "getRequestMeta", "require", "initUrl", "URL", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "desc", "lineHeight", "h1", "margin", "paddingRight", "fontSize", "fontWeight", "verticalAlign", "h2", "wrap", "withDarkMode", "title", "div", "style", "Head", "dangerouslySetInnerHTML", "__html", "className", "displayName"], "sourceRoot": ""}