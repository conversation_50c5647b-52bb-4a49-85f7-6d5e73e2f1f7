try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="f9c00a46-29e8-49a5-a073-8e64122dcd0a",e._sentryDebugIdIdentifier="sentry-dbid-f9c00a46-29e8-49a5-a073-8e64122dcd0a")}catch(e){}"use strict";(()=>{var e={};e.id=220,e.ids=[220],e.modules={8732:e=>{e.exports=require("react/jsx-runtime")},33873:e=>{e.exports=require("path")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},56472:e=>{e.exports=require("@opentelemetry/api")},82015:e=>{e.exports=require("react")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[632],()=>t(38632));module.exports=s})();
//# sourceMappingURL=_document.js.map