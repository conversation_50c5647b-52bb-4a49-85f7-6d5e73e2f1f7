{"version": 3, "file": "../pages/_document.js", "mappings": "qbAAA,kDCAA,qCCAA,qFCAA,mDCAA", "sources": ["webpack://platyfend/external commonjs \"react/jsx-runtime\"", "webpack://platyfend/external commonjs2 \"path\"", "webpack://platyfend/external commonjs \"next/dist/compiled/next-server/pages.runtime.prod.js\"", "webpack://platyfend/external commonjs \"@opentelemetry/api\"", "webpack://platyfend/external commonjs \"react\""], "sourcesContent": ["module.exports = require(\"react/jsx-runtime\");", "module.exports = require(\"path\");", "module.exports = require(\"next/dist/compiled/next-server/pages.runtime.prod.js\");", "module.exports = require(\"@opentelemetry/api\");", "module.exports = require(\"react\");"], "names": [], "sourceRoot": ""}