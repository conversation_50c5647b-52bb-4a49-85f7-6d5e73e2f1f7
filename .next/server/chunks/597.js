try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="d99ccf7d-452d-42b4-8440-c1cbe325d251",e._sentryDebugIdIdentifier="sentry-dbid-d99ccf7d-452d-42b4-8440-c1cbe325d251")}catch(e){}exports.id=597,exports.ids=[597],exports.modules={5698:(e,t,o)=>{"use strict";o.d(t,{A:()=>d,AuthProvider:()=>a});var r=o(49431),n=o(27490),s=o(72229);let i=(0,n.createContext)(void 0);function a({children:e}){let[t,o]=(0,n.useState)(null),[a,d]=(0,n.useState)(null),[l,u]=(0,n.useState)(!0);return(0,r.jsx)(i.Provider,{value:{user:t,token:a,isLoading:l,isAuthenticated:!!a&&!!t,logout:()=>{(0,s.S3)(),d(null),o(null)}},children:e})}function d(){let e=(0,n.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},5849:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=5849,e.exports=t},6820:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=6820,e.exports=t},6989:(e,t,o)=>{Promise.resolve().then(o.bind(o,94531)),Promise.resolve().then(o.bind(o,46344)),Promise.resolve().then(o.bind(o,36782)),Promise.resolve().then(o.bind(o,38989)),Promise.resolve().then(o.bind(o,17392))},10352:(e,t,o)=>{"use strict";o.d(t,{Toaster:()=>i});var r=o(49431),n=o(44670),s=o(39841);let i=({...e})=>{let{theme:t="system"}=(0,n.D)();return(0,r.jsx)(s.l$,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})}},17392:(e,t,o)=>{"use strict";o.d(t,{AuthProvider:()=>n});var r=o(78367);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/lib/auth/auth-context.tsx","AuthProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/lib/auth/auth-context.tsx","useAuth")},19082:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=19082,e.exports=t},21490:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=21490,e.exports=t},22943:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=22943,e.exports=t},24860:(e,t,o)=>{"use strict";let r;o.r(t),o.d(t,{default:()=>P,generateImageMetadata:()=>C,generateMetadata:()=>_,generateViewport:()=>U,metadata:()=>w,viewport:()=>E});var n=o(63033),s=o(69929),i=o(38989),a=o(36782),d=o(58988),l=o(94531),u=o(61146),c=o(92806);let f=l.Provider;l.Root,l.Trigger,d.forwardRef(({className:e,sideOffset:t=4,...o},r)=>(0,s.jsx)(l.Content,{ref:r,sideOffset:t,className:function(...e){return(0,c.QP)((0,u.$)(e))}("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...o})).displayName=l.Content.displayName;var m=o(17392),p=o(46344),v=o(55658),h=o.n(v),b=o(6280),g=o.n(b),x=o(95832),y=o.n(x);let O=[h().variable,g().variable,y().variable].join(" ");o(61135);var N=o(68575);let w={title:"Platyfend - AI-Powered Code Review Platform",description:"Next generation secure code review agents with vulnerability detection, GitHub integration, and automated security analysis."},E={width:"device-width",initialScale:1,maximumScale:5,userScalable:!0},D={...n},T="workUnitAsyncStorage"in D?D.workUnitAsyncStorage:"requestAsyncStorage"in D?D.requestAsyncStorage:void 0;r=new Proxy(function({children:e}){return(0,s.jsxs)("html",{lang:"en",className:O,suppressHydrationWarning:!0,children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes"}),(0,s.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,s.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"})]}),(0,s.jsx)("body",{className:h().className,children:(0,s.jsx)(m.AuthProvider,{children:(0,s.jsx)(p.ThemeProvider,{attribute:"class",defaultTheme:"light",enableSystem:!1,disableTransitionOnChange:!0,children:(0,s.jsxs)(f,{children:[(0,s.jsx)(i.Toaster,{}),(0,s.jsx)(a.Toaster,{}),e]})})})})]})},{apply:(e,t,o)=>{let r,n,s;try{let e=T?.getStore();r=e?.headers.get("sentry-trace")??void 0,n=e?.headers.get("baggage")??void 0,s=e?.headers}catch(e){}return N.wrapServerComponentWithSentry(e,{componentRoute:"/",componentType:"Layout",sentryTraceHeader:r,baggageHeader:n,headers:s}).apply(t,o)}});let _=void 0,C=void 0,U=void 0,P=r},27934:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=27934,e.exports=t},29903:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=29903,e.exports=t},30854:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=30854,e.exports=t},36782:(e,t,o)=>{"use strict";o.d(t,{Toaster:()=>n});var r=o(78367);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/components/ui/sonner.tsx","Toaster");(0,r.registerClientReference)(function(){throw Error("Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/components/ui/sonner.tsx","toast")},38989:(e,t,o)=>{"use strict";o.d(t,{Toaster:()=>r});let r=(0,o(78367).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/components/ui/toaster.tsx","Toaster")},44725:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=44725,e.exports=t},44994:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=44994,e.exports=t},47712:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=47712,e.exports=t},53320:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=53320,e.exports=t},54253:(e,t,o)=>{Promise.resolve().then(o.bind(o,86153)),Promise.resolve().then(o.bind(o,44670)),Promise.resolve().then(o.bind(o,10352)),Promise.resolve().then(o.bind(o,75996)),Promise.resolve().then(o.bind(o,5698))},55611:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=55611,e.exports=t},59223:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=59223,e.exports=t},61135:()=>{},62207:(e,t,o)=>{"use strict";o.d(t,{cn:()=>s});var r=o(27044),n=o(84320);function s(...e){return(0,n.QP)((0,r.$)(e))}},62747:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=62747,e.exports=t},63625:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=63625,e.exports=t},67503:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=67503,e.exports=t},69456:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=69456,e.exports=t},70130:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=70130,e.exports=t},72229:(e,t,o)=>{"use strict";function r(){return null}function n(e){}function s(){}function i(){let e=r();return e?{Authorization:`Bearer ${e}`}:{}}o.d(t,{Pt:()=>r,S3:()=>s,do:()=>n,z5:()=>i})},75996:(e,t,o)=>{"use strict";o.d(t,{Toaster:()=>E});var r=o(49431),n=o(27490);let s=0,i=new Map,a=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),c({type:"REMOVE_TOAST",toastId:e})},5e3);i.set(e,t)},d=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:o}=t;return o?a(o):e.toasts.forEach(e=>{a(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===o||void 0===o?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],u={toasts:[]};function c(e){u=d(u,e),l.forEach(e=>{e(u)})}function f({...e}){let t=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),o=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||o()}}}),{id:t,dismiss:o,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}var m=o(89733),p=o(55868),v=o(97036),h=o(62207);let b=m.Kq,g=n.forwardRef(({className:e,...t},o)=>(0,r.jsx)(m.LM,{ref:o,className:(0,h.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));g.displayName=m.LM.displayName;let x=(0,p.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),y=n.forwardRef(({className:e,variant:t,...o},n)=>(0,r.jsx)(m.bL,{ref:n,className:(0,h.cn)(x({variant:t}),e),...o}));y.displayName=m.bL.displayName,n.forwardRef(({className:e,...t},o)=>(0,r.jsx)(m.rc,{ref:o,className:(0,h.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=m.rc.displayName;let O=n.forwardRef(({className:e,...t},o)=>(0,r.jsx)(m.bm,{ref:o,className:(0,h.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,r.jsx)(v.A,{className:"h-4 w-4"})}));O.displayName=m.bm.displayName;let N=n.forwardRef(({className:e,...t},o)=>(0,r.jsx)(m.hE,{ref:o,className:(0,h.cn)("text-sm font-semibold",e),...t}));N.displayName=m.hE.displayName;let w=n.forwardRef(({className:e,...t},o)=>(0,r.jsx)(m.VY,{ref:o,className:(0,h.cn)("text-sm opacity-90",e),...t}));function E(){let{toasts:e}=function(){let[e,t]=n.useState(u);return n.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[]),{...e,toast:f,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}();return(0,r.jsxs)(b,{children:[e.map(function({id:e,title:t,description:o,action:n,...s}){return(0,r.jsxs)(y,{...s,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[t&&(0,r.jsx)(N,{children:t}),o&&(0,r.jsx)(w,{children:o})]}),n,(0,r.jsx)(O,{})]},e)}),(0,r.jsx)(g,{})]})}w.displayName=m.VY.displayName},77288:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,53122,23)),Promise.resolve().then(o.t.bind(o,10428,23)),Promise.resolve().then(o.t.bind(o,97440,23)),Promise.resolve().then(o.t.bind(o,76827,23)),Promise.resolve().then(o.t.bind(o,86507,23)),Promise.resolve().then(o.t.bind(o,97651,23)),Promise.resolve().then(o.t.bind(o,39779,23)),Promise.resolve().then(o.t.bind(o,54957,23))},84853:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=84853,e.exports=t},86174:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=86174,e.exports=t},90440:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,50888,23)),Promise.resolve().then(o.t.bind(o,6270,23)),Promise.resolve().then(o.t.bind(o,43230,23)),Promise.resolve().then(o.t.bind(o,60937,23)),Promise.resolve().then(o.t.bind(o,79149,23)),Promise.resolve().then(o.t.bind(o,97269,23)),Promise.resolve().then(o.t.bind(o,26845,23)),Promise.resolve().then(o.t.bind(o,55471,23))},96868:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=96868,e.exports=t},96916:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=96916,e.exports=t},98629:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=98629,e.exports=t}};
//# sourceMappingURL=597.js.map