try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},a=(new e.Error).stack;a&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[a]="03a3673f-a26e-43de-8cc2-1051eeb7b55d",e._sentryDebugIdIdentifier="sentry-dbid-03a3673f-a26e-43de-8cc2-1051eeb7b55d")}catch(e){}exports.id=542,exports.ids=[542],exports.modules={6186:(e,a,t)=>{"use strict";t.d(a,{DashboardLayout:()=>r});let r=(0,t(78367).registerClientReference)(function(){throw Error("Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/components/dashboard/dashboard-layout.tsx","DashboardLayout")},15598:(e,a,t)=>{"use strict";let r;t.r(a),t.d(a,{default:()=>m,generateImageMetadata:()=>f,generateMetadata:()=>c,generateViewport:()=>u});var s=t(63033),i=t(69929),d=t(6186),n=t(68575);let o={...s},l="workUnitAsyncStorage"in o?o.workUnitAsyncStorage:"requestAsyncStorage"in o?o.requestAsyncStorage:void 0;r=new Proxy(function({children:e}){return(0,i.jsx)(d.DashboardLayout,{children:e})},{apply:(e,a,t)=>{let r,s,i;try{let e=l?.getStore();r=e?.headers.get("sentry-trace")??void 0,s=e?.headers.get("baggage")??void 0,i=e?.headers}catch(e){}return n.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard",componentType:"Layout",sentryTraceHeader:r,baggageHeader:s,headers:i}).apply(a,t)}});let c=void 0,f=void 0,u=void 0,m=r},85997:(e,a,t)=>{Promise.resolve().then(t.bind(t,96527))},88205:(e,a,t)=>{Promise.resolve().then(t.bind(t,6186))},93803:(e,a,t)=>{"use strict";t.d(a,{$:()=>l});var r=t(49431),s=t(27490),i=t(73286),d=t(55868),n=t(62207);let o=(0,d.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",platyfend:"bg-platyfend-500 hover:bg-platyfend-600 shadow-sm"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef(({className:e,variant:a,size:t,asChild:s=!1,...d},l)=>{let c=s?i.DX:"button";return(0,r.jsx)(c,{className:(0,n.cn)(o({variant:a,size:t,className:e})),ref:l,...d})});l.displayName="Button"},96527:(e,a,t)=>{"use strict";t.d(a,{DashboardLayout:()=>eN});var r=t(49431),s=t(27490),i=t(73286),d=t(55868),n=t(25094),o=t(62207),l=t(93803);let c=s.forwardRef(({className:e,type:a,...t},s)=>(0,r.jsx)("input",{type:a,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...t}));c.displayName="Input";var f=t(13381);let u=s.forwardRef(({className:e,orientation:a="horizontal",decorative:t=!0,...s},i)=>(0,r.jsx)(f.b,{ref:i,decorative:t,orientation:a,className:(0,o.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",e),...s}));u.displayName=f.b.displayName;var m=t(77994),p=t(97036);let b=m.bL;m.l9,m.bm;let g=m.ZL,x=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(m.hJ,{className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a,ref:t}));x.displayName=m.hJ.displayName;let h=(0,d.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),v=s.forwardRef(({side:e="right",className:a,children:t,...s},i)=>(0,r.jsxs)(g,{children:[(0,r.jsx)(x,{}),(0,r.jsxs)(m.UC,{ref:i,className:(0,o.cn)(h({side:e}),a),...s,children:[t,(0,r.jsxs)(m.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));function w({className:e,...a}){return(0,r.jsx)("div",{className:(0,o.cn)("animate-pulse rounded-md bg-muted",e),...a})}v.displayName=m.UC.displayName,s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(m.hE,{ref:t,className:(0,o.cn)("text-lg font-semibold text-foreground",e),...a})).displayName=m.hE.displayName,s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(m.VY,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",e),...a})).displayName=m.VY.displayName;var y=t(86153);let N=y.Provider,j=y.Root,R=y.Trigger,k=s.forwardRef(({className:e,sideOffset:a=4,...t},s)=>(0,r.jsx)(y.Content,{ref:s,sideOffset:a,className:(0,o.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));k.displayName=y.Content.displayName;let S="18rem",z=s.createContext(null);function _(){let e=s.useContext(z);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}let C=s.forwardRef(({defaultOpen:e=!0,open:a,onOpenChange:t,className:i,style:d,children:n,...l},c)=>{let f=function(){let[e,a]=(0,s.useState)(!1);return e}(),[u,m]=s.useState(!1),[p,b]=s.useState(e),g=a??p,x=s.useCallback(e=>{let a="function"==typeof e?e(g):e;t?t(a):b(a),document.cookie=`sidebar:state=${a}; path=/; max-age=604800`},[t,g]);s.useEffect(()=>{let e=()=>{let e=window.innerWidth;e>=1024&&e<1280&&g?x(!1):!(e>=1280)||g||f||x(!0)};return window.addEventListener("resize",e),e(),()=>window.removeEventListener("resize",e)},[g,x,f]);let h=s.useCallback(()=>f?m(e=>!e):x(e=>!e),[f,x,m]);s.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),h())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[h]);let v=g?"expanded":"collapsed",w=s.useMemo(()=>({state:v,open:g,setOpen:x,isMobile:f,openMobile:u,setOpenMobile:m,toggleSidebar:h}),[v,g,x,f,u,m,h]);return(0,r.jsx)(z.Provider,{value:w,children:(0,r.jsx)(N,{delayDuration:0,children:(0,r.jsx)("div",{style:{"--sidebar-width":"16rem","--sidebar-width-tablet":"14rem","--sidebar-width-mobile":S,"--sidebar-width-icon":"3rem",...d},className:(0,o.cn)("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",i),ref:c,...l,children:n})})})});C.displayName="SidebarProvider";let A=s.forwardRef(({side:e="left",variant:a="sidebar",collapsible:t="offcanvas",className:s,children:i,...d},n)=>{let{isMobile:l,state:c,openMobile:f,setOpenMobile:u}=_();return"none"===t?(0,r.jsx)("div",{className:(0,o.cn)("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",s),ref:n,...d,children:i}):l?(0,r.jsx)(b,{open:f,onOpenChange:u,...d,children:(0,r.jsx)(v,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":S},side:e,children:(0,r.jsx)("div",{className:"flex h-full w-full flex-col",children:i})})}):(0,r.jsxs)("div",{ref:n,className:"group peer hidden lg:block text-sidebar-foreground","data-state":c,"data-collapsible":"collapsed"===c?t:"","data-variant":a,"data-side":e,children:[(0,r.jsx)("div",{className:(0,o.cn)("duration-200 relative h-svh bg-transparent transition-[width] ease-linear","w-[--sidebar-width-tablet] xl:w-[--sidebar-width]","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===a||"inset"===a?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),(0,r.jsx)("div",{className:(0,o.cn)("duration-200 fixed inset-y-0 z-10 hidden h-svh transition-[left,right,width] ease-linear lg:flex","w-[--sidebar-width-tablet] xl:w-[--sidebar-width]","left"===e?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width-tablet)*-1)] xl:group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width-tablet)*-1)] xl:group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===a||"inset"===a?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",s),...d,children:(0,r.jsx)("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:i})})]})});A.displayName="Sidebar";let D=s.forwardRef(({className:e,onClick:a,...t},s)=>{let{toggleSidebar:i}=_();return(0,r.jsxs)(l.$,{ref:s,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:(0,o.cn)("h-7 w-7",e),onClick:e=>{a?.(e),i()},...t,children:[(0,r.jsx)(n.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})});D.displayName="SidebarTrigger",s.forwardRef(({className:e,...a},t)=>{let{toggleSidebar:s}=_();return(0,r.jsx)("button",{ref:t,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:s,title:"Toggle Sidebar",className:(0,o.cn)("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...a})}).displayName="SidebarRail",s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("main",{ref:t,className:(0,o.cn)("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",e),...a})).displayName="SidebarInset",s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(c,{ref:t,"data-sidebar":"input",className:(0,o.cn)("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",e),...a})).displayName="SidebarInput";let L=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("div",{ref:t,"data-sidebar":"header",className:(0,o.cn)("flex flex-col gap-2 p-2",e),...a}));L.displayName="SidebarHeader";let M=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("div",{ref:t,"data-sidebar":"footer",className:(0,o.cn)("flex flex-col gap-2 p-2",e),...a}));M.displayName="SidebarFooter",s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(u,{ref:t,"data-sidebar":"separator",className:(0,o.cn)("mx-2 w-auto bg-sidebar-border",e),...a})).displayName="SidebarSeparator";let I=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("div",{ref:t,"data-sidebar":"content",className:(0,o.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...a}));I.displayName="SidebarContent";let P=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("div",{ref:t,"data-sidebar":"group",className:(0,o.cn)("relative flex w-full min-w-0 flex-col p-2",e),...a}));P.displayName="SidebarGroup";let E=s.forwardRef(({className:e,asChild:a=!1,...t},s)=>{let d=a?i.DX:"div";return(0,r.jsx)(d,{ref:s,"data-sidebar":"group-label",className:(0,o.cn)("duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...t})});E.displayName="SidebarGroupLabel",s.forwardRef(({className:e,asChild:a=!1,...t},s)=>{let d=a?i.DX:"button";return(0,r.jsx)(d,{ref:s,"data-sidebar":"group-action",className:(0,o.cn)("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",e),...t})}).displayName="SidebarGroupAction";let U=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("div",{ref:t,"data-sidebar":"group-content",className:(0,o.cn)("w-full text-sm",e),...a}));U.displayName="SidebarGroupContent";let V=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("ul",{ref:t,"data-sidebar":"menu",className:(0,o.cn)("flex w-full min-w-0 flex-col gap-1",e),...a}));V.displayName="SidebarMenu";let T=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("li",{ref:t,"data-sidebar":"menu-item",className:(0,o.cn)("group/menu-item relative",e),...a}));T.displayName="SidebarMenuItem";let q=(0,d.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!p-0"}},defaultVariants:{variant:"default",size:"default"}}),$=s.forwardRef(({asChild:e=!1,isActive:a=!1,variant:t="default",size:s="default",tooltip:d,className:n,...l},c)=>{let f=e?i.DX:"button",{isMobile:u,state:m}=_(),p=(0,r.jsx)(f,{ref:c,"data-sidebar":"menu-button","data-size":s,"data-active":a,className:(0,o.cn)(q({variant:t,size:s}),n),...l});return d?("string"==typeof d&&(d={children:d}),(0,r.jsxs)(j,{children:[(0,r.jsx)(R,{asChild:!0,children:p}),(0,r.jsx)(k,{side:"right",align:"center",hidden:"collapsed"!==m||u,...d})]})):p});$.displayName="SidebarMenuButton",s.forwardRef(({className:e,asChild:a=!1,showOnHover:t=!1,...s},d)=>{let n=a?i.DX:"button";return(0,r.jsx)(n,{ref:d,"data-sidebar":"menu-action",className:(0,o.cn)("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",t&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",e),...s})}).displayName="SidebarMenuAction",s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("div",{ref:t,"data-sidebar":"menu-badge",className:(0,o.cn)("absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",e),...a})).displayName="SidebarMenuBadge",s.forwardRef(({className:e,showIcon:a=!1,...t},i)=>{let d=s.useMemo(()=>`${Math.floor(40*Math.random())+50}%`,[]);return(0,r.jsxs)("div",{ref:i,"data-sidebar":"menu-skeleton",className:(0,o.cn)("rounded-md h-8 flex gap-2 px-2 items-center",e),...t,children:[a&&(0,r.jsx)(w,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,r.jsx)(w,{className:"h-4 flex-1 max-w-[--skeleton-width]","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":d}})]})}).displayName="SidebarMenuSkeleton",s.forwardRef(({className:e,...a},t)=>(0,r.jsx)("ul",{ref:t,"data-sidebar":"menu-sub",className:(0,o.cn)("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...a})).displayName="SidebarMenuSub",s.forwardRef(({...e},a)=>(0,r.jsx)("li",{ref:a,...e})).displayName="SidebarMenuSubItem",s.forwardRef(({asChild:e=!1,size:a="md",isActive:t,className:s,...d},n)=>{let l=e?i.DX:"a";return(0,r.jsx)(l,{ref:n,"data-sidebar":"menu-sub-button","data-size":a,"data-active":t,className:(0,o.cn)("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===a&&"text-xs","md"===a&&"text-sm","group-data-[collapsible=icon]:hidden",s),...d})}).displayName="SidebarMenuSubButton";var F=t(29950),G=t.n(F),H=t(22293),O=t(74888),X=t(42789),J=t(20446),Z=t(71529),B=t(82184),W=t(40211),Y=t(98656),K=t(37980),Q=t(99581),ee=t(96909),ea=t(59835),et=t(5698),er=t(90546);let es=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(er.bL,{ref:t,className:(0,o.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...a}));es.displayName=er.bL.displayName;let ei=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(er._V,{ref:t,className:(0,o.cn)("aspect-square h-full w-full",e),...a}));ei.displayName=er._V.displayName;let ed=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(er.H4,{ref:t,className:(0,o.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...a}));ed.displayName=er.H4.displayName;var en=t(67198),eo=t(48368),el=t(65620),ec=t(9494);let ef=en.bL,eu=en.l9;en.YJ,en.ZL,en.Pb,en.z6,s.forwardRef(({className:e,inset:a,children:t,...s},i)=>(0,r.jsxs)(en.ZP,{ref:i,className:(0,o.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",a&&"pl-8",e),...s,children:[t,(0,r.jsx)(eo.A,{className:"ml-auto h-4 w-4"})]})).displayName=en.ZP.displayName,s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(en.G5,{ref:t,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a})).displayName=en.G5.displayName;let em=s.forwardRef(({className:e,sideOffset:a=4,...t},s)=>(0,r.jsx)(en.ZL,{children:(0,r.jsx)(en.UC,{ref:s,sideOffset:a,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})}));em.displayName=en.UC.displayName;let ep=s.forwardRef(({className:e,inset:a,...t},s)=>(0,r.jsx)(en.q7,{ref:s,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a&&"pl-8",e),...t}));ep.displayName=en.q7.displayName,s.forwardRef(({className:e,children:a,checked:t,...s},i)=>(0,r.jsxs)(en.H_,{ref:i,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:t,...s,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(en.VF,{children:(0,r.jsx)(el.A,{className:"h-4 w-4"})})}),a]})).displayName=en.H_.displayName,s.forwardRef(({className:e,children:a,...t},s)=>(0,r.jsxs)(en.hN,{ref:s,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(en.VF,{children:(0,r.jsx)(ec.A,{className:"h-2 w-2 fill-current"})})}),a]})).displayName=en.hN.displayName;let eb=s.forwardRef(({className:e,inset:a,...t},s)=>(0,r.jsx)(en.JU,{ref:s,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",a&&"pl-8",e),...t}));eb.displayName=en.JU.displayName;let eg=s.forwardRef(({className:e,...a},t)=>(0,r.jsx)(en.wv,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...a}));function ex(){let{user:e,logout:a}=(0,et.A)(),t=(0,H.useRouter)();if(!e)return null;let s=e.login||e.email?.split("@")[0]||"User",i=e.name?e.name.split(" ").filter(e=>e.length>0).map(e=>e[0]).join("").toUpperCase():s.substring(0,2).toUpperCase();return(0,r.jsx)(V,{children:(0,r.jsx)(T,{children:(0,r.jsxs)(ef,{children:[(0,r.jsx)(eu,{asChild:!0,children:(0,r.jsxs)($,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[(0,r.jsxs)(es,{className:"h-8 w-8 rounded-lg",children:[(0,r.jsx)(ei,{src:e.avatar_url||void 0,alt:e.name||s}),(0,r.jsx)(ed,{className:"rounded-lg",children:i})]}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-semibold text-gray-900",children:e.name||s}),(0,r.jsx)("span",{className:"truncate text-xs text-gray-600",children:e.email||`@${s}`})]})]})}),(0,r.jsxs)(em,{className:"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg",side:"bottom",align:"end",sideOffset:4,children:[(0,r.jsx)(eb,{className:"p-0 font-normal",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[(0,r.jsxs)(es,{className:"h-8 w-8 rounded-lg",children:[(0,r.jsx)(ei,{src:e.avatar_url||void 0,alt:e.name||s}),(0,r.jsx)(ed,{className:"rounded-lg",children:i})]}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-semibold text-gray-900",children:e.name||s}),(0,r.jsx)("span",{className:"truncate text-xs text-gray-600",children:e.email||`@${s}`})]})]})}),(0,r.jsx)(eg,{}),(0,r.jsx)(ep,{asChild:!0,children:(0,r.jsxs)(G(),{href:"/dashboard/profile",className:"flex items-center text-gray-700 hover:!text-gray-900 hover:!bg-gray-50 cursor-pointer",children:[(0,r.jsx)(ee.A,{className:"mr-2 h-4 w-4 text-gray-500 hover:!text-gray-700"}),"Profile"]})}),(0,r.jsx)(eg,{}),(0,r.jsxs)(ep,{onClick:()=>{a(),t.push("/login")},className:"text-gray-700 hover:!text-gray-900 hover:!bg-gray-50 cursor-pointer",children:[(0,r.jsx)(ea.A,{className:"mr-2 h-4 w-4 text-gray-500 hover:!text-gray-700"}),"Sign out"]})]})]})})})}eg.displayName=en.wv.displayName;let eh=()=>[{title:"Home",url:"/dashboard",icon:O.A},{title:"Repositories",url:"/dashboard/repositories",icon:X.A},{title:"Integrations",url:"/dashboard/integrations",icon:J.A,locked:!0},{title:"Reports",url:"/dashboard/reports",icon:Z.A,locked:!0},{title:"Learnings",url:"/dashboard/learnings",icon:B.A},{title:"Organization Settings",url:"/dashboard/settings",icon:W.A},{title:"Subscription",url:"/dashboard/subscription",icon:Y.A}],ev=[{title:"Docs",url:"/docs",icon:B.A},{title:"Support",url:"/support",icon:K.A}];function ew(){let e=(0,H.usePathname)(),a=eh();return(0,r.jsxs)(A,{className:"shadow-sm border-r border-gray-200 flex-shrink-0 bg-white",children:[(0,r.jsx)(L,{className:"border-b border-gray-200 pb-4 space-y-4",children:(0,r.jsx)("div",{className:"px-2 py-2",children:(0,r.jsx)("h2",{className:"text-sm font-semibold",children:"Platyfend"})})}),(0,r.jsxs)(I,{className:"px-2 overflow-y-auto",children:[(0,r.jsxs)(P,{children:[(0,r.jsx)(E,{className:"text-xs font-semibold text-gray-400 uppercase tracking-wider px-2 mb-2",children:"Navigation"}),(0,r.jsx)(U,{children:(0,r.jsx)(V,{className:"space-y-1",children:a.map(a=>(0,r.jsx)(T,{children:(0,r.jsx)($,{asChild:!0,isActive:e===a.url||"/dashboard"!==a.url&&e.startsWith(a.url),className:"w-full justify-start rounded-lg px-3 py-2 text-sm font-medium transition-all text-gray-600 hover:text-gray-800 hover:bg-gray-50 data-[active=true]:bg-[#00617b]/10 data-[active=true]:text-[#00617b] data-[active=true]:font-semibold",children:(0,r.jsxs)(G(),{href:a.url,className:"flex items-center w-full",children:[(0,r.jsx)(a.icon,{className:"h-4 w-4 mr-3"}),(0,r.jsx)("span",{className:"flex-1",children:a.title}),a.locked&&(0,r.jsx)(Q.A,{className:"h-4 w-4 text-gray-400"})]})})},a.title))})})]}),(0,r.jsxs)(P,{className:"mt-8",children:[(0,r.jsx)(E,{className:"text-xs font-semibold text-gray-400 uppercase tracking-wider px-2 mb-2",children:"Resources"}),(0,r.jsx)(U,{children:(0,r.jsx)(V,{className:"space-y-1",children:ev.map(e=>(0,r.jsx)(T,{children:(0,r.jsx)($,{asChild:!0,className:"w-full justify-start rounded-lg px-3 py-2 text-sm font-medium transition-all text-gray-600 hover:text-gray-800 hover:bg-gray-50",children:(0,r.jsxs)(G(),{href:e.url,children:[(0,r.jsx)(e.icon,{className:"h-4 w-4 mr-3"}),(0,r.jsx)("span",{children:e.title})]})})},e.title))})})]})]}),(0,r.jsx)(M,{children:(0,r.jsx)(ex,{})})]})}var ey=t(29317);function eN({children:e}){return(0,r.jsx)(C,{children:(0,r.jsxs)("div",{className:"min-h-screen flex w-full bg-gray-50 overflow-hidden",children:[(0,r.jsx)(ew,{}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col bg-gray-50 min-w-0 w-full",children:[(0,r.jsx)("div",{className:"lg:hidden flex items-center p-4 border-b",children:(0,r.jsx)(D,{children:(0,r.jsxs)(l.$,{variant:"ghost",size:"icon",className:"mr-2",children:[(0,r.jsx)(ey.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle menu"})]})})}),(0,r.jsx)("main",{className:"flex-1 p-4 sm:p-6 lg:p-8 min-w-0 overflow-auto bg-gray-50 w-full",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto w-full",children:e})})]})]})})}}};
//# sourceMappingURL=542.js.map