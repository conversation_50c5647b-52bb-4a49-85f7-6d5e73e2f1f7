{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/FalcodeSec/frontend/src/lib/auth/config.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\";\nimport GitHub<PERSON>rovider from \"next-auth/providers/github\";\n\nif (!process.env.GITHUB_OAUTH_CLIENT_ID || !process.env.GITHUB_OAUTH_CLIENT_SECRET) {\n  throw new Error(\"Missing GitHub OAuth credentials\");\n}\n\nif (!process.env.NEXTAUTH_SECRET) {\n  throw new Error(\"Missing NEXTAUTH_SECRET\");\n}\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    GitHubProvider({\n      clientId: process.env.GITHUB_OAUTH_CLIENT_ID,\n      clientSecret: process.env.GITHUB_OAUTH_CLIENT_SECRET,\n      authorization: {\n        params: {\n          scope: \"read:user user:email\",\n        },\n      },\n    }),\n  ],\n  pages: {\n    signIn: \"/login\",\n    error: \"/login\",\n  },\n  callbacks: {\n    async jwt({ token, account, profile }) {\n      if (account && profile) {\n        token.accessToken = account.access_token;\n        token.id = (profile as any).id;\n        token.login = (profile as any).login;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (session.user) {\n        (session.user as any).id = token.id;\n        (session.user as any).login = token.login;\n      }\n      return session;\n    },\n  },\n  session: {\n    strategy: \"jwt\",\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n\n"], "names": [], "mappings": ";;;AACA;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,sBAAsB,IAAI,CAAC,QAAQ,GAAG,CAAC,0BAA0B,EAAE;IAClF,MAAM,IAAI,MAAM;AAClB;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,eAAe,EAAE;IAChC,MAAM,IAAI,MAAM;AAClB;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,sBAAsB;YAC5C,cAAc,QAAQ,GAAG,CAAC,0BAA0B;YACpD,eAAe;gBACb,QAAQ;oBACN,OAAO;gBACT;YACF;QACF;KACD;IACD,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE;YACnC,IAAI,WAAW,SAAS;gBACtB,MAAM,WAAW,GAAG,QAAQ,YAAY;gBACxC,MAAM,EAAE,GAAG,AAAC,QAAgB,EAAE;gBAC9B,MAAM,KAAK,GAAG,AAAC,QAAgB,KAAK;YACtC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,QAAQ,IAAI,EAAE;gBACf,QAAQ,IAAI,CAAS,EAAE,GAAG,MAAM,EAAE;gBAClC,QAAQ,IAAI,CAAS,KAAK,GAAG,MAAM,KAAK;YAC3C;YACA,OAAO;QACT;IACF;IACA,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/FalcodeSec/frontend/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\nimport { authOptions } from \"@/src/lib/auth/config\";\n\nconst handler = NextAuth(authOptions);\n\nexport { handler as GET, handler as POST };\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,8HAAA,CAAA,cAAW", "debugId": null}}]}