{"version": 3, "file": "597.js", "mappings": "ugBAqBA,IAAMA,EAAcC,CAAAA,EAAAA,EAAAA,aAAAA,CAAaA,MAA8BC,GAExD,SAASC,EAAa,UAAEC,CAAQ,CAAiC,EACtE,GAAM,CAACC,EAAMC,EAAQ,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,MACxC,CAACC,EAAOC,EAAS,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MAC5C,CAACG,EAAWC,EAAa,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAwD3C,MAAO,UAACP,EAAYY,QAAQ,EAACC,MARE,CAQKA,KAPlCR,QACAG,YACAE,EACAI,gBAAiB,CAAC,CAACN,GAAS,CAAC,CAACH,EAC9BU,OAXa,KACbC,CAAAA,EAAAA,EAAAA,EAAAA,CAAgBA,GAChBP,EAAS,MACTH,EAAQ,KACV,CAQA,WAE4CF,GAC9C,CAEO,SAASa,IACd,IAAMC,EAAUC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAACnB,GAC3B,QAAgBE,IAAZgB,EACF,KADyB,CACnB,MAAU,+CAElB,OAAOA,CACT,YC3FA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,UACA,sBCRA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,UACA,4BCRA,uCAAuN,CAEvN,uCAAuK,CAEvK,uCAAsJ,CAEtJ,uCAAuJ,CAEvJ,uCAA4J,2FCD5J,IAAME,EAAU,CAAC,CAAE,GAAGC,EAAqB,IACzC,GAAM,OAAEC,EAAQ,QAAQ,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GAErC,MACE,UAACC,EAAAA,EAAMA,CAAAA,CACLF,MAAOA,EACPG,UAAU,gBACVC,aAAc,CACZC,WAAY,CACVC,MACE,wIACFC,YAAa,uCACbC,aACE,mEACFC,aACE,8DACJ,CACF,EACC,GAAGV,CAAK,EAGf,u1BC5BA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,uBCRA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,uBCRA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,8CKWI,sBAAsB,8MHd1B,IAAMW,EAAkBC,EAAAA,QAAyB,CAEjCA,EAAAA,IAAqB,CAEdA,EAAAA,OAAwB,CAExBC,EAAAA,UAAgB,CAGrC,CAAC,WAAET,CAAS,YAAEU,EAAa,CAAC,CAAE,GAAGd,EAAO,CAAEe,IAC1C,UAACH,EAAAA,OAAwB,EACvBG,IAAKA,EACLD,WAAYA,EACZV,UDfG,ECeUY,ODfDA,GAAMC,CAAoB,EACxC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACF,GACtB,ECcM,qYACAb,GAED,GAAGJ,CAAK,IAGEoB,WAAW,CAAGR,EAAAA,OAAwB,CAACQ,WAAW,sFCQ1D,IAAMC,EAAgB,CAC3BC,IAAAA,QAAc,CACdC,IAAAA,QAAsB,CACtBC,IAAAA,QAAiB,CAClB,CAACC,IAAI,CAAC,KAAI,4BC1BEC,EAAqB,CAChCC,KADgC,CACzB,8CACPnB,WAAa,+HACf,EAEaoB,EAAqB,CAChCC,KADgC,CACzB,eACPC,YAAc,GACdC,YAAc,GACdC,YAAc,GAChB,ECbM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CDDrB,SAASC,CCCA,KAA4B,IDD5BA,CACd,CAGT,EACC,MACEC,CAAAA,EAAAA,EAAAA,IAAAA,CAACC,CAAAA,MAAAA,CAAAA,CAAKC,IAAK,MAAKhC,SAAWiB,CAAAA,EAAegB,WAAfhB,aAAuC,cAChEa,CAAAA,EAAAA,EAAAA,IAAAA,CAACI,CAAAA,MAAAA,CAAAA,WACCC,CAAAA,EAAAA,EAAAA,GAAAA,CAACC,CAAAA,MAAAA,CAAAA,CAAKC,IAAK,YAAWC,OAAQ,6EAC9BH,CAAAA,EAAAA,EAAAA,GAAAA,CAACI,CAAAA,MAAAA,CAAAA,CAAKC,GAAI,cAAaC,IAAK,kCAC5BN,CAAAA,EAAAA,EAAAA,GAAAA,CAACI,CAAAA,MAAAA,CAAAA,CAAKC,GAAI,cAAaC,IAAK,6BAA4BC,WAAY,kBAEtEP,CAAAA,EAAAA,EAAAA,GAAAA,CAACQ,CAAAA,MAAAA,CAAAA,CAAK3C,SAAAA,CAAWkB,IAAMlB,SAAS,CAC9B,SAAAmC,CAAAA,EAAAA,EAAAA,GAAAA,CAACzD,CAAAA,EAAAA,YAAAA,CAAAA,CACC,SAAAyD,CAAAA,EAAAA,EAAAA,GAAAA,CAACS,CAAAA,EAAAA,aAAAA,CAAAA,CACCC,SAAU,SACVC,YAAa,MAJG5B,GAKhB6B,YAAc,IACdC,yBAAyB,IAEzB,SAAAlB,CAAAA,EAAAA,EAAAA,IAAAA,CAACvB,CAAAA,EAAAA,WACC4B,CAAAA,CADD5B,CACC4B,EAAAA,GAAAA,CAACxC,CAAAA,EAAAA,OAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CACDwC,CAAAA,EAAAA,EAAAA,GAAAA,CAACpC,CAAAA,EAAAA,OAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CACApB,aAOf,EC7BsD,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EANxB,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAAC,GAAG,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,MAD4B,CACnB,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,GAAG,CACnB,aAAa,CAAE,QAAQ,mBACvB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAExB,CAKC,IAAC,OAOF,EAEE,OAOF,EAEE,OAOF,EAEE,EAA2B,CAlBN,IASL,QCvEtB,SDgF8B,EChF9B,GACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,uBCRA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,uBCRA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,6vCCRA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,uBCRA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,uBCRA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,uBCRA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,6BCRA,uCAAuN,CAEvN,uCAAuK,CAEvK,uCAAsJ,CAEtJ,uCAAuJ,CAEvJ,sCAA4J,YCR5J,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,uBCRA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,mGdLO,SAASiC,EAAG,GAAGC,CAAoB,EACxC,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAIA,CAACF,GACtB,agBLA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,uBCRA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,uBCRA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,uBCRA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,uBCRA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,0CCAO,SAASoC,IACqB,OAAO,IAE5C,CAEO,SAASC,EAAenE,CAAa,EAG5C,CAEO,SAASQ,IAGhB,CAEO,SAAS4D,IACd,IAAMpE,EAAQkE,WACd,EAGO,CACLG,CAJE,GAAQ,UAIK,CAAC,OAAO,EAAErE,EAAAA,CAAO,EAHzB,CAAC,CAKZ,6HCPA,IAAIsE,EAAQ,EA+BNC,EAAgB,IAAIC,IAEpBC,EAAmB,IACvB,GAAIF,EAAcG,GAAG,CAACC,GACpB,OAD8B,IAI1BC,EAAUC,WAAW,KACzBN,EAAcO,MAAM,CAACH,GACrBI,EAAS,CACPC,KAAM,eACNL,QAASA,CACX,EACF,EA5DyB,CA4DtBM,IAEHV,EAAcW,GAAG,CAACP,EAASC,EAC7B,EAEaO,EAAU,CAACC,EAAcC,KACpC,OAAQA,EAAOL,IAAI,EACjB,IAAK,YACH,MAAO,CACL,GAAGI,CAAK,CACRE,OAAQ,CAACD,EAAOjE,KAAK,IAAKgE,EAAME,MAAM,CAAC,CAACC,KAAK,CAAC,EAvElC,CAuEqCC,CACnD,CAEF,KAAK,eACH,MAAO,CACL,GAAGJ,CAAK,CACRE,OAAQF,EAAME,MAAM,CAACG,GAAG,CAAEC,GACxBA,EAAEC,EAAE,GAAKN,EAAOjE,KAAK,CAACuE,EAAE,CAAG,CAAE,GAAGD,CAAC,CAAE,GAAGL,EAAOjE,KAAM,EAAIsE,EAE3D,CAEF,KAAK,gBAAiB,CACpB,GAAM,SAAEf,CAAO,CAAE,CAAGU,EAYpB,OARIV,EACFF,EAAiBE,GAEjBS,EAHW,MAGC,CAACQ,OAAO,CAAExE,IACpBqD,EAAiBrD,EAAMuE,EAAE,CAC3B,GAGK,CACL,GAAGP,CAAK,CACRE,OAAQF,EAAME,MAAM,CAACG,GAAG,CAAC,GACvBC,EAAEC,EAAE,GAAKhB,QAAuBjF,IAAZiF,EAChB,CACE,GAAGe,CAAC,CACJG,MAAM,CACR,EACAH,EAER,CACF,CACA,IAAK,eACH,GAAIL,WAA8B,OAAhB,CAChB,MAAO,CACL,GAAGD,CAAK,CACRE,OAAQ,EAAE,EAGd,MAAO,CACL,GAAGF,CAAK,CACRE,OAAQF,EAAME,MAAM,CAACQ,MAAM,CAAC,GAAOJ,EAAEC,EAAE,GAAKN,EAAOV,OAAO,CAC5D,CACJ,CACF,EAAC,EAEgD,EAAE,CAE/CoB,EAAqB,CAAET,OAAQ,EAAE,EAErC,SAASP,EAASM,CAAc,EAC9BU,EAAcZ,EAAQY,EAAaV,GACnCW,EAAUJ,OAAO,CAAC,IAChBK,EAASF,EACX,EACF,CAIA,SAAS3E,EAAM,CAAE,GAAGP,EAAc,EAChC,IAAM8E,EAlHCrB,CADPA,EAAQ,CAACA,GAAQ,EAAK4B,OAAOC,gBAAAA,EAChBC,QAAQ,GAyHfC,EAAU,IAAMtB,EAAS,CAAEC,KAAM,gBAAiBL,QAASgB,CAAG,GAcpE,OAZAZ,EAAS,CACPC,KAAM,YACN5D,MAAO,CACL,GAAGP,CAAK,IACR8E,EACAE,MAAM,EACNS,aAAc,IACR,GAAOD,GACb,CACF,CACF,GAEO,CACLV,GAAIA,UACJU,EACAE,OAtBa,GACbxB,EAAS,CACPC,KAAM,eACN5D,MAAO,CAAE,GAAGP,CAAK,IAAE8E,CAAG,CACxB,EAmBF,CACF,iDCjKA,IAAMa,EAAgBC,EAAAA,EAAwB,CAExCC,EAAgBhF,EAAAA,UAAgB,CAGpC,CAAC,WAAET,CAAS,CAAE,GAAGJ,EAAO,CAAEe,IAC1B,UAAC6E,EAAAA,EAAwB,EACvB7E,IAAKA,EACLX,UAAWY,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,oIACAZ,GAED,GAAGJ,CAAK,IAGb6F,EAAczE,WAAW,CAAGwE,EAAAA,EAAwB,CAACxE,WAAW,CAEhE,IAAM0E,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CACvB,4lBACA,CACEC,SAAU,CACRC,QAAS,CACPC,QAAS,uCACTC,YACE,iFACJ,CACF,EACAC,gBAAiB,CACfH,QAAS,SACX,CACF,GAGII,EAAQxF,EAAAA,UAAgB,CAI5B,CAAC,WAAET,CAAS,CAAE6F,SAAO,CAAE,GAAGjG,EAAO,CAAEe,IAEjC,UAAC6E,EAAAA,EAAoB,EACnB7E,IAAKA,EACLX,UAAWY,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC8E,EAAc,SAAEG,CAAQ,GAAI7F,GACzC,GAAGJ,CAAK,IAIfqG,EAAMjF,WAAW,CAAGwE,EAAAA,EAAoB,CAACxE,WAAW,CAEhCP,EAAAA,UAAgB,CAGlC,CAAC,WAAET,CAAS,CAAE,GAAGJ,EAAO,CAAEe,IAC1B,UAAC6E,EAAAA,EAAsB,EACrB7E,IAAKA,EACLX,UAAWY,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,qgBACAZ,GAED,GAAGJ,CAAK,IAGDoB,WAAW,CAAGwE,EAAAA,EAAsB,CAACxE,WAAW,CAE5D,IAAMkF,EAAazF,EAAAA,UAAgB,CAGjC,CAAC,WAAET,CAAS,CAAE,GAAGJ,EAAO,CAAEe,IAC1B,UAAC6E,EAAAA,EAAqB,EACpB7E,IAAKA,EACLX,UAAWY,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,wVACAZ,GAEFmG,cAAY,GACX,GAAGvG,CAAK,UAET,UAACwG,EAAAA,CAACA,CAAAA,CAACpG,UAAU,eAGjBkG,EAAWlF,WAAW,CAAGwE,EAAAA,EAAqB,CAACxE,WAAW,CAE1D,IAAMqF,EAAa5F,EAAAA,UAAgB,CAGjC,CAAC,WAAET,CAAS,CAAE,GAAGJ,EAAO,CAAEe,IAC1B,UAAC6E,EAAAA,EAAqB,EACpB7E,IAAKA,EACLX,UAAWY,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,wBAAyBZ,GACtC,GAAGJ,CAAK,IAGbyG,EAAWrF,WAAW,CAAGwE,EAAAA,EAAqB,CAACxE,WAAW,CAE1D,IAAMsF,EAAmB7F,EAAAA,UAAgB,CAGvC,CAAC,WAAET,CAAS,CAAE,GAAGJ,EAAO,CAAEe,IAC1B,UAAC6E,EAAAA,EAA2B,EAC1B7E,IAAKA,EACLX,UAAWY,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qBAAsBZ,GACnC,GAAGJ,CAAK,IC/FN,SAASD,IACd,GAAM,QAAE0E,CAAM,CAAE,CAAGkC,QAAQA,CF6JpBA,EACP,GAAM,CAACpC,EAAOqC,EAAS,CAAG/F,EAAAA,QAAc,CAAQqE,GAWhD,OATArE,EAAAA,SAAe,CAAC,KACdsE,EAAU0B,IAAI,CAACD,GACR,KACL,IAAME,EAAQ3B,EAAU4B,OAAO,CAACH,GAC5BE,EAAQ,CAAC,GAAG,EACJE,MAAM,CAACF,EAAO,EAE5B,GACC,EAAE,EACE,CACL,GAAGvC,CAAK,OACRhE,EACAiF,QAAS,GAAsBtB,EAAS,CAAEC,KAAM,wBAAiBL,CAAQ,EAC3E,CACF,IE5KE,MACE,WAAC6B,EAAaA,WAAAA,EACJf,GAAG,CAAC,SAAU,IAAEE,CAAE,OAAEnD,CAAK,aAAEnB,CAAW,QAAEgE,CAAM,CAAE,GAAGxE,EAAO,EAChE,MACE,WAACqG,EAAKA,CAAW,EAAXA,CAAcrG,CAAK,WACvB,WAACiH,MAAAA,CAAI7G,UAAU,uBACZuB,GAAS,UAAC8E,EAAUA,QAAAA,EAAE9E,IACtBnB,GACC,UAACkG,EAAgBA,UAAElG,IAAFkG,GAGpBlC,EACD,UAAC8B,EAAUA,CAAAA,KARDxB,EAQCwB,GAIjB,UAACT,EAAaA,CAAAA,KAGpB,CD4EAa,EAAiBtF,EC/EGyE,SD+EQ,CAAGD,EAAAA,EAA2B,CAACxE,WAAW,kBE9GtE,4CAAoJ,CAEpJ,4CAAuJ,CAEvJ,4CAAuJ,CAEvJ,4CAA4K,CAE5K,4CAAsJ,CAEtJ,4CAAgK,CAEhK,4CAAmK,CAEnK,4CAAqK,YCdrK,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,uBCRA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,6BCRA,4CAAoJ,CAEpJ,2CAAuJ,CAEvJ,4CAAuJ,CAEvJ,4CAA4K,CAE5K,4CAAsJ,CAEtJ,4CAAgK,CAEhK,4CAAmK,CAEnK,4CAAqK,YCdrK,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,uBCRA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,uBCRA,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA", "sources": ["webpack://platyfend/./src/lib/auth/auth-context.tsx", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-fs/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-mysql2/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/?3ae1", "webpack://platyfend/./src/components/ui/sonner.tsx", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-mysql/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-redis-4/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-koa/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./src/lib/utils/index.ts", "webpack://platyfend/./src/components/ui/tooltip.tsx", "webpack://platyfend/./src/lib/fonts/index.ts", "webpack://platyfend/src/app/layout.tsx", "webpack://platyfend/sentry-wrapper-module", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-amqplib/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-graphql/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-lru-memoizer/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-generic-pool/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-knex/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/?8357", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-tedious/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-kafkajs/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./src/app/globals.css", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-pg/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-dataloader/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-mongoose/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-hapi/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-connect/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./src/lib/auth/token-storage.ts", "webpack://platyfend/./src/components/ui/use-toast.ts", "webpack://platyfend/./src/components/ui/toast.tsx", "webpack://platyfend/./src/components/ui/toaster.tsx", "webpack://platyfend/?b374", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-ioredis/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-mongodb/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/?5c05", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-undici/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./node_modules/@opentelemetry/instrumentation-express/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://platyfend/./node_modules/@sentry/node/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { getStoredToken, setStoredToken, clearStoredToken } from './token-storage';\n\nexport interface User {\n  id: string;\n  login: string;\n  name?: string;\n  email?: string;\n  avatar_url?: string;\n}\n\ninterface AuthContextType {\n  user: User | null;\n  token: string | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  logout: () => void;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null);\n  const [token, setToken] = useState<string | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Initialize auth state from localStorage on mount\n  useEffect(() => {\n    const storedToken = getStoredToken();\n    if (storedToken) {\n      setToken(storedToken);\n      // Optionally validate token with backend\n      validateToken(storedToken);\n    } else {\n      setIsLoading(false);\n    }\n  }, []);\n\n  const validateToken = async (token: string) => {\n    try {\n      const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000';\n      const response = await fetch(`${BACKEND_URL}/api/v1/login/validate-session`, {\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      });\n\n      if (response.ok) {\n        const userData = await response.json();\n        setUser(userData);\n      } else {\n        // Token is invalid, clear it\n        clearStoredToken();\n        setToken(null);\n        setUser(null);\n      }\n    } catch (error) {\n      console.error('Token validation error:', error);\n      clearStoredToken();\n      setToken(null);\n      setUser(null);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = () => {\n    clearStoredToken();\n    setToken(null);\n    setUser(null);\n  };\n\n  const value: AuthContextType = {\n    user,\n    token,\n    isLoading,\n    isAuthenticated: !!token && !!user,\n    logout,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n\n", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 5849;\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 6820;\nmodule.exports = webpackEmptyContext;", "import(/* webpackMode: \"eager\", webpackExports: [\"Provider\",\"Root\",\"Trigger\",\"Content\",\"displayName\"] */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/@radix-ui/react-tooltip/dist/index.mjs\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ThemeProvider\"] */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next-themes/dist/index.mjs\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Toaster\"] */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/components/ui/sonner.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Toaster\"] */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/components/ui/toaster.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"AuthProvider\"] */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/lib/auth/auth-context.tsx\");\n", "'use client'\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Sonner, toast } from \"sonner\"\n\ntype ToasterProps = React.ComponentProps<typeof Sonner>\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      toastOptions={{\n        classNames: {\n          toast:\n            \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\n          description: \"group-[.toast]:text-muted-foreground\",\n          actionButton:\n            \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\n          cancelButton:\n            \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\",\n        },\n      }}\n      {...props}\n    />\n  )\n}\n\nexport { Toaster, toast }\n", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 19082;\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 21490;\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 22943;\nmodule.exports = webpackEmptyContext;", "import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDate(date: Date | string): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  }).format(new Date(date));\n}\n\nexport function formatRelativeTime(date: Date | string): string {\n  const now = new Date();\n  const target = new Date(date);\n  const diffInSeconds = Math.floor((now.getTime() - target.getTime()) / 1000);\n\n  if (diffInSeconds < 60) {\n    return \"just now\";\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60);\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minute${diffInMinutes > 1 ? \"s\" : \"\"} ago`;\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60);\n  if (diffInHours < 24) {\n    return `${diffInHours} hour${diffInHours > 1 ? \"s\" : \"\"} ago`;\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24);\n  if (diffInDays < 7) {\n    return `${diffInDays} day${diffInDays > 1 ? \"s\" : \"\"} ago`;\n  }\n\n  return formatDate(date);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + \"...\";\n}\n\nexport function generateSlug(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^a-z0-9]+/g, \"-\")\n    .replace(/(^-|-$)/g, \"\");\n}\n\nexport function capitalizeFirst(text: string): string {\n  return text.charAt(0).toUpperCase() + text.slice(1);\n}\n\nexport function formatNumber(num: number): string {\n  if (num >= 1000000) {\n    return (num / 1000000).toFixed(1) + \"M\";\n  }\n  if (num >= 1000) {\n    return (num / 1000).toFixed(1) + \"K\";\n  }\n  return num.toString();\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function isValidUrl(url: string): boolean {\n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(\" \")\n    .map((n) => n[0])\n    .join(\"\")\n    .toUpperCase()\n    .slice(0, 2);\n}\n\nexport function copyToClipboard(text: string): Promise<void> {\n  if (navigator.clipboard) {\n    return navigator.clipboard.writeText(text);\n  }\n  \n  // Fallback for older browsers\n  const textArea = document.createElement(\"textarea\");\n  textArea.value = text;\n  document.body.appendChild(textArea);\n  textArea.focus();\n  textArea.select();\n  \n  try {\n    document.execCommand(\"copy\");\n    return Promise.resolve();\n  } catch (err) {\n    return Promise.reject(err);\n  } finally {\n    document.body.removeChild(textArea);\n  }\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise((resolve) => setTimeout(resolve, ms));\n}\n", "import * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/src/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n", "import { Inter, JetBrains_Mono } from 'next/font/google'\nimport localFont from 'next/font/local'\n\n// Google Fonts with optimized loading\nexport const inter = Inter({\n  subsets: ['latin'],\n  display: 'swap',\n  variable: '--font-inter',\n  preload: true,\n})\n\nexport const jetbrainsMono = JetBrains_Mono({\n  subsets: ['latin'],\n  display: 'swap',\n  variable: '--font-jetbrains-mono',\n  preload: false, // Only load when needed for code blocks\n})\n\n// Local custom font (Frutiger)\nexport const frutiger = localFont({\n  src: [\n    {\n      path: '../../../public/Frutiger_bold.woff',\n      weight: '700',\n      style: 'normal',\n    },\n  ],\n  variable: '--font-frutiger',\n  display: 'swap',\n  preload: true,\n})\n\n// Font class names for easy usage\nexport const fontVariables = [\n  inter.variable,\n  jetbrainsMono.variable,\n  frutiger.variable,\n].join(' ')\n\n// CSS custom properties for use in Tailwind config\nexport const fontFamilies = {\n  sans: ['var(--font-inter)', 'system-ui', 'sans-serif'],\n  mono: ['var(--font-jetbrains-mono)', 'Consolas', 'monospace'],\n  frutiger: ['var(--font-frutiger)', 'system-ui', 'sans-serif'],\n}\n", "import { Toaster } from \"@/src/components/ui/toaster\";\nimport { Toaster as Sonner } from \"@/src/components/ui/sonner\";\nimport { TooltipProvider } from \"@/src/components/ui/tooltip\";\nimport { AuthProvider } from \"@/src/lib/auth/auth-context\";\nimport { ThemeProvider } from \"next-themes\";\n\nimport { inter, fontVariables } from \"@/src/lib/fonts\";\nimport type { Metadata, Viewport } from 'next';\nimport './globals.css';\n\n\nexport const metadata: Metadata = {\n  title: 'Platyfend - AI-Powered Code Review Platform',\n  description: 'Next generation secure code review agents with vulnerability detection, GitHub integration, and automated security analysis.',\n};\n\nexport const viewport: Viewport = {\n  width: 'device-width',\n  initialScale: 1,\n  maximumScale: 5,\n  userScalable: true,\n};\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <html lang=\"en\" className={fontVariables} suppressHydrationWarning>\n      <head>\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes\" />\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"anonymous\" />\n      </head>\n      <body className={inter.className}>\n        <AuthProvider>\n          <ThemeProvider\n            attribute=\"class\"\n            defaultTheme=\"light\"\n            enableSystem={false}\n            disableTransitionOnChange\n          >\n            <TooltipProvider>\n              <Toaster />\n              <Sonner />\n              {children}\n            </TooltipProvider>\n          </ThemeProvider>\n        </AuthProvider>\n      </body>\n    </html>\n  )\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/',\n        componentType: 'Layout',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 27934;\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 29903;\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 30854;\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 44725;\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 44994;\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 47712;\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 53320;\nmodule.exports = webpackEmptyContext;", "import(/* webpackMode: \"eager\", webpackExports: [\"Provider\",\"Root\",\"Trigger\",\"Content\",\"displayName\"] */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/@radix-ui/react-tooltip/dist/index.mjs\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ThemeProvider\"] */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next-themes/dist/index.mjs\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Toaster\"] */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/components/ui/sonner.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Toaster\"] */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/components/ui/toaster.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"AuthProvider\"] */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/lib/auth/auth-context.tsx\");\n", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 55611;\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 59223;\nmodule.exports = webpackEmptyContext;", null, "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 62747;\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 63625;\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 67503;\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 69456;\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 70130;\nmodule.exports = webpackEmptyContext;", "/**\n * Token storage utilities for managing JWT tokens from the backend\n * The backend sets a session_token cookie, but we also store it in localStorage\n * for explicit Authorization header usage in cross-origin requests\n */\n\nconst TOKEN_STORAGE_KEY = \"platyfend_session_token\";\n\nexport function getStoredToken(): string | null {\n  if (typeof window === \"undefined\") return null;\n  return localStorage.getItem(TOKEN_STORAGE_KEY);\n}\n\nexport function setStoredToken(token: string): void {\n  if (typeof window === \"undefined\") return;\n  localStorage.setItem(TOKEN_STORAGE_KEY, token);\n}\n\nexport function clearStoredToken(): void {\n  if (typeof window === \"undefined\") return;\n  localStorage.removeItem(TOKEN_STORAGE_KEY);\n}\n\nexport function getAuthHeaders(): HeadersInit {\n  const token = getStoredToken();\n  if (!token) {\n    return {};\n  }\n  return {\n    Authorization: `Bearer ${token}`,\n  };\n}\n\n", "import * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"@/src/components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 5000 // 5 seconds\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [])\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n", "import * as React from \"react\"\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/src/lib/utils\"\n\nconst ToastProvider = ToastPrimitives.Provider\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Viewport\n    ref={ref}\n    className={cn(\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\n      className\n    )}\n    {...props}\n  />\n))\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\n\nconst toastVariants = cva(\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\n  {\n    variants: {\n      variant: {\n        default: \"border bg-background text-foreground\",\n        destructive:\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\n    VariantProps<typeof toastVariants>\n>(({ className, variant, ...props }, ref) => {\n  return (\n    <ToastPrimitives.Root\n      ref={ref}\n      className={cn(toastVariants({ variant }), className)}\n      {...props}\n    />\n  )\n})\nToast.displayName = ToastPrimitives.Root.displayName\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Action\n    ref={ref}\n    className={cn(\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\n      className\n    )}\n    {...props}\n  />\n))\nToastAction.displayName = ToastPrimitives.Action.displayName\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Close\n    ref={ref}\n    className={cn(\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\n      className\n    )}\n    toast-close=\"\"\n    {...props}\n  >\n    <X className=\"h-4 w-4\" />\n  </ToastPrimitives.Close>\n))\nToastClose.displayName = ToastPrimitives.Close.displayName\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Title\n    ref={ref}\n    className={cn(\"text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nToastTitle.displayName = ToastPrimitives.Title.displayName\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Description\n    ref={ref}\n    className={cn(\"text-sm opacity-90\", className)}\n    {...props}\n  />\n))\nToastDescription.displayName = ToastPrimitives.Description.displayName\n\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\n\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\n\nexport {\n  type ToastProps,\n  type ToastActionElement,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n}\n", "'use client'\n\nimport { useToast } from \"./use-toast\"\nimport {\n  Toast,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n} from \"@/src/components/ui/toast\"\n\nexport function Toaster() {\n  const { toasts } = useToast()\n\n  return (\n    <ToastProvider>\n      {toasts.map(function ({ id, title, description, action, ...props }) {\n        return (\n          <Toast key={id} {...props}>\n            <div className=\"grid gap-1\">\n              {title && <ToastTitle>{title}</ToastTitle>}\n              {description && (\n                <ToastDescription>{description}</ToastDescription>\n              )}\n            </div>\n            {action}\n            <ToastClose />\n          </Toast>\n        )\n      })}\n      <ToastViewport />\n    </ToastProvider>\n  )\n}\n", "import(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next/dist/client/components/client-page.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next/dist/client/components/client-segment.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next/dist/client/components/error-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next/dist/client/components/layout-router.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next/dist/client/components/metadata/async-metadata.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next/dist/client/components/metadata/metadata-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next/dist/client/components/render-from-template-context.js\");\n", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 84853;\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 86174;\nmodule.exports = webpackEmptyContext;", "import(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next/dist/client/components/client-page.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next/dist/client/components/client-segment.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next/dist/client/components/error-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next/dist/client/components/layout-router.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next/dist/client/components/metadata/async-metadata.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next/dist/client/components/metadata/metadata-boundary.js\");\n;\nimport(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/next/dist/client/components/render-from-template-context.js\");\n", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 96868;\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 96916;\nmodule.exports = webpackEmptyContext;", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 98629;\nmodule.exports = webpackEmptyContext;"], "names": ["AuthContext", "createContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "user", "setUser", "useState", "token", "setToken", "isLoading", "setIsLoading", "Provider", "value", "isAuthenticated", "logout", "clearStoredToken", "useAuth", "context", "useContext", "Toaster", "props", "theme", "useTheme", "<PERSON><PERSON>", "className", "toastOptions", "classNames", "toast", "description", "actionButton", "cancelButton", "TooltipProvider", "TooltipPrimitive", "React", "sideOffset", "ref", "cn", "inputs", "twMerge", "clsx", "displayName", "fontVariables", "inter", "jetbrainsMono", "frutiger", "join", "metadata", "title", "viewport", "width", "initialScale", "maximumScale", "userScalable", "RootLayout", "_jsxs", "html", "lang", "suppressHydrationWarning", "head", "_jsx", "meta", "name", "content", "link", "rel", "href", "crossOrigin", "body", "ThemeProvider", "attribute", "defaultTheme", "enableSystem", "disableTransitionOnChange", "getStoredToken", "setStoredToken", "getAuthHeaders", "Authorization", "count", "toastTimeouts", "Map", "addToRemoveQueue", "has", "toastId", "timeout", "setTimeout", "delete", "dispatch", "type", "TOAST_REMOVE_DELAY", "set", "reducer", "state", "action", "toasts", "slice", "TOAST_LIMIT", "map", "t", "id", "for<PERSON>ach", "open", "filter", "memoryState", "listeners", "listener", "Number", "MAX_SAFE_INTEGER", "toString", "dismiss", "onOpenChange", "update", "ToastProvider", "ToastPrimitives", "ToastViewport", "toastVariants", "cva", "variants", "variant", "default", "destructive", "defaultVariants", "Toast", "ToastClose", "toast-close", "X", "ToastTitle", "ToastDescription", "useToast", "setState", "push", "index", "indexOf", "splice", "div"], "sourceRoot": ""}