{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/FalcodeSec/frontend/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nexport default function HomePage() {\n  return (\n    <div className=\"p-8\">\n      <h1 className=\"text-2xl font-bold\">Welcome to Falcode</h1>\n      <p className=\"text-gray-600 mt-2\">This is a UI-only build. Visit the dashboard or login pages to preview the UI.</p>\n      <div className=\"mt-4 flex gap-4\">\n        <a href=\"/dashboard\" className=\"underline text-primary\">Go to Dashboard</a>\n        <a href=\"/login\" className=\"underline text-primary\">Login</a>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAqB;;;;;;0BACnC,8OAAC;gBAAE,WAAU;0BAAqB;;;;;;0BAClC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,MAAK;wBAAa,WAAU;kCAAyB;;;;;;kCACxD,8OAAC;wBAAE,MAAK;wBAAS,WAAU;kCAAyB;;;;;;;;;;;;;;;;;;AAI5D", "debugId": null}}]}