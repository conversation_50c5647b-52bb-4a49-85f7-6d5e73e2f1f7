{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/FalcodeSec/frontend/src/components/dashboard/dashboard-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DashboardLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/dashboard-layout.tsx <module evaluation>\",\n    \"DashboardLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,+EACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/FalcodeSec/frontend/src/components/dashboard/dashboard-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const DashboardLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/dashboard-layout.tsx\",\n    \"DashboardLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,2DACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/FalcodeSec/frontend/src/app/dashboard/layout.tsx"], "sourcesContent": ["import { DashboardLayout } from '@/src/components/dashboard/dashboard-layout'\n\nexport default function Layout({ children }: { children: React.ReactNode }) {\n  return (\n    <DashboardLayout>\n      {children}\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,OAAO,EAAE,QAAQ,EAAiC;IACxE,qBACE,8OAAC,sJAAA,CAAA,kBAAe;kBACb;;;;;;AAGP", "debugId": null}}]}