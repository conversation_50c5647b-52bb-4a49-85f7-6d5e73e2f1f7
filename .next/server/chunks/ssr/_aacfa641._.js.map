{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/hooks/use-github-app-installations.ts"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\nimport { getAuthHeaders, getStoredToken } from \"@/src/lib/auth/token-storage\";\n\nexport interface Repository {\n  id: number;\n  name: string;\n  full_name: string;\n  url: string;\n  private: boolean;\n  added_at: string;\n}\n\nexport interface GitHubAppInstallation {\n  installation_id: number;\n  status: \"active\" | \"inactive\" | \"revoked\";\n  repositories: Repository[];\n  installed_at: string;\n  last_webhook_received: string | null;\n}\n\ninterface UseGitHubAppInstallationsReturn {\n  installations: GitHubAppInstallation[];\n  loading: boolean;\n  error: string | null;\n  refetch: () => Promise<void>;\n}\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\";\n\nexport function useGitHubAppInstallations(): UseGitHubAppInstallationsReturn {\n  const [installations, setInstallations] = useState<GitHubAppInstallation[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchInstallations = async () => {\n    // Check if user has a stored authentication token\n    const token = getStoredToken();\n    if (!token) {\n      setLoading(false);\n      setError(\"Not authenticated\");\n      console.warn(\"No stored token found in localStorage\");\n      return;\n    }\n\n    console.log(\"✓ Token found in localStorage, token preview:\", token.substring(0, 50) + \"...\");\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      const authHeaders = getAuthHeaders();\n      const headers: HeadersInit = {\n        \"Content-Type\": \"application/json\",\n        ...authHeaders,\n      };\n\n      console.log(\"Fetching installations with headers:\", {\n        hasAuth: !!(authHeaders as any).Authorization,\n        authHeader: (authHeaders as any).Authorization ? (authHeaders as any).Authorization.substring(0, 50) + \"...\" : \"none\"\n      });\n\n      const response = await fetch(`${API_BASE_URL}/api/v1/github-app/installations`, {\n        method: \"GET\",\n        headers,\n        credentials: \"include\",\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        throw new Error(`Failed to fetch installations: ${response.statusText} - ${errorData.detail || \"\"}`);\n      }\n\n      const data = await response.json();\n      setInstallations(data.installations || []);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Unknown error\";\n      setError(errorMessage);\n      console.error(\"Error fetching GitHub App installations:\", err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchInstallations();\n  }, []);\n\n  return {\n    installations,\n    loading,\n    error,\n    refetch: fetchInstallations,\n  };\n}\n\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AA6BA,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAEjD,SAAS;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,EAAE;IAC9E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,qBAAqB;QACzB,kDAAkD;QAClD,MAAM,QAAQ,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD;QAC3B,IAAI,CAAC,OAAO;YACV,WAAW;YACX,SAAS;YACT,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,QAAQ,GAAG,CAAC,iDAAiD,MAAM,SAAS,CAAC,GAAG,MAAM;QAEtF,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,cAAc,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD;YACjC,MAAM,UAAuB;gBAC3B,gBAAgB;gBAChB,GAAG,WAAW;YAChB;YAEA,QAAQ,GAAG,CAAC,wCAAwC;gBAClD,SAAS,CAAC,CAAC,AAAC,YAAoB,aAAa;gBAC7C,YAAY,AAAC,YAAoB,aAAa,GAAG,AAAC,YAAoB,aAAa,CAAC,SAAS,CAAC,GAAG,MAAM,QAAQ;YACjH;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,gCAAgC,CAAC,EAAE;gBAC9E,QAAQ;gBACR;gBACA,aAAa;YACf;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,UAAU,MAAM,IAAI,IAAI;YACrG;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,iBAAiB,KAAK,aAAa,IAAI,EAAE;QAC3C,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,QAAQ,KAAK,CAAC,4CAA4C;QAC5D,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA,SAAS;IACX;AACF", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/hooks/use-github-app-install.ts"], "sourcesContent": ["\"use client\";\n\nimport { useCallback, useState } from \"react\";\nimport { getAuthHeaders, getStoredToken } from \"@/src/lib/auth/token-storage\";\n\ninterface UseGitHubAppInstallReturn {\n  initiateInstall: () => Promise<void>;\n  loading: boolean;\n  error: string | null;\n}\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\";\n\nexport function useGitHubAppInstall(): UseGitHubAppInstallReturn {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const initiateInstall = useCallback(async () => {\n    // Check if user has a stored authentication token\n    const token = getStoredToken();\n    if (!token) {\n      setError(\"User not authenticated\");\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch(`${API_BASE_URL}/api/v1/github-app/install/authorize`, {\n        method: \"GET\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          ...getAuthHeaders(),\n        },\n        credentials: \"include\",\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to initiate installation: ${response.statusText}`);\n      }\n\n      // Parse the JSON response to get the install URL\n      const data = await response.json();\n      if (data.install_url) {\n        // Redirect to GitHub installation URL\n        window.location.href = data.install_url;\n      } else {\n        throw new Error(\"No installation URL provided by server\");\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Unknown error\";\n      setError(errorMessage);\n      console.error(\"Error initiating GitHub App installation:\", err);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  return {\n    initiateInstall,\n    loading,\n    error,\n  };\n}\n\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAWA,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAEjD,SAAS;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,kDAAkD;QAClD,MAAM,QAAQ,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD;QAC3B,IAAI,CAAC,OAAO;YACV,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,oCAAoC,CAAC,EAAE;gBAClF,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,GAAG,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD,GAAG;gBACrB;gBACA,aAAa;YACf;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,SAAS,UAAU,EAAE;YAC3E;YAEA,iDAAiD;YACjD,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,WAAW,EAAE;gBACpB,sCAAsC;gBACtC,OAAO,QAAQ,CAAC,IAAI,GAAG,KAAK,WAAW;YACzC,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,QAAQ,KAAK,CAAC,6CAA6C;QAC7D,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/dashboard/repositories/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport { But<PERSON> } from \"@/src/components/ui/button\";\nimport { Plus, Loader2, AlertCircle, CheckCircle2, Trash2 } from \"lucide-react\";\nimport { useGitHubAppInstallations } from \"@/src/hooks/use-github-app-installations\";\nimport { useGitHubAppInstall } from \"@/src/hooks/use-github-app-install\";\n\nfunction RepositoriesPage() {\n  const { installations, loading, error, refetch } = useGitHubAppInstallations();\n  const { initiateInstall, loading: installLoading, error: installError } = useGitHubAppInstall();\n  const [deleteError, setDeleteError] = useState<string | null>(null);\n  const [deletingId, setDeletingId] = useState<number | null>(null);\n\n  const handleAddRepository = async () => {\n    await initiateInstall();\n  };\n\n  const handleDeleteInstallation = async (installationId: number) => {\n    if (!confirm(`Are you sure you want to remove installation #${installationId}? This action cannot be undone.`)) {\n      return;\n    }\n\n    try {\n      setDeletingId(installationId);\n      setDeleteError(null);\n      // Refetch installations after successful deletion\n      await refetch();\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : \"Unknown error\";\n      setDeleteError(errorMessage);\n    } finally {\n      setDeletingId(null);\n    }\n  };\n\n  const hasInstallations = installations.length > 0;\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-semibold text-slate-900\">Repositories</h1>\n          <p className=\"text-slate-600 mt-1\">\n            Manage your connected repositories\n          </p>\n        </div>\n        <Button onClick={handleAddRepository} disabled={installLoading}>\n          {installLoading ? (\n            <>\n              <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n              Connecting...\n            </>\n          ) : (\n            <>\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Repository\n            </>\n          )}\n        </Button>\n      </div>\n\n      {installError && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 flex items-start gap-3\">\n          <AlertCircle className=\"h-5 w-5 text-red-600 flex-shrink-0 mt-0.5\" />\n          <div>\n            <h3 className=\"font-medium text-red-900\">Error</h3>\n            <p className=\"text-red-700 text-sm\">{installError}</p>\n          </div>\n        </div>\n      )}\n\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 flex items-start gap-3\">\n          <AlertCircle className=\"h-5 w-5 text-red-600 flex-shrink-0 mt-0.5\" />\n          <div>\n            <h3 className=\"font-medium text-red-900\">Error Loading Installations</h3>\n            <p className=\"text-red-700 text-sm\">{error}</p>\n          </div>\n        </div>\n      )}\n\n      {loading ? (\n        <div className=\"bg-white rounded-lg border border-slate-200 shadow-sm p-8\">\n          <div className=\"flex items-center justify-center gap-2\">\n            <Loader2 className=\"h-5 w-5 animate-spin text-slate-600\" />\n            <p className=\"text-slate-600\">Loading repositories...</p>\n          </div>\n        </div>\n      ) : !hasInstallations ? (\n        <div className=\"bg-white rounded-lg border border-slate-200 shadow-sm p-8\">\n          <div className=\"text-center\">\n            <h2 className=\"text-lg font-medium text-slate-900 mb-2\">No Repositories Connected</h2>\n            <p className=\"text-slate-600 mb-6\">\n              Connect your first repository to get started with code reviews.\n            </p>\n            <Button onClick={handleAddRepository} disabled={installLoading}>\n              {installLoading ? (\n                <>\n                  <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                  Connecting...\n                </>\n              ) : (\n                <>\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Connect Repository\n                </>\n              )}\n            </Button>\n          </div>\n        </div>\n      ) : (\n        <div className=\"space-y-4\">\n          {installations.map((installation) => (\n            <div\n              key={installation.installation_id}\n              className=\"bg-white rounded-lg border border-slate-200 shadow-sm p-6\"\n            >\n              <div className=\"flex items-start justify-between mb-4\">\n                <div>\n                  <div className=\"flex items-center gap-2\">\n                    <h3 className=\"text-lg font-medium text-slate-900\">\n                      Installation #{installation.installation_id}\n                    </h3>\n                    <div className=\"flex items-center gap-1\">\n                      <CheckCircle2 className=\"h-4 w-4 text-green-600\" />\n                      <span className=\"text-sm text-green-700 font-medium\">\n                        {installation.status}\n                      </span>\n                    </div>\n                  </div>\n                  <p className=\"text-sm text-slate-600 mt-1\">\n                    Installed on {new Date(installation.installed_at).toLocaleDateString()}\n                  </p>\n                </div>\n              </div>\n\n              {installation.repositories.length > 0 ? (\n                <div>\n                  <h4 className=\"text-sm font-medium text-slate-900 mb-3\">\n                    Connected Repositories ({installation.repositories.length})\n                  </h4>\n                  <div className=\"space-y-2\">\n                    {installation.repositories.map((repo) => (\n                      <div\n                        key={repo.id}\n                        className=\"flex items-center justify-between p-3 bg-slate-50 rounded border border-slate-100\"\n                      >\n                        <div>\n                          <p className=\"font-medium text-slate-900\">{repo.full_name}</p>\n                          <p className=\"text-xs text-slate-600\">\n                            {repo.private ? \"Private\" : \"Public\"}\n                          </p>\n                        </div>\n                        <a\n                          href={repo.url}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"text-sm text-blue-600 hover:text-blue-700\"\n                        >\n                          View on GitHub →\n                        </a>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              ) : (\n                <p className=\"text-sm text-slate-600\">\n                  No repositories selected for this installation. Visit GitHub to add repositories.\n                </p>\n              )}\n\n              {installation.last_webhook_received && (\n                <p className=\"text-xs text-slate-500 mt-4\">\n                  Last webhook received:{\" \"}\n                  {new Date(installation.last_webhook_received).toLocaleString()}\n                </p>\n              )}\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default RepositoriesPage;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQA,SAAS;IACP,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,4BAAyB,AAAD;IAC3E,MAAM,EAAE,eAAe,EAAE,SAAS,cAAc,EAAE,OAAO,YAAY,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,sBAAmB,AAAD;IAC5F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,MAAM,sBAAsB;QAC1B,MAAM;IACR;IAEA,MAAM,2BAA2B,OAAO;QACtC,IAAI,CAAC,QAAQ,CAAC,8CAA8C,EAAE,eAAe,+BAA+B,CAAC,GAAG;YAC9G;QACF;QAEA,IAAI;YACF,cAAc;YACd,eAAe;YACf,kDAAkD;YAClD,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,eAAe;QACjB,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,mBAAmB,cAAc,MAAM,GAAG;IAEhD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;kCAIrC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAqB,UAAU;kCAC7C,+BACC;;8CACE,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA8B;;yDAInD;;8CACE,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;YAOxC,8BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;YAK1C,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;YAK1C,wBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;4BAAE,WAAU;sCAAiB;;;;;;;;;;;;;;;;uBAGhC,CAAC,iCACH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0C;;;;;;sCACxD,8OAAC;4BAAE,WAAU;sCAAsB;;;;;;sCAGnC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAqB,UAAU;sCAC7C,+BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;qCAQ3C,8OAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;wBAEC,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;wDAAqC;wDAClC,aAAa,eAAe;;;;;;;8DAE7C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,qNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACxB,8OAAC;4DAAK,WAAU;sEACb,aAAa,MAAM;;;;;;;;;;;;;;;;;;sDAI1B,8OAAC;4CAAE,WAAU;;gDAA8B;gDAC3B,IAAI,KAAK,aAAa,YAAY,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;4BAKzE,aAAa,YAAY,CAAC,MAAM,GAAG,kBAClC,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;4CAA0C;4CAC7B,aAAa,YAAY,CAAC,MAAM;4CAAC;;;;;;;kDAE5D,8OAAC;wCAAI,WAAU;kDACZ,aAAa,YAAY,CAAC,GAAG,CAAC,CAAC,qBAC9B,8OAAC;gDAEC,WAAU;;kEAEV,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAA8B,KAAK,SAAS;;;;;;0EACzD,8OAAC;gEAAE,WAAU;0EACV,KAAK,OAAO,GAAG,YAAY;;;;;;;;;;;;kEAGhC,8OAAC;wDACC,MAAM,KAAK,GAAG;wDACd,QAAO;wDACP,KAAI;wDACJ,WAAU;kEACX;;;;;;;+CAdI,KAAK,EAAE;;;;;;;;;;;;;;;qDAsBpB,8OAAC;gCAAE,WAAU;0CAAyB;;;;;;4BAKvC,aAAa,qBAAqB,kBACjC,8OAAC;gCAAE,WAAU;;oCAA8B;oCAClB;oCACtB,IAAI,KAAK,aAAa,qBAAqB,EAAE,cAAc;;;;;;;;uBA5D3D,aAAa,eAAe;;;;;;;;;;;;;;;;AAqE/C;uCAEe", "debugId": null}}, {"offset": {"line": 611, "column": 0}, "map": {"version": 3, "file": "plus.js", "sources": ["file:///home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 657, "column": 0}, "map": {"version": 3, "file": "loader-circle.js", "sources": ["file:///home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/lucide-react/src/icons/loader-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }]];\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('loader-circle', __iconNode);\n\nexport default LoaderCircle;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA;AAa5F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 696, "column": 0}, "map": {"version": 3, "file": "circle-alert.js", "sources": ["file:///home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('circle-alert', __iconNode);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACvE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 757, "column": 0}, "map": {"version": 3, "file": "circle-check.js", "sources": ["file:///home/<USER>/Desktop/Platyfend/platyfend-dashboard/node_modules/lucide-react/src/icons/circle-check.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'm9 12 2 2 4-4', key: 'dzmm74' }],\n];\n\n/**\n * @component @name CircleCheck\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJtOSAxMiAyIDIgNC00IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheck = createLucideIcon('circle-check', __iconNode);\n\nexport default CircleCheck;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}