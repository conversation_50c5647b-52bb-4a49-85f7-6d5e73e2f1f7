{"version": 3, "file": "542.js", "mappings": "06BCmBI,sBAAsB,mIAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAAC,SDtBN,CCsBP,KAA4B,IDtBnBA,CAAQ,CAAiC,EACxE,MACEC,CAAAA,EAAAA,EAAAA,GAAAA,CAACC,CAAAA,EAAAA,eAAAA,CAAAA,CACEF,QAAAA,CAAAA,GAGP,ECgBsD,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,IAAI,CACrC,IAD0C,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GAAmB,OAAO,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,EAAI,OACtE,EAD+E,GAC5C,OAAO,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,MAAQ,CAAC,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,YAAY,CAC5B,aAAa,CAAE,QAAQ,CACvB,iBAAiB,iBACjB,EACA,OAAO,EACf,CAAO,CAFc,CAEZ,KAAK,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,cCvEtB,GDgF8B,KChF9B,+BAA+K,kBCA/K,sCAA+K,2GCM/K,IAAMG,EAAiBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CACxB,2VACA,CACEC,SAAU,CACRC,QAAS,CACPC,QAAS,mEACTC,YACE,qEACFC,QACE,iFACFC,UACE,+DACFC,MAAO,+CACPC,KAAM,kDACNC,UAAW,mDACb,EACAC,KAAM,CACJP,QAAS,iBACTQ,GAAI,sBACJC,GAAI,uBACJC,KAAM,WACR,CACF,EACAC,gBAAiB,CACfZ,QAAS,UACTQ,KAAM,SACR,CACF,GASIK,EAASC,EAAAA,UAAgB,CAC7B,CAAC,WAAEC,CAAS,SAAEf,CAAO,MAAEQ,CAAI,SAAEQ,EAAU,EAAK,CAAE,GAAGC,EAAO,CAAEC,KACxD,IAAMC,EAAOH,EAAUI,EAAAA,EAAIA,CAAG,SAC9B,MACE,UAACD,EAAAA,CACCJ,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACxB,EAAe,SAAEG,OAASQ,YAAMO,CAAU,IACxDG,IAAKA,EACJ,GAAGD,CAAK,EAGf,GAEFJ,EAAOS,WAAW,CAAG,wJElDrB,IAAMC,EAAQT,EAAAA,UAAgB,CAC5B,CAAC,WAAEC,CAAS,MAAES,CAAI,CAAE,GAAGP,EAAO,CAAEC,IAE5B,UAACO,QAAAA,CACCD,KAAMA,EACNT,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,iYACAN,GAEFG,IAAKA,EACJ,GAAGD,CAAK,IAKjBM,EAAMD,WAAW,CAAG,uBCdpB,IAAMI,EAAYZ,EAAAA,UAAgB,CAIhC,CACE,WAAEC,CAAS,aAAEY,EAAc,YAAY,YAAEC,GAAa,CAAI,CAAE,GAAGX,EAAO,CACtEC,IAEA,UAACW,EAAAA,CAAuB,EACtBX,IAAKA,EACLU,WAAYA,EACZD,YAAaA,EACbZ,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,qBACAM,iBAA+B,iBAAmB,iBAClDZ,GAED,GAAGE,CAAK,IAIfS,EAAUJ,WAAW,CAAGO,EAAAA,CAAuB,CAACP,WAAW,2BCnB3D,IAAMQ,EAAQC,EAAAA,EAAmB,GAEZA,EAAsB,CAExBA,EAAAA,EAAoB,CAEvC,IAAMC,EAAcD,EAAAA,EAAqB,CAEnCE,EAAenB,EAAAA,UAAgB,CAGnC,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAC1B,UAACa,EAAAA,EAAsB,EACrBhB,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,0JACAN,GAED,GAAGE,CAAK,CACTC,IAAKA,IAGTe,GAAaX,WAAW,CAAGS,EAAAA,EAAsB,CAACT,WAAW,CAE7D,IAAMY,EAAgBpC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CACvB,mMACA,CACEC,SAAU,CACRoC,KAAM,CACJC,IAAK,oGACLC,OACE,6GACFC,KAAM,gIACNC,MACE,mIACJ,CACF,EACA3B,gBAAiB,CACfuB,KAAM,OACR,CACF,GAOIK,EAAe1B,EAAAA,UAAgB,CAGnC,CAAC,MAAEqB,EAAO,OAAO,WAAEpB,CAAS,UAAErB,CAAQ,CAAE,GAAGuB,EAAO,CAAEC,IACpD,WAACc,EAAAA,WACC,UAACC,EAAAA,CAAAA,GACD,WAACF,EAAAA,EAAsB,EACrBb,IAAKA,EACLH,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACa,EAAc,MAAEC,CAAK,GAAIpB,GACtC,GAAGE,CAAK,WAERvB,EACD,WAACqC,EAAAA,EAAoB,EAAChB,UAAU,qPAC9B,UAAC0B,EAAAA,CAACA,CAAAA,CAAC1B,UAAU,YACb,UAAC2B,OAAAA,CAAK3B,UAAU,mBAAU,oBCjElC,SAAS4B,EAAS,WAChB5B,CAAS,CACT,GAAGE,EACkC,EACrC,MACE,UAAC2B,MAAAA,CACC7B,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oCAAqCN,GAClD,GAAGE,CAAK,EAGf,CD4DAuB,EAAalB,WAAW,CAAGS,EAAAA,EAAsB,CAACT,WAAW,CA8B1CR,EAAAA,UAAgB,CAGjC,CAAC,CAAEC,WAAS,CAAE,GAAGE,EAAO,CAAEC,IAC1B,UAACa,EAAAA,EAAoB,EACnBb,IAAKA,EACLH,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,wCAAyCN,GACtD,GAAGE,CAAK,IAGFK,WAAW,CAAGS,EAAAA,EAAoB,CAACT,WAAW,CAYzDuB,EAVyB/B,UAAgB,CAGvC,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAC1B,UAACa,EAAAA,EAA0B,EACzBb,IAAKA,EACLH,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCN,GAC9C,GAAGE,CAAK,IAGIK,WAAW,CAAGS,EAAAA,EAA0B,CAACT,WAAW,gBEvHrE,IAAMwB,EAAkBC,EAAAA,QAAyB,CAE3CC,EAAUD,EAAAA,IAAqB,CAE/BE,EAAiBF,EAAAA,OAAwB,CAEzCG,EAAiBpC,EAAAA,UAAgB,CAGrC,CAAC,WAAEC,CAAS,YAAEoC,EAAa,CAAC,CAAE,GAAGlC,EAAO,CAAEC,IAC1C,UAAC6B,EAAAA,OAAwB,EACvB7B,IAAKA,EACLiC,WAAYA,EACZpC,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,qYACAN,GAED,GAAGE,CAAK,IAGbiC,EAAe5B,WAAW,CAAGyB,EAAAA,OAAwB,CAACzB,WAAW,CCFjE,IAAM8B,EAAuB,QAcvBC,EAAiBvC,EAAAA,aAAmB,CAAwB,MAElE,SAASwC,IACP,IAAMC,EAAUzC,EAAAA,UAAgB,CAACuC,GACjC,GAAI,CAACE,EACH,MAAM,CADM,KACI,qDAGlB,OAAOA,CACT,CAEA,IAAMC,EAAkB1C,EAAAA,UAAgB,CAQtC,CACE,aACE2C,GAAc,CAAI,CAClBC,KAAMC,CAAQ,CACdC,aAAcC,CAAW,WACzB9C,CAAS,OACT+C,CAAK,UACLpE,CAAQ,CACR,GAAGuB,EACJ,CACDC,KAEA,IAAM6C,EAAWC,SNlELA,EMkEgBA,GNjExB,CAACD,EAAUE,EAAY,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAiBzC,OAAOH,CACT,IMgDU,CAACI,EAAYC,EAAc,CAAGtD,EAAAA,QAAc,CAAC,IAI7C,CAACuD,EAAOC,EAAS,CAAGxD,EAAAA,QAAc,CAAC2C,GACnCC,EAAOC,GAAYU,EACnBE,EAAUzD,EAAAA,WAAiB,CAC/B,IACE,IAAM0D,EAA6B,YAAjB,OAAOC,EAAuBA,EAAMf,GAAQe,EAC1DZ,EACFA,EAAYW,GAEZF,EAASE,GAIXE,CAPiB,QAORC,MAAM,CAAG,GAAGC,cAAuBJ,MAAH,CAAC,GAAY,iBAA4C,CAA1B,CAE1E,CAF4EK,EAE9DnB,EAAK,EAIrB5C,EAAAA,SAAe,CAAC,KACd,IAAMgE,EAAe,KACnB,IAAMC,EAAQC,OAAOC,UAAU,CAC3BF,GAAS,MAAQA,EAAQ,MAAQrB,EAEnCa,GAAQ,CAFiC,IAGhCQ,GAAS,OAASrB,EAAD,CAAUK,GAEpCQ,EAFmC,GAIvC,EAJkD,OAMlDS,OAAOE,gBAAgB,CAAC,SAAUJ,GAClCA,IAEO,IAAME,OAAOG,iBAFY,EAEO,CAAC,SAAUL,EACpD,EAAG,CAACpB,EAAMa,EAASR,EAAS,EAG5B,IAAMqB,EAAgBtE,EAAAA,WAAiB,CAAC,IAC/BiD,EACHK,EAAc,GAAU,CAACV,GACzBa,EAAQ,GAAU,CAACb,GACtB,CAACK,EAAUQ,EAASH,EAAc,EAGrCtD,EAAAA,SAAe,CAAC,KACd,IAAMuE,EAAiBC,IA7FK,MA+FxBA,EAAMC,GAAG,GACRD,EADaE,OACA,EAAIF,EAAMG,OAAAA,GACxB,CACAH,EAAMI,KAFLJ,SAEmB,GACpBF,IAEJ,EAGA,OADAJ,OAAOE,gBAAgB,CAAC,UAAWG,GAC5B,IAAML,OAAOG,mBAAmB,CAAC,UAAWE,EACrD,EAAG,CAACD,EAAc,EAIlB,IAAMO,EAAQjC,EAAO,WAAa,YAE5BkC,EAAe9E,EAAAA,OAAa,CAChC,IAAO,QACL6E,OACAjC,UACAa,WACAR,aACAI,gBACAC,gBACAgB,EACF,EACA,CAACO,EAAOjC,EAAMa,EAASR,EAAUI,EAAYC,EAAegB,EAAc,EAG5E,MACE,UAAC/B,EAAewC,QAAQ,EAACpB,MAAOmB,WAC9B,UAAC9C,EAAeA,CAACgD,YAADhD,EAAgB,WAC9B,UAACF,MAAAA,CACCkB,MACE,CACE,kBAtIM,CAsIaiC,OACnB,yBAtIa,CAsIaC,OAC1B,yBAA0B5C,EAC1B,uBAtIW,CAsIa6C,MACxB,GAAGnC,CACL,EAEF/C,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,oFACAN,GAEFG,IAAKA,EACJ,GAAGD,CAAK,UAERvB,OAKX,GAEF8D,EAAgBlC,WAAW,CAAG,kBAE9B,IAAM4E,EAAUpF,EAAAA,UAAgB,CAQ9B,CACE,MACEqB,EAAO,MAAM,SACbnC,EAAU,SAAS,aACnBmG,EAAc,WAAW,WACzBpF,CAAS,UACTrB,CAAQ,CACR,GAAGuB,EACJ,CACDC,KAEA,GAAM,UAAE6C,CAAQ,OAAE4B,CAAK,YAAExB,CAAU,eAAEC,CAAa,CAAE,CAAGd,UAEvD,QAA4B,CAAxB6C,EAEA,UAACvD,MAAAA,CACC7B,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,8EACAN,GAEFG,IAAKA,EACJ,GAAGD,CAAK,UAERvB,IAKHqE,EAEA,QAFU,EAETjC,EAAKA,CAAC4B,EAAD5B,GAAOqC,EAAYP,aAAcQ,EAAgB,GAAGnD,CAAK,UAC7D,UAACuB,EAAYA,CACX4D,SADW5D,MACE,UACb6D,cAAY,OACZtF,UAAU,+EACV+C,MACE,CACE,kBAAmBV,CACrB,EAEFjB,KAAMA,WAEN,UAACS,MAAAA,CAAI7B,UAAU,uCAA+BrB,QAOpD,WAACkD,MAAAA,CACC1B,IAAKA,EACLH,UAAU,qDACVuF,aAAYX,EACZY,mBAA4B,cAAVZ,EAAwBQ,EAAc,GACxDK,eAAcxG,EACdyG,YAAWtE,YAGX,UAACS,MAAAA,CACC7B,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,4EACA,oDACA,yCACA,qCACY,aAAZrB,GAAsC,UAAZA,EACtB,uFACA,4DAGR,UAAC4C,MAAAA,CACC7B,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,mGACA,oDACAc,WACI,mKACA,sKAEQ,CADZ,YACAnC,GAAsC,UAAZA,EACtB,2BAFkD,qEAGlD,0HACJe,GAED,GAAGE,CAAK,UAET,UAAC2B,MAAAA,CACCwD,eAAa,UACbrF,UAAU,yNAETrB,QAKX,GAEFwG,EAAQ5E,WAAW,CAAG,UAEtB,IAAMoF,EAAiB5F,EAAAA,UAAgB,CAGrC,CAAC,WAAEC,CAAS,SAAE4F,CAAO,CAAE,GAAG1F,EAAO,CAAEC,KACnC,GAAM,eAAEkE,CAAa,CAAE,CAAG9B,IAE1B,MACE,WAACzC,EAAAA,CAAMA,CAAAA,CACLK,IAAKA,EACLkF,eAAa,UACbpG,QAAQ,QACRQ,KAAK,OACLO,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,UAAWN,GACzB4F,QAAS,IACPA,IAAUrB,GACVF,GACF,EACC,GAAGnE,CAAK,WAET,UAAC2F,EAAAA,CAASA,CAAAA,CAAAA,GACV,UAAClE,OAAAA,CAAK3B,UAAU,mBAAU,qBAGhC,GACA2F,EAAepF,WAAW,CAAG,iBA6B7BuF,EA3BoB/F,UAAgB,CAGlC,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,KAC1B,GAAM,eAAEkE,CAAa,CAAE,CAAG9B,IAE1B,MACE,UAACwD,SAAAA,CACC5F,IAAKA,EACLkF,eAAa,OACbW,aAAW,iBACXC,SAAU,CAAC,EACXL,QAASvB,EACT6B,MAAM,iBACNlG,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,kPACA,6EACA,yHACA,0JACA,4DACA,4DACAN,GAED,GAAGE,CAAK,EAGf,GACYK,WAAW,CAAG,cAELR,EAAAA,UAAgB,CAGnC,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAExB,UAACgG,OAAAA,CACChG,IAAKA,EACLH,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,wDACA,+QACAN,GAED,GAAGE,CAAK,IAIFK,WAAW,CAAG,eAENR,EAAAA,UAAgB,CAGnC,CAAC,CAAEC,WAAS,CAAE,GAAGE,EAAO,CAAEC,IAExB,UAACK,EAAKA,CACJL,EADIK,EACCL,EACLkF,eAAa,QACbrF,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,4FACAN,GAED,GAAGE,CAAK,IAIFK,WAAW,CAAG,eAE3B,IAAM6F,EAAgBrG,EAAAA,UAAgB,CAGpC,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAExB,UAAC0B,MAAAA,CACC1B,IAAKA,EACLkF,eAAa,SACbrF,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0BAA2BN,GACxC,GAAGE,CAAK,IAIfkG,EAAc7F,WAAW,CAAG,gBAE5B,IAAM8F,EAAgBtG,EAAAA,UAAgB,CAGpC,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAExB,UAAC0B,MAAAA,CACC1B,IAAKA,EACLkF,eAAa,SACbrF,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0BAA2BN,GACxC,GAAGE,CAAK,IAIfmG,EAAc9F,WAAW,CAAG,gBAEHR,EAAAA,UAAgB,CAGvC,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAExB,UAACQ,EAASA,CACRR,IAAKA,EACLkF,eAAa,YACbrF,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCN,GAC9C,GAAGE,CAAK,IAIEK,WAAW,CAAG,mBAE/B,IAAM+F,EAAiBvG,EAAAA,UAAgB,CAGrC,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAExB,UAAC0B,MAAAA,CACC1B,IAAKA,EACLkF,eAAa,UACbrF,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,iGACAN,GAED,GAAGE,CAAK,IAIfoG,EAAe/F,WAAW,CAAG,iBAE7B,IAAMgG,EAAexG,EAAAA,UAAgB,CAGnC,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAExB,UAAC0B,MAAAA,CACC1B,IAAKA,EACLkF,eAAa,QACbrF,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,4CAA6CN,GAC1D,GAAGE,CAAK,IAIfqG,EAAahG,WAAW,CAAG,eAE3B,IAAMiG,EAAoBzG,EAAAA,UAAgB,CAGxC,CAAC,WAAEC,CAAS,SAAEC,GAAU,CAAK,CAAE,GAAGC,EAAO,CAAEC,KAC3C,IAAMC,EAAOH,EAAUI,EAAAA,EAAIA,CAAG,MAE9B,MACE,UAACD,EAAAA,CACCD,IAAKA,EACLkF,eAAa,cACbrF,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,qOACA,8EACAN,GAED,GAAGE,CAAK,EAGf,GACAsG,EAAkBjG,WAAW,CAAG,oBAELR,EAAAA,UAAgB,CAGzC,CAAC,WAAEC,CAAS,SAAEC,GAAU,CAAK,CAAE,GAAGC,EAAO,CAAEC,KAC3C,IAAMC,EAAOH,EAAUI,EAAAA,EAAIA,CAAG,SAE9B,MACE,UAACD,EAAAA,CACCD,IAAKA,EACLkF,eAAa,eACbrF,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,2RAEA,CADA,+CAEA,GAFkD,oCAGlDN,GAED,GAAGE,CAAK,EAGf,GACmBK,WAAW,CAAG,qBAEjC,IAAMkG,EAAsB1G,EAAAA,UAAgB,CAG1C,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAC1B,UAAC0B,MAAAA,CACC1B,IAAKA,EACLkF,eAAa,gBACbrF,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iBAAkBN,GAC/B,GAAGE,CAAK,IAGbuG,EAAoBlG,WAAW,CAAG,sBAElC,IAAMmG,EAAc3G,EAAAA,UAAgB,CAGlC,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAC1B,UAACwG,KAAAA,CACCxG,IAAKA,EACLkF,eAAa,OACbrF,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsCN,GACnD,GAAGE,CAAK,IAGbwG,EAAYnG,WAAW,CAAG,cAE1B,IAAMqG,EAAkB7G,EAAAA,UAAgB,CAGtC,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAC1B,UAAC0G,KAAAA,CACC1G,IAAKA,EACLkF,eAAa,YACbrF,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,2BAA4BN,GACzC,GAAGE,CAAK,IAGb0G,EAAgBrG,WAAW,CAAG,kBAE9B,IAAMuG,EAA4B/H,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CACnC,ozBACA,CACEC,SAAU,CACRC,QAAS,CACPC,QAAS,+DACTE,QACE,8KACJ,EACAK,KAAM,CACJP,QAAS,cACTQ,GAAI,cACJC,GAAI,iDACN,CACF,EACAE,gBAAiB,CACfZ,QAAS,UACTQ,KAAM,SACR,CACF,GAGIsH,EAAoBhH,EAAAA,UAAgB,CAQxC,CACE,SACEE,GAAU,CAAK,UACf+G,GAAW,CAAK,SAChB/H,EAAU,SAAS,MACnBQ,EAAO,SAAS,CAChBwH,SAAO,WACPjH,CAAS,CACT,GAAGE,EACJ,CACDC,KAEA,IAAMC,EAAOH,EAAUI,EAAAA,EAAIA,CAAG,SACxB,UAAE2C,CAAQ,OAAE4B,CAAK,CAAE,CAAGrC,IAEtBwD,EACJ,UAAC3F,EAAAA,CACCD,IAAKA,EACLkF,eAAa,cACb6B,YAAWzH,EACX0H,cAAaH,EACbhH,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACwG,EAA0B,SAAE7H,OAASQ,CAAK,GAAIO,GAC3D,GAAGE,CAAK,UAIb,GAIuB,CAJnB,KAAU,IAIV,OAAO+G,IACTA,EAAU,CACRtI,SAAUsI,EACZ,EAIA,WAAChF,EAAOA,KAAAA,MACN,UAACC,EAAcA,CAACjC,OAAO,IAARiC,SAAU6D,IACzB,UAAC5D,EAAcA,CACbf,KAAK,MADQe,EAEbiF,MAAM,SACNC,OAAkB,cAAVzC,GAAyB5B,EAChC,GAAGiE,CAAO,OAhBRlB,CAoBX,EAEFgB,GAAkBxG,WAAW,CAAG,oBAENR,EAAAA,UAAgB,CAMxC,CAAC,WAAEC,CAAS,SAAEC,GAAU,CAAK,CAAEqH,eAAc,CAAK,CAAE,GAAGpH,EAAO,CAAEC,KAChE,IAAMC,EAAOH,EAAUI,EAAAA,EAAIA,CAAG,SAE9B,MACE,UAACD,EAAAA,CACCD,IAAKA,EACLkF,eAAa,cACbrF,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,iVAEA,CADA,+CAEA,GAFkD,qCAGlD,+CACA,0CACA,uCACAgH,GACE,2LACFtH,GAED,GAAGE,CAAK,EAGf,GACkBK,WAAW,CAAG,oBAEPR,EAAAA,UAAgB,CAGvC,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAC1B,UAAC0B,MAAAA,CACC1B,IAAKA,EACLkF,eAAa,aACbrF,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,yKACA,2HACA,wCACA,+CACA,0CACA,uCACAN,GAED,GAAGE,CAAK,IAGIK,WAAW,CAAG,mBAEHR,EAAAA,UAAgB,CAK1C,CAAC,CAAEC,WAAS,UAAEuH,GAAW,CAAK,CAAE,GAAGrH,EAAO,CAAEC,KAE5C,IAAM6D,EAAQjE,EAAAA,OAAa,CAAC,IACnB,GAAGyH,KAAKC,KAAK,CAAiB,GAAhBD,KAAKE,MAAM,IAAW,GAAG,CAAC,CAAC,CAC/C,EAAE,EAEL,MACE,WAAC7F,MAAAA,CACC1B,IAAKA,EACLkF,eAAa,gBACbrF,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8CAA+CN,GAC5D,GAAGE,CAAK,WAERqH,GACC,UAAC3F,EAAQA,CACP5B,KADO4B,KACG,oBACVyD,eAAa,uBAGjB,UAACzD,EAAQA,CACP5B,KADO4B,KACG,sCACVyD,eAAa,qBACbtC,MACE,CACE,mBAAoBiB,CACtB,MAKV,GACoBzD,WAAW,CAAG,sBAEXR,EAAAA,UAAgB,CAGrC,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAC1B,UAACwG,KAAAA,CACCxG,IAAKA,EACLkF,eAAa,WACbrF,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,iGACA,uCACAN,GAED,GAAGE,CAAK,IAGEK,WAAW,CAAG,iBAM7BoH,EAJ2B5H,UAAgB,CAGzC,CAAC,CAAE,GAAGG,EAAO,CAAEC,IAAQ,UAAC0G,KAAAA,CAAG1G,IAAKA,EAAM,GAAGD,CAAK,IAC7BK,WAAW,CAAG,qBA8BjCqH,EA5B6B7H,UAAgB,CAO3C,CAAC,SAAEE,GAAU,CAAK,MAAER,EAAO,IAAI,UAAEuH,CAAQ,WAAEhH,CAAS,CAAE,GAAGE,EAAO,CAAEC,KAClE,IAAMC,EAAOH,EAAUI,EAAAA,EAAIA,CAAG,IAE9B,MACE,UAACD,EAAAA,CACCD,IAAKA,EACLkF,eAAa,kBACb6B,YAAWzH,EACX0H,cAAaH,EACbhH,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,8eACA,yFACS,OAATb,GAAiB,UACR,OAATA,GAAiB,UACjB,uCACAO,GAED,GAAGE,CAAK,EAGf,GACqBK,WAAW,CAAG,4MChvBnC,IAAMsH,GAAS9H,EAAAA,UAAgB,CAG7B,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAC1B,UAAC2H,GAAAA,EAAoB,EACnB3H,IAAKA,EACLH,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,gEACAN,GAED,GAAGE,CAAK,IAGb2H,GAAOtH,WAAW,CAAGuH,GAAAA,EAAoB,CAACvH,WAAW,CAErD,IAAMwH,GAAchI,EAAAA,UAAgB,CAGlC,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAC1B,UAAC2H,GAAAA,EAAqB,EACpB3H,IAAKA,EACLH,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8BAA+BN,GAC5C,GAAGE,CAAK,IAGb6H,GAAYxH,WAAW,CAAGuH,GAAAA,EAAqB,CAACvH,WAAW,CAE3D,IAAMyH,GAAiBjI,EAAAA,UAAgB,CAGrC,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAC1B,UAAC2H,GAAAA,EAAwB,EACvB3H,IAAKA,EACLH,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,uEACAN,GAED,GAAGE,CAAK,IAGb8H,GAAezH,WAAW,CAAGuH,GAAAA,EAAwB,CAACvH,WAAW,oDCvCjE,IAAM0H,GAAeC,GAAAA,EAA0B,CAEzCC,GAAsBD,GAAAA,EAA6B,CAE/BA,GAAAA,EAA2B,CAE1BA,GAAAA,EAA4B,CAE/BA,GAAAA,EAAyB,CAElBA,GAAAA,EAAgC,CAqB/DE,EAnB+BrI,UAAgB,CAK7C,CAAC,WAAEC,CAAS,OAAEqI,CAAK,UAAE1J,CAAQ,CAAE,GAAGuB,EAAO,CAAEC,IAC3C,WAAC+H,GAAAA,EAAgC,EAC/B/H,IAAKA,EACLH,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,uIACA+H,GAAS,OACTrI,GAED,GAAGE,CAAK,WAERvB,EACD,UAAC2J,GAAAA,CAAYA,CAAAA,CAACtI,UAAU,wBAGLO,WAAW,CAChC2H,GAAAA,EAAgC,CAAC3H,WAAW,CAEfR,EAAAA,UAAgB,CAG7C,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAC1B,UAAC+H,GAAAA,EAAgC,EAC/B/H,IAAKA,EACLH,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,wbACAN,GAED,GAAGE,CAAK,IAGUK,WAAW,CAChC2H,GAAAA,EAAgC,CAAC3H,WAAW,CAE9C,IAAMgI,GAAsBxI,EAAAA,UAAgB,CAG1C,CAAC,WAAEC,CAAS,YAAEoC,EAAa,CAAC,CAAE,GAAGlC,EAAO,CAAEC,IAC1C,UAAC+H,GAAAA,EAA4B,WAC3B,UAACA,GAAAA,EAA6B,EAC5B/H,IAAKA,EACLiC,WAAYA,EACZpC,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,wbACAN,GAED,GAAGE,CAAK,MAIfqI,GAAoBhI,WAAW,CAAG2H,GAAAA,EAA6B,CAAC3H,WAAW,CAE3E,IAAMiI,GAAmBzI,EAAAA,UAAgB,CAKvC,CAAC,WAAEC,CAAS,OAAEqI,CAAK,CAAE,GAAGnI,EAAO,CAAEC,IACjC,UAAC+H,GAAAA,EAA0B,EACzB/H,IAAKA,EACLH,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,kOACA+H,GAAS,OACTrI,GAED,GAAGE,CAAK,IAGbsI,GAAiBjI,WAAW,CAAG2H,GAAAA,EAA0B,CAAC3H,WAAW,CAEpCR,EAAAA,UAAgB,CAG/C,CAAC,WAAEC,CAAS,UAAErB,CAAQ,SAAE8J,CAAO,CAAE,GAAGvI,EAAO,CAAEC,IAC7C,WAAC+H,GAAAA,EAAkC,EACjC/H,IAAKA,EACLH,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,uOACAN,GAEFyI,QAASA,EACR,GAAGvI,CAAK,WAET,UAACyB,OAAAA,CAAK3B,UAAU,wEACd,UAACkI,GAAAA,EAAmC,WAClC,UAACQ,GAAAA,CAAKA,CAAAA,CAAC1I,UAAU,gBAGpBrB,MAGoB4B,WAAW,CAClC2H,GAAAA,EAAkC,CAAC3H,WAAW,CAElBR,EAAAA,UAAgB,CAG5C,CAAC,WAAEC,CAAS,CAAErB,UAAQ,CAAE,GAAGuB,EAAO,CAAEC,IACpC,WAAC+H,GAAAA,EAA+B,EAC9B/H,IAAKA,EACLH,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,uOACAN,GAED,GAAGE,CAAK,WAET,UAACyB,OAAAA,CAAK3B,UAAU,wEACd,UAACkI,GAAAA,EAAmC,WAClC,UAACS,GAAAA,CAAMA,CAAAA,CAAC3I,UAAU,6BAGrBrB,MAGiB4B,WAAW,CAAG2H,GAAAA,EAA+B,CAAC3H,WAAW,CAE/E,IAAMqI,GAAoB7I,EAAAA,UAAgB,CAKxC,CAAC,WAAEC,CAAS,OAAEqI,CAAK,CAAE,GAAGnI,EAAO,CAAEC,IACjC,UAAC+H,GAAAA,EAA2B,EAC1B/H,IAAKA,EACLH,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CACX,oCACA+H,GAAS,OACTrI,GAED,GAAGE,CAAK,IAGb0I,GAAkBrI,WAAW,CAAG2H,GAAAA,EAA2B,CAAC3H,WAAW,CAEvE,IAAMsI,GAAwB9I,EAAAA,UAAgB,CAG5C,CAAC,WAAEC,CAAS,CAAE,GAAGE,EAAO,CAAEC,IAC1B,UAAC+H,GAAAA,EAA+B,EAC9B/H,IAAKA,EACLH,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,2BAA4BN,GACzC,GAAGE,CAAK,ICzIN,SAAS4I,KACd,GAAM,MAAEC,CAAI,QAAEC,CAAM,CAAE,CAAGC,CAAAA,EAAAA,GAAAA,CAAAA,CAAOA,GAC1BC,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAExB,GAAI,CAACJ,EACH,IADS,GACF,KAGT,IAAMK,EAAYL,EAAKM,KAAK,EAAIN,EAAKO,KAAK,EAAEC,MAAM,IAAI,CAAC,EAAE,EAAI,OACvDC,EAAWT,EAAKU,IAAI,CACtBV,EAAKU,IAAI,CACNF,KAAK,CAAC,KACNG,MAAM,CAAC,GAAOC,EAAEC,MAAM,CAAG,GACzBC,GAAG,CAAEF,GAAMA,CAAC,CAAC,EAAE,EACfG,IAAI,CAAC,IACLC,WAAW,GACdX,EAAUY,SAAS,CAAC,EAAG,GAAGD,WAAW,GAOzC,MACE,UAACrD,EAAWA,SAAAA,CACV,UAACE,EAAeA,UACd,GADcA,EACd,MAACqB,GAAYA,SAAAA,EACX,UAACE,GAAmBA,CAAClI,OAAO,QAARkI,KAClB,WAACpB,EAAiBA,CAChBtH,KAAK,KACLO,IAFgB+G,MAEN,iGAEV,WAACc,GAAMA,CAAC7H,EAAD6H,QAAW,+BAChB,UAACE,GAAWA,CAACkC,IAAKlB,EAAKmB,CAAXnC,SAAqB,OAAIoC,EAAWC,IAAKrB,EAAKU,IAAI,EAAIL,IAClE,UAACpB,GAAcA,CAAChI,UAADgI,sBACZwB,OAGL,WAAC3H,MAAAA,CAAI7B,UAAU,wDACb,UAAC2B,OAAAA,CAAK3B,UAAU,gDAAwC+I,EAAKU,IAAI,EAAIL,IACrE,UAACzH,OAAAA,CAAK3B,UAAU,0CAAkC+I,EAAKO,KAAK,EAAI,CAAC,CAAC,EAAEF,EAAAA,CAAW,WAIrF,WAACb,GAAmBA,CAClBvI,UAAU,KADQuI,yDAElBnH,KAAK,SACLgG,MAAM,MACNhF,WAAY,YAEZ,UAACwG,GAAiBA,CAAC5I,UAAU,GAAX4I,wBAChB,WAAC/G,MAAAA,CAAI7B,UAAU,kEACb,WAAC6H,GAAMA,CAAC7H,EAAD6H,QAAW,+BAChB,UAACE,GAAWA,CAACkC,IAAKlB,EAAKmB,CAAXnC,SAAqB,OAAIoC,EAAWC,IAAKrB,EAAKU,IAAI,EAAIL,IAClE,UAACpB,GAAcA,CAAChI,UAADgI,sBACZwB,OAGL,WAAC3H,MAAAA,CAAI7B,UAAU,wDACb,UAAC2B,OAAAA,CAAK3B,UAAU,gDAAwC+I,EAAKU,IAAI,EAAIL,IACrE,UAACzH,OAAAA,CAAK3B,UAAU,0CAAkC+I,EAAKO,KAAK,EAAI,CAAC,CAAC,EAAEF,EAAAA,CAAW,WAIrF,UAACP,GAAqBA,CAAAA,GACtB,UAACL,GAAgBA,CADKK,OACG,KAARL,QACf,WAAC6B,IAAIA,CAACC,KAAK,KAAND,gBAA2BrK,UAAU,kGACxC,UAACuK,GAAAA,CAAIA,CAAAA,CAACvK,UAAU,oDAAoD,eAIxE,UAAC6I,GAAqBA,CAAAA,GACtB,WAACL,GADqBK,CACJjD,QAtDN,CAsDe4E,GAAVhC,CArDzBQ,IACAE,EAAOuB,IAAI,CAAC,SACd,EAmDoDzK,UAAU,gFAClD,UAAC0K,GAAAA,CAAMA,CAAAA,CAAC1K,UAAU,oDAAoD,uBAQpF,CD2DA6I,GAAsBtI,WAAW,CAAG2H,GAAAA,EAA+B,CAAC3H,WAAW,CEvI/E,IAAMoK,GAAqB,IAAM,CAC/B,CAAEzE,MAAO,OAAQ0E,IAAK,aAAchL,KAAMiL,EAAAA,CAAIA,EAC9C,CAAE3E,MAAO,eAAgB0E,IAAK,0BAA2BhL,KAAMkL,EAAAA,CAASA,EACxE,CAAE5E,MAAO,eAAgB0E,IAAK,0BAA2BhL,KAAMmL,EAAAA,CAAIA,CAAEC,QAAQ,CAAK,EAClF,CAAE9E,MAAO,UAAW0E,IAAK,qBAAsBhL,KAAMqL,EAAAA,CAASA,CAAED,QAAQ,CAAK,EAC7E,CAAE9E,MAAO,YAAa0E,IAAK,uBAAwBhL,KAAMsL,EAAAA,CAAQA,EACjE,CAAEhF,MAAO,wBAAyB0E,IAAK,sBAAuBhL,KAAMuL,EAAAA,CAAQA,EAC5E,CAAEjF,MAAO,eAAgB0E,IAAK,0BAA2BhL,KAAMwL,EAAAA,CAAUA,EAC1E,CAEKC,GAAc,CAClB,CAAEnF,MAAO,OAAQ0E,IAAK,QAAShL,KAAMsL,EAAAA,CAAQA,EAC7C,CAAEhF,MAAO,UAAW0E,IAAK,WAAYhL,KAAM0L,EAAAA,CAAUA,EACtD,CAEM,SAASC,KACd,IAAMC,EAAWC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,GACtBC,EAAkBf,KAExB,MACE,WAACxF,EAAOA,CAACnF,IAADmF,MAAW,sEACjB,UAACiB,EAAaA,CAACpG,UAAU,mDACvB,UAAC6B,MAAAA,CAAI7B,UAAU,qBACb,UAAC2L,KAAAA,CAAG3L,UAAU,iCAAwB,kBAI1C,WAACsG,EAAcA,CAACtG,UAAU,CAAXsG,gCAEb,WAACC,EAAYA,UAAAA,CACX,UAACC,EAAiBA,CAACxG,UAAU,IAAXwG,8EAAoF,eAGtG,UAACC,EAAmBA,UAClB,OADkBA,CAClB,EAACC,EAAWA,CAAC1G,QAAD0G,EAAW,qBACpBgF,EAAgB7B,GAAG,CAAC,GACnB,UAACjD,EAAeA,UACd,GADcA,EACd,KAACG,EAAiBA,CAChB9G,OAAO,IACP+G,GAFgBD,MAENyE,IAAaI,EAAKhB,GAAG,EAAkB,eAAbgB,EAAKhB,GAAG,EAAqBY,EAASK,UAAU,CAACD,EAAKhB,GAAG,EAC7F5K,UAAU,iPAEV,WAACqK,IAAIA,CAACC,KAAMsB,EAAKhB,GAAZP,CAAiBrK,UAAU,qCAC9B,UAAC4L,EAAKhM,IAAI,EAACI,UAAU,iBACrB,UAAC2B,OAAAA,CAAK3B,UAAU,kBAAU4L,EAAK1F,KAAK,GACnC0F,EAAKZ,MAAM,EAAI,UAACc,EAAAA,CAAIA,CAAAA,CAAC9L,UAAU,gCAThB4L,EAAK1F,KAAK,UAmBxC,WAACK,EAAYA,CAACvG,SAADuG,CAAW,iBACtB,UAACC,EAAiBA,CAACxG,UAAU,IAAXwG,8EAAoF,cAGtG,UAACC,EAAmBA,UAClB,OADkBA,CAClB,EAACC,EAAWA,CAAC1G,QAAD0G,EAAW,qBACpB2E,GAAYxB,GAAG,CAAC,GACf,UAACjD,EAAeA,UACd,GADcA,EACd,KAACG,EAAiBA,CAChB9G,OAAO,IACPD,GAFgB+G,OAEN,2IAEV,WAACsD,IAAIA,CAACC,KAAMsB,EAAKhB,GAAZP,WACH,UAACuB,EAAKhM,IAAI,EAACI,UAAU,iBACrB,UAAC2B,OAAAA,UAAMiK,EAAK1F,KAAK,SAPD0F,EAAK1F,KAAK,aAiB1C,UAACG,EAAaA,UACZ,CADYA,EACZ,OAACyC,GAAYA,CAAAA,OAIrB,CAJqBA,gBCvGd,SAASjK,GAAgB,UAAEF,CAAQ,CAAiC,EACzE,MACE,UAAC8D,EAAeA,UACd,GADcA,EACd,MAACZ,MAAAA,CAAI7B,UAAU,gEACb,UAACuL,GAAgBA,CAAAA,GAGjB,WAAC1J,MAAAA,CAAI7B,UAAU,2DAEb,UAAC6B,MAAAA,CAAI7B,UAAU,oDACb,UAAC2F,EAAcA,UACb,EADaA,CACb,QAAC7F,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,QAAQQ,KAAK,OAAOO,UAAU,iBAC5C,UAAC+L,GAAAA,CAAIA,CAAAA,CAAC/L,UAAU,YAChB,UAAC2B,OAAAA,CAAK3B,UAAU,mBAAU,uBAIhC,UAACmG,OAAAA,CAAKnG,UAAU,4EACd,UAAC6B,MAAAA,CAAI7B,UAAU,oCACZrB,aAOf", "sources": ["webpack://platyfend/src/app/dashboard/layout.tsx", "webpack://platyfend/sentry-wrapper-module", "webpack://platyfend/?f4c0", "webpack://platyfend/?934a", "webpack://platyfend/./src/components/ui/button.tsx", "webpack://platyfend/./src/hooks/use-mobile.ts", "webpack://platyfend/./src/components/ui/input.tsx", "webpack://platyfend/./src/components/ui/separator.tsx", "webpack://platyfend/./src/components/ui/sheet.tsx", "webpack://platyfend/./src/components/ui/skeleton.tsx", "webpack://platyfend/./src/components/ui/tooltip.tsx", "webpack://platyfend/./src/components/ui/sidebar.tsx", "webpack://platyfend/./src/components/ui/avatar.tsx", "webpack://platyfend/./src/components/ui/dropdown-menu.tsx", "webpack://platyfend/./src/components/dashboard/user-dropdown.tsx", "webpack://platyfend/./src/components/dashboard/sidebar.tsx", "webpack://platyfend/./src/components/dashboard/dashboard-layout.tsx"], "sourcesContent": ["import { DashboardLayout } from '@/src/components/dashboard/dashboard-layout'\n\nexport default function Layout({ children }: { children: React.ReactNode }) {\n  return (\n    <DashboardLayout>\n      {children}\n    </DashboardLayout>\n  );\n}\n", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard',\n        componentType: 'Layout',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "import(/* webpackMode: \"eager\", webpackExports: [\"DashboardLayout\"] */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/components/dashboard/dashboard-layout.tsx\");\n", "import(/* webpackMode: \"eager\", webpackExports: [\"DashboardLayout\"] */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/components/dashboard/dashboard-layout.tsx\");\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/src/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        platyfend: \"bg-platyfend-500 hover:bg-platyfend-600 shadow-sm\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n", "import { useEffect, useState } from \"react\"\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = useState(false)\n\n  useEffect(() => {\n    const checkDevice = () => {\n      // Updated threshold to 1024px to match lg: breakpoint\n      // This ensures mobile behavior on tablets and smaller screens\n      setIsMobile(window.innerWidth < 1024)\n    }\n\n    checkDevice()\n    window.addEventListener(\"resize\", checkDevice)\n\n    return () => {\n      window.removeEventListener(\"resize\", checkDevice)\n    }\n  }, [])\n\n  return isMobile\n}\n", "import * as React from \"react\"\n\nimport { cn } from \"@/src/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n", "import * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/src/lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }\n", "import * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\nimport * as React from \"react\"\n\nimport { cn } from \"@/src/lib/utils\"\n\nconst Sheet = SheetPrimitive.Root\n\nconst SheetTrigger = SheetPrimitive.Trigger\n\nconst SheetClose = SheetPrimitive.Close\n\nconst SheetPortal = SheetPrimitive.Portal\n\nconst SheetOverlay = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\n\nconst sheetVariants = cva(\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n  {\n    variants: {\n      side: {\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n        bottom:\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n        right:\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\n      },\n    },\n    defaultVariants: {\n      side: \"right\",\n    },\n  }\n)\n\ninterface SheetContentProps\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\n  VariantProps<typeof sheetVariants> { }\n\nconst SheetContent = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Content>,\n  SheetContentProps\n>(({ side = \"right\", className, children, ...props }, ref) => (\n  <SheetPortal>\n    <SheetOverlay />\n    <SheetPrimitive.Content\n      ref={ref}\n      className={cn(sheetVariants({ side }), className)}\n      {...props}\n    >\n      {children}\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </SheetPrimitive.Close>\n    </SheetPrimitive.Content>\n  </SheetPortal>\n))\nSheetContent.displayName = SheetPrimitive.Content.displayName\n\nconst SheetHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetHeader.displayName = \"SheetHeader\"\n\nconst SheetFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetFooter.displayName = \"SheetFooter\"\n\nconst SheetTitle = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\n    {...props}\n  />\n))\nSheetTitle.displayName = SheetPrimitive.Title.displayName\n\nconst SheetDescription = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nSheetDescription.displayName = SheetPrimitive.Description.displayName\n\nexport {\n  Sheet, SheetClose,\n  SheetContent, SheetDescription, SheetFooter, SheetHeader, SheetOverlay, SheetPortal, SheetTitle, SheetTrigger\n}\n\n", "import { cn } from \"@/src/lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n", "import * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/src/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n", "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { VariantProps, cva } from \"class-variance-authority\"\nimport { PanelLeft } from \"lucide-react\"\n\nimport { useIsMobile } from \"@/src/hooks/use-mobile\"\nimport { cn } from \"@/src/lib/utils\"\nimport { Button } from \"@/src/components/ui/button\"\nimport { Input } from \"@/src/components/ui/input\"\nimport { Separator } from \"@/src/components/ui/separator\"\nimport { Sheet, SheetContent } from \"@/src/components/ui/sheet\"\nimport { Skeleton } from \"@/src/components/ui/skeleton\"\nimport {\n  Toolt<PERSON>,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/src/components/ui/tooltip\"\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar:state\"\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\nconst SIDEBAR_WIDTH = \"16rem\"\nconst SIDEBAR_WIDTH_TABLET = \"14rem\"\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\n\ntype SidebarContext = {\n  state: \"expanded\" | \"collapsed\"\n  open: boolean\n  setOpen: (open: boolean) => void\n  openMobile: boolean\n  setOpenMobile: (open: boolean) => void\n  isMobile: boolean\n  toggleSidebar: () => void\n}\n\nconst SidebarContext = React.createContext<SidebarContext | null>(null)\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext)\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\n  }\n\n  return context\n}\n\nconst SidebarProvider = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    defaultOpen?: boolean\n    open?: boolean\n    onOpenChange?: (open: boolean) => void\n  }\n>(\n  (\n    {\n      defaultOpen = true,\n      open: openProp,\n      onOpenChange: setOpenProp,\n      className,\n      style,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const isMobile = useIsMobile()\n    const [openMobile, setOpenMobile] = React.useState(false)\n\n    // This is the internal state of the sidebar.\n    // We use openProp and setOpenProp for control from outside the component.\n    const [_open, _setOpen] = React.useState(defaultOpen)\n    const open = openProp ?? _open\n    const setOpen = React.useCallback(\n      (value: boolean | ((value: boolean) => boolean)) => {\n        const openState = typeof value === \"function\" ? value(open) : value\n        if (setOpenProp) {\n          setOpenProp(openState)\n        } else {\n          _setOpen(openState)\n        }\n\n        // This sets the cookie to keep the sidebar state.\n        document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\n      },\n      [setOpenProp, open]\n    )\n\n    // Auto-collapse on medium screens (1024px - 1279px)\n    React.useEffect(() => {\n      const handleResize = () => {\n        const width = window.innerWidth\n        if (width >= 1024 && width < 1280 && open) {\n          // Auto-collapse on medium screens for better space utilization\n          setOpen(false)\n        } else if (width >= 1280 && !open && !isMobile) {\n          // Auto-expand on large screens\n          setOpen(true)\n        }\n      }\n\n      window.addEventListener('resize', handleResize)\n      handleResize() // Check on mount\n\n      return () => window.removeEventListener('resize', handleResize)\n    }, [open, setOpen, isMobile])\n\n    // Helper to toggle the sidebar.\n    const toggleSidebar = React.useCallback(() => {\n      return isMobile\n        ? setOpenMobile((open) => !open)\n        : setOpen((open) => !open)\n    }, [isMobile, setOpen, setOpenMobile])\n\n    // Adds a keyboard shortcut to toggle the sidebar.\n    React.useEffect(() => {\n      const handleKeyDown = (event: KeyboardEvent) => {\n        if (\n          event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\n          (event.metaKey || event.ctrlKey)\n        ) {\n          event.preventDefault()\n          toggleSidebar()\n        }\n      }\n\n      window.addEventListener(\"keydown\", handleKeyDown)\n      return () => window.removeEventListener(\"keydown\", handleKeyDown)\n    }, [toggleSidebar])\n\n    // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n    // This makes it easier to style the sidebar with Tailwind classes.\n    const state = open ? \"expanded\" : \"collapsed\"\n\n    const contextValue = React.useMemo<SidebarContext>(\n      () => ({\n        state,\n        open,\n        setOpen,\n        isMobile,\n        openMobile,\n        setOpenMobile,\n        toggleSidebar,\n      }),\n      [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\n    )\n\n    return (\n      <SidebarContext.Provider value={contextValue}>\n        <TooltipProvider delayDuration={0}>\n          <div\n            style={\n              {\n                \"--sidebar-width\": SIDEBAR_WIDTH,\n                \"--sidebar-width-tablet\": SIDEBAR_WIDTH_TABLET,\n                \"--sidebar-width-mobile\": SIDEBAR_WIDTH_MOBILE,\n                \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n                ...style,\n              } as React.CSSProperties\n            }\n            className={cn(\n              \"group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar\",\n              className\n            )}\n            ref={ref}\n            {...props}\n          >\n            {children}\n          </div>\n        </TooltipProvider>\n      </SidebarContext.Provider>\n    )\n  }\n)\nSidebarProvider.displayName = \"SidebarProvider\"\n\nconst Sidebar = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    side?: \"left\" | \"right\"\n    variant?: \"sidebar\" | \"floating\" | \"inset\"\n    collapsible?: \"offcanvas\" | \"icon\" | \"none\"\n  }\n>(\n  (\n    {\n      side = \"left\",\n      variant = \"sidebar\",\n      collapsible = \"offcanvas\",\n      className,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\n\n    if (collapsible === \"none\") {\n      return (\n        <div\n          className={cn(\n            \"flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground\",\n            className\n          )}\n          ref={ref}\n          {...props}\n        >\n          {children}\n        </div>\n      )\n    }\n\n    if (isMobile) {\n      return (\n        <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n          <SheetContent\n            data-sidebar=\"sidebar\"\n            data-mobile=\"true\"\n            className=\"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden\"\n            style={\n              {\n                \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\n              } as React.CSSProperties\n            }\n            side={side}\n          >\n            <div className=\"flex h-full w-full flex-col\">{children}</div>\n          </SheetContent>\n        </Sheet>\n      )\n    }\n\n    return (\n      <div\n        ref={ref}\n        className=\"group peer hidden lg:block text-sidebar-foreground\"\n        data-state={state}\n        data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\n        data-variant={variant}\n        data-side={side}\n      >\n        {/* This is what handles the sidebar gap on desktop */}\n        <div\n          className={cn(\n            \"duration-200 relative h-svh bg-transparent transition-[width] ease-linear\",\n            \"w-[--sidebar-width-tablet] xl:w-[--sidebar-width]\",\n            \"group-data-[collapsible=offcanvas]:w-0\",\n            \"group-data-[side=right]:rotate-180\",\n            variant === \"floating\" || variant === \"inset\"\n              ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]\"\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon]\"\n          )}\n        />\n        <div\n          className={cn(\n            \"duration-200 fixed inset-y-0 z-10 hidden h-svh transition-[left,right,width] ease-linear lg:flex\",\n            \"w-[--sidebar-width-tablet] xl:w-[--sidebar-width]\",\n            side === \"left\"\n              ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width-tablet)*-1)] xl:group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\n              : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width-tablet)*-1)] xl:group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\n            // Adjust the padding for floating and inset variants.\n            variant === \"floating\" || variant === \"inset\"\n              ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]\"\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l\",\n            className\n          )}\n          {...props}\n        >\n          <div\n            data-sidebar=\"sidebar\"\n            className=\"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow\"\n          >\n            {children}\n          </div>\n        </div>\n      </div>\n    )\n  }\n)\nSidebar.displayName = \"Sidebar\"\n\nconst SidebarTrigger = React.forwardRef<\n  React.ElementRef<typeof Button>,\n  React.ComponentProps<typeof Button>\n>(({ className, onClick, ...props }, ref) => {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <Button\n      ref={ref}\n      data-sidebar=\"trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"h-7 w-7\", className)}\n      onClick={(event) => {\n        onClick?.(event)\n        toggleSidebar()\n      }}\n      {...props}\n    >\n      <PanelLeft />\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  )\n})\nSidebarTrigger.displayName = \"SidebarTrigger\"\n\nconst SidebarRail = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\">\n>(({ className, ...props }, ref) => {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <button\n      ref={ref}\n      data-sidebar=\"rail\"\n      aria-label=\"Toggle Sidebar\"\n      tabIndex={-1}\n      onClick={toggleSidebar}\n      title=\"Toggle Sidebar\"\n      className={cn(\n        \"absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex\",\n        \"[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize\",\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\n        \"group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar\",\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarRail.displayName = \"SidebarRail\"\n\nconst SidebarInset = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"main\">\n>(({ className, ...props }, ref) => {\n  return (\n    <main\n      ref={ref}\n      className={cn(\n        \"relative flex min-h-svh flex-1 flex-col bg-background\",\n        \"peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarInset.displayName = \"SidebarInset\"\n\nconst SidebarInput = React.forwardRef<\n  React.ElementRef<typeof Input>,\n  React.ComponentProps<typeof Input>\n>(({ className, ...props }, ref) => {\n  return (\n    <Input\n      ref={ref}\n      data-sidebar=\"input\"\n      className={cn(\n        \"h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarInput.displayName = \"SidebarInput\"\n\nconst SidebarHeader = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"header\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarHeader.displayName = \"SidebarHeader\"\n\nconst SidebarFooter = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"footer\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarFooter.displayName = \"SidebarFooter\"\n\nconst SidebarSeparator = React.forwardRef<\n  React.ElementRef<typeof Separator>,\n  React.ComponentProps<typeof Separator>\n>(({ className, ...props }, ref) => {\n  return (\n    <Separator\n      ref={ref}\n      data-sidebar=\"separator\"\n      className={cn(\"mx-2 w-auto bg-sidebar-border\", className)}\n      {...props}\n    />\n  )\n})\nSidebarSeparator.displayName = \"SidebarSeparator\"\n\nconst SidebarContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"content\"\n      className={cn(\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarContent.displayName = \"SidebarContent\"\n\nconst SidebarGroup = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"group\"\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarGroup.displayName = \"SidebarGroup\"\n\nconst SidebarGroupLabel = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"div\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-label\"\n      className={cn(\n        \"duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarGroupLabel.displayName = \"SidebarGroupLabel\"\n\nconst SidebarGroupAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-action\"\n      className={cn(\n        \"absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarGroupAction.displayName = \"SidebarGroupAction\"\n\nconst SidebarGroupContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    data-sidebar=\"group-content\"\n    className={cn(\"w-full text-sm\", className)}\n    {...props}\n  />\n))\nSidebarGroupContent.displayName = \"SidebarGroupContent\"\n\nconst SidebarMenu = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    data-sidebar=\"menu\"\n    className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\n    {...props}\n  />\n))\nSidebarMenu.displayName = \"SidebarMenu\"\n\nconst SidebarMenuItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    data-sidebar=\"menu-item\"\n    className={cn(\"group/menu-item relative\", className)}\n    {...props}\n  />\n))\nSidebarMenuItem.displayName = \"SidebarMenuItem\"\n\nconst sidebarMenuButtonVariants = cva(\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n        outline:\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\n      },\n      size: {\n        default: \"h-8 text-sm\",\n        sm: \"h-7 text-xs\",\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:!p-0\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst SidebarMenuButton = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & {\n    asChild?: boolean\n    isActive?: boolean\n    tooltip?: string | React.ComponentProps<typeof TooltipContent>\n  } & VariantProps<typeof sidebarMenuButtonVariants>\n>(\n  (\n    {\n      asChild = false,\n      isActive = false,\n      variant = \"default\",\n      size = \"default\",\n      tooltip,\n      className,\n      ...props\n    },\n    ref\n  ) => {\n    const Comp = asChild ? Slot : \"button\"\n    const { isMobile, state } = useSidebar()\n\n    const button = (\n      <Comp\n        ref={ref}\n        data-sidebar=\"menu-button\"\n        data-size={size}\n        data-active={isActive}\n        className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n        {...props}\n      />\n    )\n\n    if (!tooltip) {\n      return button\n    }\n\n    if (typeof tooltip === \"string\") {\n      tooltip = {\n        children: tooltip,\n      }\n    }\n\n    return (\n      <Tooltip>\n        <TooltipTrigger asChild>{button}</TooltipTrigger>\n        <TooltipContent\n          side=\"right\"\n          align=\"center\"\n          hidden={state !== \"collapsed\" || isMobile}\n          {...tooltip}\n        />\n      </Tooltip>\n    )\n  }\n)\nSidebarMenuButton.displayName = \"SidebarMenuButton\"\n\nconst SidebarMenuAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & {\n    asChild?: boolean\n    showOnHover?: boolean\n  }\n>(({ className, asChild = false, showOnHover = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-action\"\n      className={cn(\n        \"absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        showOnHover &&\n          \"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuAction.displayName = \"SidebarMenuAction\"\n\nconst SidebarMenuBadge = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    data-sidebar=\"menu-badge\"\n    className={cn(\n      \"absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none\",\n      \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\n      \"peer-data-[size=sm]/menu-button:top-1\",\n      \"peer-data-[size=default]/menu-button:top-1.5\",\n      \"peer-data-[size=lg]/menu-button:top-2.5\",\n      \"group-data-[collapsible=icon]:hidden\",\n      className\n    )}\n    {...props}\n  />\n))\nSidebarMenuBadge.displayName = \"SidebarMenuBadge\"\n\nconst SidebarMenuSkeleton = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    showIcon?: boolean\n  }\n>(({ className, showIcon = false, ...props }, ref) => {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`\n  }, [])\n\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"menu-skeleton\"\n      className={cn(\"rounded-md h-8 flex gap-2 px-2 items-center\", className)}\n      {...props}\n    >\n      {showIcon && (\n        <Skeleton\n          className=\"size-4 rounded-md\"\n          data-sidebar=\"menu-skeleton-icon\"\n        />\n      )}\n      <Skeleton\n        className=\"h-4 flex-1 max-w-[--skeleton-width]\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            \"--skeleton-width\": width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  )\n})\nSidebarMenuSkeleton.displayName = \"SidebarMenuSkeleton\"\n\nconst SidebarMenuSub = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    data-sidebar=\"menu-sub\"\n    className={cn(\n      \"mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5\",\n      \"group-data-[collapsible=icon]:hidden\",\n      className\n    )}\n    {...props}\n  />\n))\nSidebarMenuSub.displayName = \"SidebarMenuSub\"\n\nconst SidebarMenuSubItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ ...props }, ref) => <li ref={ref} {...props} />)\nSidebarMenuSubItem.displayName = \"SidebarMenuSubItem\"\n\nconst SidebarMenuSubButton = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentProps<\"a\"> & {\n    asChild?: boolean\n    size?: \"sm\" | \"md\"\n    isActive?: boolean\n  }\n>(({ asChild = false, size = \"md\", isActive, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        \"flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground\",\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\n        size === \"sm\" && \"text-xs\",\n        size === \"md\" && \"text-sm\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuSubButton.displayName = \"SidebarMenuSubButton\"\n\nexport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n}\n", "import * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/src/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n", "import * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/src/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n", "\"use client\";\n\nimport React from \"react\";\nimport Link from \"next/link\";\nimport { useRouter } from \"next/navigation\";\nimport { LogOut, User } from \"lucide-react\";\nimport { useAuth } from \"@/src/lib/auth/auth-context\";\nimport {\n  Avatar,\n  AvatarFallback,\n  AvatarImage,\n} from \"@/src/components/ui/avatar\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/src/components/ui/dropdown-menu\";\nimport {\n  SidebarMenu,\n  SidebarMenuItem,\n  SidebarMenuButton,\n} from \"@/src/components/ui/sidebar\";\n\nexport function UserDropdown() {\n  const { user, logout } = useAuth();\n  const router = useRouter();\n\n  if (!user) {\n    return null;\n  }\n\n  const userLogin = user.login || user.email?.split(\"@\")[0] || \"User\";\n  const initials = user.name\n    ? user.name\n        .split(\" \")\n        .filter((n) => n.length > 0)\n        .map((n) => n[0])\n        .join(\"\")\n        .toUpperCase()\n    : userLogin.substring(0, 2).toUpperCase();\n\n  const handleSignOut = () => {\n    logout();\n    router.push(\"/login\");\n  };\n\n  return (\n    <SidebarMenu>\n      <SidebarMenuItem>\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <SidebarMenuButton\n              size=\"lg\"\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\"\n            >\n              <Avatar className=\"h-8 w-8 rounded-lg\">\n                <AvatarImage src={user.avatar_url || undefined} alt={user.name || userLogin} />\n                <AvatarFallback className=\"rounded-lg\">\n                  {initials}\n                </AvatarFallback>\n              </Avatar>\n              <div className=\"grid flex-1 text-left text-sm leading-tight\">\n                <span className=\"truncate font-semibold text-gray-900\">{user.name || userLogin}</span>\n                <span className=\"truncate text-xs text-gray-600\">{user.email || `@${userLogin}`}</span>\n              </div>\n            </SidebarMenuButton>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent\n            className=\"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg\"\n            side=\"bottom\"\n            align=\"end\"\n            sideOffset={4}\n          >\n            <DropdownMenuLabel className=\"p-0 font-normal\">\n              <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\n                <Avatar className=\"h-8 w-8 rounded-lg\">\n                  <AvatarImage src={user.avatar_url || undefined} alt={user.name || userLogin} />\n                  <AvatarFallback className=\"rounded-lg\">\n                    {initials}\n                  </AvatarFallback>\n                </Avatar>\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\n                  <span className=\"truncate font-semibold text-gray-900\">{user.name || userLogin}</span>\n                  <span className=\"truncate text-xs text-gray-600\">{user.email || `@${userLogin}`}</span>\n                </div>\n              </div>\n            </DropdownMenuLabel>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem asChild>\n              <Link href=\"/dashboard/profile\" className=\"flex items-center text-gray-700 hover:!text-gray-900 hover:!bg-gray-50 cursor-pointer\">\n                <User className=\"mr-2 h-4 w-4 text-gray-500 hover:!text-gray-700\" />\n                Profile\n              </Link>\n            </DropdownMenuItem>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem onClick={handleSignOut} className=\"text-gray-700 hover:!text-gray-900 hover:!bg-gray-50 cursor-pointer\">\n              <LogOut className=\"mr-2 h-4 w-4 text-gray-500 hover:!text-gray-700\" />\n              Sign out\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n      </SidebarMenuItem>\n    </SidebarMenu>\n  );\n}\n", "\"use client\";\n\nimport React from \"react\";\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport {\n  Home,\n  Settings,\n  GitBranch,\n  BarChart2,\n  DollarSign,\n  Plug,\n  BookOpen,\n  HelpCircle,\n  Lock\n} from \"lucide-react\";\nimport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarMenu,\n  SidebarMenuItem,\n  SidebarMenuButton,\n} from \"@/src/components/ui/sidebar\";\nimport { UserDropdown } from \"./user-dropdown\";\n\n// Navigation Items\nconst getNavigationItems = () => [\n  { title: \"Home\", url: \"/dashboard\", icon: Home },\n  { title: \"Repositories\", url: \"/dashboard/repositories\", icon: GitBranch },\n  { title: \"Integrations\", url: \"/dashboard/integrations\", icon: Plug, locked: true },\n  { title: \"Reports\", url: \"/dashboard/reports\", icon: BarChart2, locked: true },\n  { title: \"Learnings\", url: \"/dashboard/learnings\", icon: BookOpen },\n  { title: \"Organization Settings\", url: \"/dashboard/settings\", icon: Settings },\n  { title: \"Subscription\", url: \"/dashboard/subscription\", icon: DollarSign },\n];\n\nconst bottomItems = [\n  { title: \"Docs\", url: \"/docs\", icon: BookOpen },\n  { title: \"Support\", url: \"/support\", icon: HelpCircle },\n];\n\nexport function DashboardSidebar() {\n  const pathname = usePathname();\n  const navigationItems = getNavigationItems();\n\n  return (\n    <Sidebar className=\"shadow-sm border-r border-gray-200 flex-shrink-0 bg-white\">\n      <SidebarHeader className=\"border-b border-gray-200 pb-4 space-y-4\">\n        <div className=\"px-2 py-2\">\n          <h2 className=\"text-sm font-semibold\">Platyfend</h2>\n        </div>\n      </SidebarHeader>\n\n      <SidebarContent className=\"px-2 overflow-y-auto\">\n        {/* Main Navigation */}\n        <SidebarGroup>\n          <SidebarGroupLabel className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider px-2 mb-2\">\n            Navigation\n          </SidebarGroupLabel>\n          <SidebarGroupContent>\n            <SidebarMenu className=\"space-y-1\">\n              {navigationItems.map((item) => (\n                <SidebarMenuItem key={item.title}>\n                  <SidebarMenuButton\n                    asChild\n                    isActive={pathname === item.url || (item.url !== \"/dashboard\" && pathname.startsWith(item.url))}\n                    className=\"w-full justify-start rounded-lg px-3 py-2 text-sm font-medium transition-all text-gray-600 hover:text-gray-800 hover:bg-gray-50 data-[active=true]:bg-[#00617b]/10 data-[active=true]:text-[#00617b] data-[active=true]:font-semibold\"\n                  >\n                    <Link href={item.url} className=\"flex items-center w-full\">\n                      <item.icon className=\"h-4 w-4 mr-3\" />\n                      <span className=\"flex-1\">{item.title}</span>\n                      {item.locked && <Lock className=\"h-4 w-4 text-gray-400\" />}\n                    </Link>\n                  </SidebarMenuButton>\n                </SidebarMenuItem>\n              ))}\n            </SidebarMenu>\n          </SidebarGroupContent>\n        </SidebarGroup>\n\n        {/* Resources */}\n        <SidebarGroup className=\"mt-8\">\n          <SidebarGroupLabel className=\"text-xs font-semibold text-gray-400 uppercase tracking-wider px-2 mb-2\">\n            Resources\n          </SidebarGroupLabel>\n          <SidebarGroupContent>\n            <SidebarMenu className=\"space-y-1\">\n              {bottomItems.map((item) => (\n                <SidebarMenuItem key={item.title}>\n                  <SidebarMenuButton\n                    asChild\n                    className=\"w-full justify-start rounded-lg px-3 py-2 text-sm font-medium transition-all text-gray-600 hover:text-gray-800 hover:bg-gray-50\"\n                  >\n                    <Link href={item.url}>\n                      <item.icon className=\"h-4 w-4 mr-3\" />\n                      <span>{item.title}</span>\n                    </Link>\n                  </SidebarMenuButton>\n                </SidebarMenuItem>\n              ))}\n            </SidebarMenu>\n          </SidebarGroupContent>\n        </SidebarGroup>\n      </SidebarContent>\n\n      <SidebarFooter>\n        <UserDropdown />\n      </SidebarFooter>\n    </Sidebar>\n  );\n}\n", "\"use client\";\n\nimport React from \"react\";\nimport { SidebarProvider, SidebarTrigger } from \"@/src/components/ui/sidebar\";\nimport { DashboardSidebar } from \"./sidebar\";\nimport { Button } from \"@/src/components/ui/button\";\nimport { Menu } from \"lucide-react\";\n\nexport function DashboardLayout({ children }: { children: React.ReactNode }) {\n  return (\n    <SidebarProvider>\n      <div className=\"min-h-screen flex w-full bg-gray-50 overflow-hidden\">\n        <DashboardSidebar />\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex flex-col bg-gray-50 min-w-0 w-full\">\n          {/* Mobile Menu Trigger */}\n          <div className=\"lg:hidden flex items-center p-4 border-b\">\n            <SidebarTrigger>\n              <Button variant=\"ghost\" size=\"icon\" className=\"mr-2\">\n                <Menu className=\"h-5 w-5\" />\n                <span className=\"sr-only\">Toggle menu</span>\n              </Button>\n            </SidebarTrigger>\n          </div>\n          <main className=\"flex-1 p-4 sm:p-6 lg:p-8 min-w-0 overflow-auto bg-gray-50 w-full\">\n            <div className=\"max-w-7xl mx-auto w-full\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n    </SidebarProvider>\n  );\n}\n"], "names": ["children", "_jsx", "DashboardLayout", "buttonVariants", "cva", "variants", "variant", "default", "destructive", "outline", "secondary", "ghost", "link", "platyfend", "size", "sm", "lg", "icon", "defaultVariants", "<PERSON><PERSON>", "React", "className", "<PERSON><PERSON><PERSON><PERSON>", "props", "ref", "Comp", "Slot", "cn", "displayName", "Input", "type", "input", "Separator", "orientation", "decorative", "SeparatorPrimitive", "Sheet", "SheetPrimitive", "SheetPort<PERSON>", "SheetOverlay", "sheetVariants", "side", "top", "bottom", "left", "right", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "X", "span", "Skeleton", "div", "SheetDescription", "TooltipProvider", "TooltipPrimitive", "<PERSON><PERSON><PERSON>", "TooltipTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sideOffset", "SIDEBAR_WIDTH_MOBILE", "SidebarContext", "useSidebar", "context", "SidebarProvider", "defaultOpen", "open", "openProp", "onOpenChange", "setOpenProp", "style", "isMobile", "useIsMobile", "setIsMobile", "useState", "openMobile", "setOpenMobile", "_open", "_setOpen", "<PERSON><PERSON><PERSON>", "openState", "value", "document", "cookie", "SIDEBAR_COOKIE_NAME", "SIDEBAR_COOKIE_MAX_AGE", "handleResize", "width", "window", "innerWidth", "addEventListener", "removeEventListener", "toggleSidebar", "handleKeyDown", "event", "key", "SIDEBAR_KEYBOARD_SHORTCUT", "ctrl<PERSON>ey", "preventDefault", "state", "contextValue", "Provider", "delayDuration", "SIDEBAR_WIDTH", "SIDEBAR_WIDTH_TABLET", "SIDEBAR_WIDTH_ICON", "Sidebar", "collapsible", "data-sidebar", "data-mobile", "data-state", "data-collapsible", "data-variant", "data-side", "SidebarTrigger", "onClick", "PanelLeft", "SidebarRail", "button", "aria-label", "tabIndex", "title", "main", "SidebarHeader", "SidebarFooter", "<PERSON>bar<PERSON><PERSON>nt", "SidebarGroup", "SidebarGroupLabel", "SidebarGroupContent", "SidebarMenu", "ul", "SidebarMenuItem", "li", "sidebarMenuButtonVariants", "SidebarMenuButton", "isActive", "tooltip", "data-size", "data-active", "align", "hidden", "showOnHover", "showIcon", "Math", "floor", "random", "SidebarMenuSubItem", "SidebarMenuSubButton", "Avatar", "AvatarPrimitive", "AvatarImage", "AvatarFallback", "DropdownMenu", "DropdownMenuPrimitive", "DropdownMenuTrigger", "DropdownMenuSubTrigger", "inset", "ChevronRight", "DropdownMenuContent", "DropdownMenuItem", "checked", "Check", "Circle", "DropdownMenuLabel", "DropdownMenuSeparator", "UserDropdown", "user", "logout", "useAuth", "router", "useRouter", "userLogin", "login", "email", "split", "initials", "name", "filter", "n", "length", "map", "join", "toUpperCase", "substring", "src", "avatar_url", "undefined", "alt", "Link", "href", "User", "handleSignOut", "push", "LogOut", "getNavigationItems", "url", "Home", "GitBranch", "Plug", "locked", "BarChart2", "BookOpen", "Settings", "DollarSign", "bottomItems", "HelpCircle", "DashboardSidebar", "pathname", "usePathname", "navigationItems", "h2", "item", "startsWith", "Lock", "<PERSON><PERSON>"], "sourceRoot": ""}