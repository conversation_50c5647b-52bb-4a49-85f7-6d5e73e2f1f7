{"version": 3, "file": "../app/support/page.js", "mappings": "qbAAA,6GCAA,oDCAA,+GEmBI,sBAAsB,yHAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAAC,SDxBbA,CCwBA,CDvBtB,ICuBkD,EDtBhDC,CAAAA,EAFoBD,EAEpBC,CAFoBD,EAEpBC,CAACC,CAAAA,KAAAA,CAAAA,CAAIC,SAAU,yCACb,SAAAC,CAAAA,EAAAA,EAAAA,IAAAA,CAACF,CAAAA,KAAAA,CAAAA,CAAIC,SAAU,oDACbC,CAAAA,EAAAA,EAAAA,IAAAA,CAACF,CAAAA,KAAAA,CAAAA,CAAIC,SAAU,uCACbF,CAAAA,EAAAA,EAAAA,GAAAA,CAACI,CAAAA,IAAAA,CAAAA,CAAGF,SAAU,iEAAgE,qBAC9EF,CAAAA,EAAAA,EAAAA,GAAAA,CAACK,CAAAA,GAAAA,CAAAA,CAAEH,SAAU,oCAAmC,wCAKlDF,CAAAA,EAAAA,EAAAA,GAAAA,CAACC,CAAAA,KAAAA,CAAAA,CAAIC,SAAU,mEACb,SAAAC,CAAAA,EAAAA,EAAAA,IAAAA,CAACF,CAAAA,KAAAA,CAAAA,CAAIC,SAAU,yBACbF,CAAAA,EAAAA,EAAAA,GAAAA,CAACM,CAAAA,IAAAA,CAAAA,CAAGJ,SAAU,wDAAuD,wBACrEF,CAAAA,EAAAA,EAAAA,GAAAA,CAACK,CAAAA,GAAAA,CAAAA,CAAEH,SAAU,2CAA0C,oFAGvDC,CAAAA,EAAAA,EAAAA,IAAAA,CAACF,CAAAA,KAAAA,CAAAA,CAAIC,SAAU,wCACbC,CAAAA,EAAAA,EAAAA,IAAAA,CAACF,CAAAA,KAAAA,CAAAA,CAAIC,SAAU,uCACbF,CAAAA,EAAAA,EAAAA,GAAAA,CAACO,CAAAA,IAAAA,CAAAA,CAAGL,SAAU,oDAAmD,2BACjEF,CAAAA,EAAAA,EAAAA,GAAAA,CAACK,CAAAA,GAAAA,CAAAA,CAAEH,SAAU,sCAAqC,sCAEpDC,CAAAA,EAAAA,EAAAA,IAAAA,CAACF,CAAAA,KAAAA,CAAAA,CAAIC,SAAU,uCACbF,CAAAA,EAAAA,EAAAA,GAAAA,CAACO,CAAAA,IAAAA,CAAAA,CAAGL,SAAU,oDAAmD,2BACjEF,CAAAA,EAAAA,EAAAA,GAAAA,CAACK,CAAAA,GAAAA,CAAAA,CAAEH,SAAU,sCAAqC,6DAQlE,ECRsD,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EAAE,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,EAAI,OAC7D,EAAU,GAAmB,EAAtB,KAA6B,CACrC,MAD4B,CACnB,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,UAAU,CAC1B,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,mECAA,0GCAA,qDCAA,gDCAA,kDCAA,gDCAA,wGCAA,gECAA,kDCAA,iECAA,uDCAA,uDCAA,sDCAA,wVCaA,OACA,UACA,GACA,CACA,UACA,UACA,CACA,uBAAiC,EACjC,MAjBA,IAAoB,uCAAqH,CAiBzI,mFAEA,CAAS,EACF,CACP,CAGA,EACA,CACO,CACP,CACA,QAhCA,IAAsB,uCAA+G,CAgCrI,8EACA,aAhCA,IAAsB,4CAAgF,CAgCtG,+CACA,WAhCA,IAAsB,4CAAgF,CAgCtG,+CACA,cAhCA,IAAsB,4CAAmF,CAgCzG,mDAGA,CACO,UACP,uFAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,qBACA,oBAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,yBCpED,sDCAA,sECAA,oDCAA,kECAA,yDCAA,uDCAA,6GCAA,qDCAA,4DCAA,wDCAA,iECAA,uDCAA,mECAA,iDCAA,2DCAA,2DCAA,iDCAA,yDCAA,4DCAA", "sources": ["webpack://platyfend/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://platyfend/external commonjs2 \"module\"", "webpack://platyfend/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://platyfend/src/app/support/page.tsx", "webpack://platyfend/sentry-wrapper-module", "webpack://platyfend/external commonjs \"require-in-the-middle\"", "webpack://platyfend/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://platyfend/external commonjs2 \"process\"", "webpack://platyfend/external commonjs2 \"os\"", "webpack://platyfend/external commonjs2 \"util\"", "webpack://platyfend/external commonjs2 \"fs\"", "webpack://platyfend/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://platyfend/external node-commonjs \"node:child_process\"", "webpack://platyfend/external commonjs2 \"path\"", "webpack://platyfend/external commonjs2 \"diagnostics_channel\"", "webpack://platyfend/external node-commonjs \"node:http\"", "webpack://platyfend/external node-commonjs \"node:zlib\"", "webpack://platyfend/external node-commonjs \"node:tls\"", "webpack://platyfend/external node-commonjs \"node:https\"", "webpack://platyfend/?9552", "webpack://platyfend/external node-commonjs \"node:os\"", "webpack://platyfend/external node-commonjs \"node:diagnostics_channel\"", "webpack://platyfend/external commonjs2 \"crypto\"", "webpack://platyfend/external commonjs \"import-in-the-middle\"", "webpack://platyfend/external node-commonjs \"node:stream\"", "webpack://platyfend/external node-commonjs \"node:util\"", "webpack://platyfend/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://platyfend/external node-commonjs \"node:fs\"", "webpack://platyfend/external commonjs2 \"worker_threads\"", "webpack://platyfend/external commonjs2 \"perf_hooks\"", "webpack://platyfend/external node-commonjs \"node:worker_threads\"", "webpack://platyfend/external node-commonjs \"node:path\"", "webpack://platyfend/external node-commonjs \"node:net\"", "webpack://platyfend/external commonjs2 \"url\"", "webpack://platyfend/external commonjs2 \"child_process\"", "webpack://platyfend/external node-commonjs \"node:readline\"", "webpack://platyfend/external commonjs2 \"tty\"", "webpack://platyfend/external commonjs2 \"async_hooks\"", "webpack://platyfend/external node-commonjs \"node:inspector\"", "webpack://platyfend/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "export default function SupportPage() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-8 sm:py-12\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-8 sm:mb-12\">\n          <h1 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">Support</h1>\n          <p className=\"text-lg sm:text-xl text-gray-600\">\n            Get help with Platyfend\n          </p>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 sm:p-8\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl sm:text-2xl font-semibold text-gray-900 mb-4\">Need Help?</h2>\n            <p className=\"text-gray-600 mb-6 text-sm sm:text-base\">\n              Our support system is being set up. For now, please reach out through:\n            </p>\n            <div className=\"space-y-4 max-w-md mx-auto\">\n              <div className=\"p-4 bg-gray-50 rounded-lg\">\n                <h3 className=\"font-semibold text-gray-900 text-sm sm:text-base\">Email Support</h3>\n                <p className=\"text-gray-600 text-sm sm:text-base\"><EMAIL></p>\n              </div>\n              <div className=\"p-4 bg-gray-50 rounded-lg\">\n                <h3 className=\"font-semibold text-gray-900 text-sm sm:text-base\">GitHub Issues</h3>\n                <p className=\"text-gray-600 text-sm sm:text-base\">Report bugs and feature requests</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/support',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/support',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/support',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/support',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/not-found-error\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst page4 = () => import(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/support/page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'support',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/support/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/support/page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"next/dist/client/components/error-boundary\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/support/page\",\n        pathname: \"/support\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["SupportPage", "_jsx", "div", "className", "_jsxs", "h1", "p", "h2", "h3"], "sourceRoot": ""}