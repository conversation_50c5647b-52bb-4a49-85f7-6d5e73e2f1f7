try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="0e7c9b73-8e77-431b-a441-05b4e2a85226",e._sentryDebugIdIdentifier="sentry-dbid-0e7c9b73-8e77-431b-a441-05b4e2a85226")}catch(e){}(()=>{var e={};e.id=174,e.ids=[174],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11961:(e,t,r)=>{"use strict";let s;r.r(t),r.d(t,{default:()=>c,generateImageMetadata:()=>u,generateMetadata:()=>p,generateViewport:()=>l});var i=r(63033),o=r(69929),a=r(68575);let n={...i},d="workUnitAsyncStorage"in n?n.workUnitAsyncStorage:"requestAsyncStorage"in n?n.requestAsyncStorage:void 0;s=new Proxy(function(){return(0,o.jsx)("div",{className:"min-h-screen bg-gray-50 py-8 sm:py-12",children:(0,o.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,o.jsxs)("div",{className:"text-center mb-8 sm:mb-12",children:[(0,o.jsx)("h1",{className:"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4",children:"Support"}),(0,o.jsx)("p",{className:"text-lg sm:text-xl text-gray-600",children:"Get help with Platyfend"})]}),(0,o.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 sm:p-8",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("h2",{className:"text-xl sm:text-2xl font-semibold text-gray-900 mb-4",children:"Need Help?"}),(0,o.jsx)("p",{className:"text-gray-600 mb-6 text-sm sm:text-base",children:"Our support system is being set up. For now, please reach out through:"}),(0,o.jsxs)("div",{className:"space-y-4 max-w-md mx-auto",children:[(0,o.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,o.jsx)("h3",{className:"font-semibold text-gray-900 text-sm sm:text-base",children:"Email Support"}),(0,o.jsx)("p",{className:"text-gray-600 text-sm sm:text-base",children:"<EMAIL>"})]}),(0,o.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,o.jsx)("h3",{className:"font-semibold text-gray-900 text-sm sm:text-base",children:"GitHub Issues"}),(0,o.jsx)("p",{className:"text-gray-600 text-sm sm:text-base",children:"Report bugs and feature requests"})]})]})]})})]})})},{apply:(e,t,r)=>{let s,i,o;try{let e=d?.getStore();s=e?.headers.get("sentry-trace")??void 0,i=e?.headers.get("baggage")??void 0,o=e?.headers}catch(e){}return a.wrapServerComponentWithSentry(e,{componentRoute:"/support",componentType:"Page",sentryTraceHeader:s,baggageHeader:i,headers:o}).apply(t,r)}});let p=void 0,u=void 0,l=void 0,c=s},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},46712:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>c,tree:()=>p});var s=r(72787),i=r(35964),o=r(43230),a=r.n(o),n=r(38441),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let p={children:["",{children:["support",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,11961)),"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/support/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,24860)),"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,33674,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,39755,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,37648,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/support/page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/support/page",pathname:"/support",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[883,597],()=>r(46712));module.exports=s})();
//# sourceMappingURL=page.js.map