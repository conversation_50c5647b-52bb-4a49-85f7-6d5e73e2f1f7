try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="1a1ae8b1-12f2-4d6b-b26b-f1206156a4f7",e._sentryDebugIdIdentifier="sentry-dbid-1a1ae8b1-12f2-4d6b-b26b-f1206156a4f7")}catch(e){}(()=>{var e={};e.id=631,e.ids=[631],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17427:(e,r,t)=>{"use strict";let s;t.r(r),t.d(r,{default:()=>x,generateImageMetadata:()=>l,generateMetadata:()=>p,generateViewport:()=>c});var i=t(63033),o=t(69929);function n(){return(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h1",{className:"text-2xl font-semibold text-slate-900",children:"Organization Settings"}),(0,o.jsx)("p",{className:"text-slate-600 mt-1",children:"Manage your organization preferences and configurations."})]}),(0,o.jsx)("div",{className:"bg-white rounded-lg border border-slate-200 shadow-sm p-8",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("h2",{className:"text-lg font-medium text-slate-900 mb-2",children:"Organization Settings"}),(0,o.jsx)("p",{className:"text-slate-600",children:"This page is under development. Organization management features will be available soon."})]})})]})}var a=t(68575);let d={...i},u="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;s=new Proxy(function(){return(0,o.jsx)(n,{})},{apply:(e,r,t)=>{let s,i,o;try{let e=u?.getStore();s=e?.headers.get("sentry-trace")??void 0,i=e?.headers.get("baggage")??void 0,o=e?.headers}catch(e){}return a.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/settings",componentType:"Page",sentryTraceHeader:s,baggageHeader:i,headers:o}).apply(r,t)}});let p=void 0,l=void 0,c=void 0,x=s},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68154:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>l,pages:()=>p,routeModule:()=>c,tree:()=>u});var s=t(72787),i=t(35964),o=t(43230),n=t.n(o),a=t(38441),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let u={children:["",{children:["dashboard",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,17427)),"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/dashboard/settings/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,15598)),"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/dashboard/layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,24860)),"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,33674,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39755,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,37648,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=["/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/dashboard/settings/page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/settings/page",pathname:"/dashboard/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[883,822,597,542],()=>t(68154));module.exports=s})();
//# sourceMappingURL=page.js.map