{"version": 3, "file": "../app/dashboard/settings/page.js", "mappings": "obAAA,8GCAA,mDCAA,gHGmBI,sBAAsB,8GFnBnB,SAASA,IACd,MACE,WAACC,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,WACC,UAACE,KAAAA,CAAGD,UAAU,iDAAwC,0BACtD,UAACE,IAAAA,CAAEF,UAAU,+BAAsB,gEAKrC,UAACD,MAAAA,CAAIC,UAAU,qEACb,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACG,KAAAA,CAAGH,UAAU,mDAA0C,0BACxD,UAACE,IAAAA,CAAEF,UAAU,0BAAiB,oGAOxC,gBEZA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAAC,SDtBbI,CCsBA,CDrBtB,EADsBA,ECsB4B,CDtB5BA,CACfC,CAAAA,EAAAA,EAAAA,GAAAA,CAACP,CAAAA,EAAAA,CAAAA,CAAAA,CAAAA,ECqB4C,CAClD,IDtBMA,CCsBD,CAAE,CAAC,EAAkB,EAAS,IAAI,CACrC,IAD0C,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EADI,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACpC,MAAO,CAAC,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,qBAAqB,CACrC,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,mECAA,0GCAA,qDCAA,gDCAA,kDCAA,gDCAA,wGCAA,gECAA,kDCAA,iECAA,uDCAA,uDCAA,sDCAA,wDCAA,qDCAA,sECAA,oDCAA,kECAA,yDCAA,uDCAA,6YCcA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,WACA,CACA,uBAAiC,EACjC,MApBA,IAAoB,uCAAgI,CAoBpJ,+FAES,EACF,CACP,CAGA,EACA,CACO,CACP,CACA,QAhCA,IAAsB,uCAAyH,CAgC/I,yFAGA,CACO,CACP,CACA,QA1CA,IAAsB,uCAA+G,CA0CrI,8EACA,aA1CA,IAAsB,4CAAgF,CA0CtG,+CACA,WA1CA,IAAsB,4CAAgF,CA0CtG,+CACA,cA1CA,IAAsB,4CAAmF,CA0CzG,mDAGA,CACO,UACP,kGAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,gCACA,+BAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,0BC9ED,qDCAA,4DCAA,wDCAA,iECAA,uDCAA,mECAA,iDCAA,0DCAA,4DCAA,iDCAA,yDCAA,4DCAA", "sources": ["webpack://platyfend/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://platyfend/external commonjs2 \"module\"", "webpack://platyfend/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://platyfend/./src/components/dashboard/settings-page.tsx", "webpack://platyfend/src/app/dashboard/settings/page.tsx", "webpack://platyfend/sentry-wrapper-module", "webpack://platyfend/external commonjs \"require-in-the-middle\"", "webpack://platyfend/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://platyfend/external commonjs2 \"process\"", "webpack://platyfend/external commonjs2 \"os\"", "webpack://platyfend/external commonjs2 \"util\"", "webpack://platyfend/external commonjs2 \"fs\"", "webpack://platyfend/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://platyfend/external node-commonjs \"node:child_process\"", "webpack://platyfend/external commonjs2 \"path\"", "webpack://platyfend/external commonjs2 \"diagnostics_channel\"", "webpack://platyfend/external node-commonjs \"node:http\"", "webpack://platyfend/external node-commonjs \"node:zlib\"", "webpack://platyfend/external node-commonjs \"node:tls\"", "webpack://platyfend/external node-commonjs \"node:https\"", "webpack://platyfend/external node-commonjs \"node:os\"", "webpack://platyfend/external node-commonjs \"node:diagnostics_channel\"", "webpack://platyfend/external commonjs2 \"crypto\"", "webpack://platyfend/external commonjs \"import-in-the-middle\"", "webpack://platyfend/external node-commonjs \"node:stream\"", "webpack://platyfend/external node-commonjs \"node:util\"", "webpack://platyfend/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://platyfend/?7732", "webpack://platyfend/external node-commonjs \"node:fs\"", "webpack://platyfend/external commonjs2 \"worker_threads\"", "webpack://platyfend/external commonjs2 \"perf_hooks\"", "webpack://platyfend/external node-commonjs \"node:worker_threads\"", "webpack://platyfend/external node-commonjs \"node:path\"", "webpack://platyfend/external node-commonjs \"node:net\"", "webpack://platyfend/external commonjs2 \"url\"", "webpack://platyfend/external commonjs2 \"child_process\"", "webpack://platyfend/external node-commonjs \"node:readline\"", "webpack://platyfend/external commonjs2 \"tty\"", "webpack://platyfend/external commonjs2 \"async_hooks\"", "webpack://platyfend/external node-commonjs \"node:inspector\"", "webpack://platyfend/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "export function SettingsPage() {\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h1 className=\"text-2xl font-semibold text-slate-900\">Organization Settings</h1>\n        <p className=\"text-slate-600 mt-1\">\n          Manage your organization preferences and configurations.\n        </p>\n      </div>\n      \n      <div className=\"bg-white rounded-lg border border-slate-200 shadow-sm p-8\">\n        <div className=\"text-center\">\n          <h2 className=\"text-lg font-medium text-slate-900 mb-2\">Organization Settings</h2>\n          <p className=\"text-slate-600\">\n            This page is under development. Organization management features will be available soon.\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "import { SettingsPage } from \"@/src/components/dashboard/settings-page\";\n\nexport default function Page() {\n  return <SettingsPage />;\n}\n", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/settings',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/settings',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/settings',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/settings',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/not-found-error\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/dashboard/layout.tsx\");\nconst page5 = () => import(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/dashboard/settings/page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'settings',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/dashboard/settings/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/dashboard/layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/dashboard/settings/page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"next/dist/client/components/error-boundary\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/settings/page\",\n        pathname: \"/dashboard/settings\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["SettingsPage", "div", "className", "h1", "p", "h2", "Page", "_jsx"], "sourceRoot": ""}