{"version": 3, "file": "../app/docs/page.js", "mappings": "mbAAA,6GCAA,oDCAA,qYCaA,OACA,UACA,GACA,CACA,UACA,OACA,CACA,uBAAiC,EACjC,MAjBA,IAAoB,uCAAkH,CAiBtI,iFAES,EACF,CACP,CAGA,EACA,CACO,CACP,CACA,QAhCA,IAAsB,uCAA+G,CAgCrI,8EACA,aAhCA,IAAsB,4CAAgF,CAgCtG,+CACA,WAhCA,IAAsB,4CAAgF,CAgCtG,+CACA,cAhCA,IAAsB,4CAAmF,CAgCzG,mDAGA,CACO,UACP,oFAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,kBACA,iBAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,0BCpED,mECAA,0GCAA,qDCAA,gDCAA,kDCAA,gDCAA,uGCAA,iECAA,kDCAA,iECAA,uDCAA,uDCAA,sDCAA,wDCAA,qDCAA,sECAA,oDCAA,kECAA,yDCAA,uDCAA,6GCAA,oDCAA,6DCAA,wDCAA,gECAA,wDCAA,mEEAA,iDCAA,2DCAA,2DCAA,gDCAA,0DCAA,sEEmBI,sBAAsB,yHAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,CACnB,OASN,EATe,IASc,KAAK,CAAC,SDxBbA,CCwBA,CDvBtB,ICuBkD,EDtBhDC,CAAAA,EAAAA,EAAAA,GAAAA,CAACC,CAAAA,KAAAA,CAAAA,CAAIC,SAAU,yCACb,SAAAC,CAAAA,EAAAA,EAAAA,IAAAA,CAACF,CAAAA,KAAAA,CAAAA,CAAIC,SAAU,oDACbC,CAAAA,EAAAA,EAAAA,IAAAA,CAACF,CAAAA,KAAAA,CAAAA,CAAIC,SAAU,uCACbF,CAAAA,EAAAA,EAAAA,GAAAA,CAACI,CAAAA,IAAAA,CAAAA,CAAGF,SAAU,iEAAgE,2BAC9EF,CAAAA,EAAAA,EAAAA,GAAAA,CAACK,CAAAA,GAAAA,CAAAA,CAAEH,SAAU,oCAAmC,+EAKlDF,CAAAA,EAAAA,EAAAA,GAAAA,CAACC,CAAAA,KAAAA,CAAAA,CAAIC,SAAU,mEACb,SAAAC,CAAAA,EAAAA,EAAAA,IAAAA,CAACF,CAAAA,KAAAA,CAAAA,CAAIC,SAAU,yBACbF,CAAAA,EAAAA,EAAAA,GAAAA,CAACM,CAAAA,IAAAA,CAAAA,CAAGJ,SAAU,wDAAuD,yBACrEF,CAAAA,EAAAA,EAAAA,GAAAA,CAACK,CAAAA,GAAAA,CAAAA,CAAEH,SAAU,2CAA0C,iFAGvDC,CAAAA,EAAAA,EAAAA,IAAAA,CAACI,CAAAA,IAAAA,CAAAA,CAAGL,SAAU,qFACZF,CAAAA,EAAAA,EAAAA,GAAAA,CAACQ,CAAAA,IAAAA,CAAAA,CAAG,qCACJR,CAAAA,EAAAA,EAAAA,GAAAA,CAACQ,CAAAA,IAAAA,CAAAA,CAAG,iCACJR,CAAAA,EAAAA,EAAAA,GAAAA,CAACQ,CAAAA,IAAAA,CAAAA,CAAG,iCACJR,CAAAA,EAAAA,EAAAA,GAAAA,CAACQ,CAAAA,IAAAA,CAAAA,CAAG,8BACJR,CAAAA,EAAAA,EAAAA,GAAAA,CAACQ,CAAAA,IAAAA,CAAAA,CAAG,2CAOlB,ECLsD,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,MAD4B,CACnB,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,OAAO,CACvB,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B", "sources": ["webpack://platyfend/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://platyfend/external commonjs2 \"module\"", "webpack://platyfend/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://platyfend/?2778", "webpack://platyfend/external commonjs \"require-in-the-middle\"", "webpack://platyfend/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://platyfend/external commonjs2 \"process\"", "webpack://platyfend/external commonjs2 \"os\"", "webpack://platyfend/external commonjs2 \"util\"", "webpack://platyfend/external commonjs2 \"fs\"", "webpack://platyfend/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://platyfend/external node-commonjs \"node:child_process\"", "webpack://platyfend/external commonjs2 \"path\"", "webpack://platyfend/external commonjs2 \"diagnostics_channel\"", "webpack://platyfend/external node-commonjs \"node:http\"", "webpack://platyfend/external node-commonjs \"node:zlib\"", "webpack://platyfend/external node-commonjs \"node:tls\"", "webpack://platyfend/external node-commonjs \"node:https\"", "webpack://platyfend/external node-commonjs \"node:os\"", "webpack://platyfend/external node-commonjs \"node:diagnostics_channel\"", "webpack://platyfend/external commonjs2 \"crypto\"", "webpack://platyfend/external commonjs \"import-in-the-middle\"", "webpack://platyfend/external node-commonjs \"node:stream\"", "webpack://platyfend/external node-commonjs \"node:util\"", "webpack://platyfend/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://platyfend/external node-commonjs \"node:fs\"", "webpack://platyfend/external commonjs2 \"worker_threads\"", "webpack://platyfend/external commonjs2 \"perf_hooks\"", "webpack://platyfend/external node-commonjs \"node:worker_threads\"", "webpack://platyfend/external node-commonjs \"node:path\"", "webpack://platyfend/external node-commonjs \"node:net\"", "webpack://platyfend/", "webpack://platyfend/external commonjs2 \"url\"", "webpack://platyfend/external commonjs2 \"child_process\"", "webpack://platyfend/external node-commonjs \"node:readline\"", "webpack://platyfend/external commonjs2 \"tty\"", "webpack://platyfend/external commonjs2 \"async_hooks\"", "webpack://platyfend/external node-commonjs \"node:inspector\"", "webpack://platyfend/src/app/docs/page.tsx", "webpack://platyfend/sentry-wrapper-module", "webpack://platyfend/external commonjs2 \"events\"", "webpack://platyfend/?11f9"], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/not-found-error\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst page4 = () => import(/* webpackMode: \"eager\" */ \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/docs/page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'docs',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/docs/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/docs/page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"next/dist/client/components/error-boundary\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/docs/page\",\n        pathname: \"/docs\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", null, "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "export default function DocsPage() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-8 sm:py-12\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-8 sm:mb-12\">\n          <h1 className=\"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4\">Documentation</h1>\n          <p className=\"text-lg sm:text-xl text-gray-600\">\n            Learn how to use Platyfend to improve your code review process\n          </p>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 sm:p-8\">\n          <div className=\"text-center\">\n            <h2 className=\"text-xl sm:text-2xl font-semibold text-gray-900 mb-4\">Coming Soon</h2>\n            <p className=\"text-gray-600 mb-6 text-sm sm:text-base\">\n              Our comprehensive documentation is being prepared. It will include:\n            </p>\n            <ul className=\"text-left max-w-md mx-auto space-y-2 text-gray-600 text-sm sm:text-base\">\n              <li>• Getting started guide</li>\n              <li>• Integration setup</li>\n              <li>• API documentation</li>\n              <li>• Best practices</li>\n              <li>• Troubleshooting</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/docs',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/docs',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/docs',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/docs',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"events\");"], "names": ["DocsPage", "_jsx", "div", "className", "_jsxs", "h1", "p", "h2", "ul", "li"], "sourceRoot": ""}