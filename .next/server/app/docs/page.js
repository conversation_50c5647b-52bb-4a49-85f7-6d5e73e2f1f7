try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="92c0945f-953d-4c81-bc68-8db5ec810ca2",e._sentryDebugIdIdentifier="sentry-dbid-92c0945f-953d-4c81-bc68-8db5ec810ca2")}catch(e){}(()=>{var e={};e.id=40,e.ids=[40],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17838:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>p,tree:()=>c});var s=t(72787),i=t(35964),o=t(43230),n=t.n(o),d=t(38441),a={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>d[e]);t.d(r,a);let c={children:["",{children:["docs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,89638)),"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/docs/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,24860)),"/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,33674,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,39755,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,37648,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["/home/<USER>/Desktop/Platyfend/platyfend-dashboard/src/app/docs/page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/docs/page",pathname:"/docs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},89638:(e,r,t)=>{"use strict";let s;t.r(r),t.d(r,{default:()=>p,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>l});var i=t(63033),o=t(69929),n=t(68575);let d={...i},a="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;s=new Proxy(function(){return(0,o.jsx)("div",{className:"min-h-screen bg-gray-50 py-8 sm:py-12",children:(0,o.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,o.jsxs)("div",{className:"text-center mb-8 sm:mb-12",children:[(0,o.jsx)("h1",{className:"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4",children:"Documentation"}),(0,o.jsx)("p",{className:"text-lg sm:text-xl text-gray-600",children:"Learn how to use Platyfend to improve your code review process"})]}),(0,o.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 sm:p-8",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("h2",{className:"text-xl sm:text-2xl font-semibold text-gray-900 mb-4",children:"Coming Soon"}),(0,o.jsx)("p",{className:"text-gray-600 mb-6 text-sm sm:text-base",children:"Our comprehensive documentation is being prepared. It will include:"}),(0,o.jsxs)("ul",{className:"text-left max-w-md mx-auto space-y-2 text-gray-600 text-sm sm:text-base",children:[(0,o.jsx)("li",{children:"• Getting started guide"}),(0,o.jsx)("li",{children:"• Integration setup"}),(0,o.jsx)("li",{children:"• API documentation"}),(0,o.jsx)("li",{children:"• Best practices"}),(0,o.jsx)("li",{children:"• Troubleshooting"})]})]})})]})})},{apply:(e,r,t)=>{let s,i,o;try{let e=a?.getStore();s=e?.headers.get("sentry-trace")??void 0,i=e?.headers.get("baggage")??void 0,o=e?.headers}catch(e){}return n.wrapServerComponentWithSentry(e,{componentRoute:"/docs",componentType:"Page",sentryTraceHeader:s,baggageHeader:i,headers:o}).apply(r,t)}});let c=void 0,u=void 0,l=void 0,p=s},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[883,597],()=>t(17838));module.exports=s})();
//# sourceMappingURL=page.js.map