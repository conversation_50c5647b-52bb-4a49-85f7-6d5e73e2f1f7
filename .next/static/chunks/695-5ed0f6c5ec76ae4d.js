try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="91b28aa8-e03c-4d90-9328-1b25c25d5853",e._sentryDebugIdIdentifier="sentry-dbid-91b28aa8-e03c-4d90-9328-1b25c25d5853")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[695],{1285:(e,t,n)=>{n.d(t,{B:()=>a});var r,l=n(2115),o=n(2712),u=(r||(r=n.t(l,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function a(e){let[t,n]=l.useState(u());return(0,o.N)(()=>{e||n(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},2085:(e,t,n)=>{n.d(t,{F:()=>u});var r=n(2596);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.$,u=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:u,defaultVariants:i}=t,a=Object.keys(u).map(e=>{let t=null==n?void 0:n[e],r=null==i?void 0:i[e];if(null===t)return null;let o=l(t)||l(r);return u[e][o]}),c=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return o(e,a,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...l}=t;return Object.entries(l).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...i,...c}[t]):({...i,...c})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},2712:(e,t,n)=>{n.d(t,{N:()=>l});var r=n(2115),l=globalThis?.document?r.useLayoutEffect:()=>{}},3655:(e,t,n)=>{n.d(t,{hO:()=>a,sG:()=>i});var r=n(2115),l=n(7650),o=n(9708),u=n(5155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,o.TL)(`Primitive.${t}`),l=r.forwardRef((e,r)=>{let{asChild:l,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(l?n:t,{...o,ref:r})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{});function a(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5845:(e,t,n)=>{n.d(t,{i:()=>i});var r,l=n(2115),o=n(2712),u=(r||(r=n.t(l,2)))[" useInsertionEffect ".trim().toString()]||o.N;function i({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,i,a]=function({defaultProp:e,onChange:t}){let[n,r]=l.useState(e),o=l.useRef(n),i=l.useRef(t);return u(()=>{i.current=t},[t]),l.useEffect(()=>{o.current!==n&&(i.current?.(n),o.current=n)},[n,o]),[n,r,i]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:o;{let t=l.useRef(void 0!==e);l.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,l.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else i(t)},[c,e,i,a])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,n)=>{n.d(t,{A:()=>u,q:()=>o});var r=n(2115),l=n(5155);function o(e,t){let n=r.createContext(t),o=e=>{let{children:t,...o}=e,u=r.useMemo(()=>o,Object.values(o));return(0,l.jsx)(n.Provider,{value:u,children:t})};return o.displayName=e+"Provider",[o,function(l){let o=r.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${l}\` must be used within \`${e}\``)}]}function u(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let l=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:l}}),[n,l])}};return o.scopeName=e,[function(t,o){let u=r.createContext(o),i=n.length;n=[...n,o];let a=t=>{let{scope:n,children:o,...a}=t,c=n?.[e]?.[i]||u,s=r.useMemo(()=>a,Object.values(a));return(0,l.jsx)(c.Provider,{value:s,children:o})};return a.displayName=t+"Provider",[a,function(n,l){let a=l?.[e]?.[i]||u,c=r.useContext(a);if(c)return c;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=n.reduce((t,{useScope:n,scopeName:r})=>{let l=n(e)[`__scope${r}`];return{...t,...l}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return n.scopeName=t.scopeName,n}(o,...t)]}},6101:(e,t,n)=>{n.d(t,{s:()=>u,t:()=>o});var r=n(2115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let n=!1,r=e.map(e=>{let r=l(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():l(e[t],null)}}}}function u(...e){return r.useCallback(o(...e),e)}},7328:(e,t,n)=>{function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function l(e,t){var n=r(e,t,"get");return n.get?n.get.call(e):n.value}function o(e,t,n){var l=r(e,t,"set");if(l.set)l.set.call(e,n);else{if(!l.writable)throw TypeError("attempted to set read only private field");l.value=n}return n}n.d(t,{N:()=>f});var u,i=n(2115),a=n(6081),c=n(6101),s=n(9708),d=n(5155);function f(e){let t=e+"CollectionProvider",[n,r]=(0,a.A)(t),[l,o]=n(t,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:t,children:n}=e,r=i.useRef(null),o=i.useRef(new Map).current;return(0,d.jsx)(l,{scope:t,itemMap:o,collectionRef:r,children:n})};u.displayName=t;let f=e+"CollectionSlot",m=(0,s.TL)(f),p=i.forwardRef((e,t)=>{let{scope:n,children:r}=e,l=o(f,n),u=(0,c.s)(t,l.collectionRef);return(0,d.jsx)(m,{ref:u,children:r})});p.displayName=f;let v=e+"CollectionItemSlot",y="data-radix-collection-item",N=(0,s.TL)(v),g=i.forwardRef((e,t)=>{let{scope:n,children:r,...l}=e,u=i.useRef(null),a=(0,c.s)(t,u),s=o(v,n);return i.useEffect(()=>(s.itemMap.set(u,{ref:u,...l}),()=>void s.itemMap.delete(u))),(0,d.jsx)(N,{...{[y]:""},ref:a,children:r})});return g.displayName=v,[{Provider:u,Slot:p,ItemSlot:g},function(t){let n=o(e+"CollectionConsumer",t);return i.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(y,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var m=new WeakMap;function p(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=v(t),l=r>=0?r:n+r;return l<0||l>=n?-1:l}(e,t);return -1===n?void 0:e[n]}function v(e){return e!=e||0===e?0:Math.trunc(e)}u=new WeakMap},8905:(e,t,n)=>{n.d(t,{C:()=>u});var r=n(2115),l=n(6101),o=n(2712),u=e=>{let{present:t,children:n}=e,u=function(e){var t,n;let[l,u]=r.useState(),a=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=i(a.current);s.current="mounted"===d?e:"none"},[d]),(0,o.N)(()=>{let t=a.current,n=c.current;if(n!==e){let r=s.current,l=i(t);e?f("MOUNT"):"none"===l||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==l?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,o.N)(()=>{if(l){var e;let t,n=null!=(e=l.ownerDocument.defaultView)?e:window,r=e=>{let r=i(a.current).includes(e.animationName);if(e.target===l&&r&&(f("ANIMATION_END"),!c.current)){let e=l.style.animationFillMode;l.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===l.style.animationFillMode&&(l.style.animationFillMode=e)})}},o=e=>{e.target===l&&(s.current=i(a.current))};return l.addEventListener("animationstart",o),l.addEventListener("animationcancel",r),l.addEventListener("animationend",r),()=>{n.clearTimeout(t),l.removeEventListener("animationstart",o),l.removeEventListener("animationcancel",r),l.removeEventListener("animationend",r)}}f("ANIMATION_END")},[l,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{a.current=e?getComputedStyle(e):null,u(e)},[])}}(t),a="function"==typeof n?n({present:u.isPresent}):r.Children.only(n),c=(0,l.s)(u.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,l=r&&"isReactWarning"in r&&r.isReactWarning;return l?e.ref:(l=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||u.isPresent?r.cloneElement(a,{ref:c}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}u.displayName="Presence"},9033:(e,t,n)=>{n.d(t,{c:()=>l});var r=n(2115);function l(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9708:(e,t,n)=>{n.d(t,{DX:()=>i,Dc:()=>c,TL:()=>u});var r=n(2115),l=n(6101),o=n(5155);function u(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var u;let e,i,a=(u=n,(i=(e=Object.getOwnPropertyDescriptor(u.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?u.ref:(i=(e=Object.getOwnPropertyDescriptor(u,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?u.props.ref:u.props.ref||u.ref),c=function(e,t){let n={...t};for(let r in t){let l=e[r],o=t[r];/^on[A-Z]/.test(r)?l&&o?n[r]=(...e)=>{let t=o(...e);return l(...e),t}:l&&(n[r]=l):"style"===r?n[r]={...l,...o}:"className"===r&&(n[r]=[l,o].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(c.ref=t?(0,l.t)(t,a):a),r.cloneElement(n,c)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:l,...u}=e,i=r.Children.toArray(l),a=i.find(s);if(a){let e=a.props.children,l=i.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...u,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,l):null})}return(0,o.jsx)(t,{...u,ref:n,children:l})});return n.displayName=`${e}.Slot`,n}var i=u("Slot"),a=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=a,t}function s(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}}}]);