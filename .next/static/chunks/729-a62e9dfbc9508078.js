try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="d9e8d9a7-4862-4dd8-8bc6-ce0dfbd8dfa0",e._sentryDebugIdIdentifier="sentry-dbid-d9e8d9a7-4862-4dd8-8bc6-ce0dfbd8dfa0")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[729],{235:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chart-no-axes-column",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]])},381:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1007:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1414:(e,t,n)=>{e.exports=n(2436)},2293:(e,t,n)=>{n.d(t,{Oh:()=>a});var r=n(2115),o=0;function a(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:u()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:u()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function u(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2432:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},2436:(e,t,n)=>{var r=n(2115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=r.useState,u=r.useEffect,i=r.useLayoutEffect,l=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=a({inst:{value:n,getSnapshot:t}}),o=r[0].inst,s=r[1];return i(function(){o.value=n,o.getSnapshot=t,c(o)&&s({inst:o})},[e,n,t]),u(function(){return c(o)&&s({inst:o}),e(function(){c(o)&&s({inst:o})})},[e]),l(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:s},2664:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let r=n(9991),o=n(7102);function a(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,o.hasBasePath)(n.pathname)}catch(e){return!1}}},2757:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return i},urlObjectKeys:function(){return u}});let r=n(6966)._(n(8859)),o=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:n}=e,a=e.protocol||"",u=e.pathname||"",i=e.hash||"",l=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:n&&(c=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(c+=":"+e.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let s=e.search||l&&"?"+l||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||o.test(a))&&!1!==c?(c="//"+(c||""),u&&"/"!==u[0]&&(u="/"+u)):c||(c=""),i&&"#"!==i[0]&&(i="#"+i),s&&"?"!==s[0]&&(s="?"+s),""+a+c+(u=u.replace(/[?#]/g,encodeURIComponent))+(s=s.replace("#","%23"))+i}let u=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return a(e)}},2775:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("git-branch",[["line",{x1:"6",x2:"6",y1:"3",y2:"15",key:"17qcm7"}],["circle",{cx:"18",cy:"6",r:"3",key:"1h7g24"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M18 9a9 9 0 0 1-9 9",key:"n2h4wq"}]])},2892:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("plug",[["path",{d:"M12 22v-5",key:"1ega77"}],["path",{d:"M9 8V2",key:"14iosj"}],["path",{d:"M15 8V2",key:"18g5xt"}],["path",{d:"M18 8v5a4 4 0 0 1-4 4h-4a4 4 0 0 1-4-4V8Z",key:"osxo6l"}]])},2919:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3052:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3180:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},3795:(e,t,n)=>{n.d(t,{A:()=>H});var r,o,a=function(){return(a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function u(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var i=("function"==typeof SuppressedError&&SuppressedError,n(2115)),l="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,f=new WeakMap;function p(e){return e}var v=function(e){void 0===e&&(e={});var t,n,r,o,u=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var a=function(){var n=t;t=[],n.forEach(e)},u=function(){return Promise.resolve().then(a)};u(),r={push:function(e){t.push(e),u()},filter:function(e){return t=t.filter(e),r}}}});return u.options=a({async:!0,ssr:!1},e),u}(),h=function(){},m=i.forwardRef(function(e,t){var n,r,o,l,c=i.useRef(null),p=i.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),m=p[0],g=p[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,C=e.shards,R=e.sideCar,M=e.noRelative,j=e.noIsolation,k=e.inert,A=e.allowPinchZoom,S=e.as,P=e.gapMode,D=u(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),_=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,l=o.facade,d(function(){var e=f.get(l);if(e){var t=new Set(e),r=new Set(n),o=l.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(l,n)},[n]),l),I=a(a({},D),m);return i.createElement(i.Fragment,null,E&&i.createElement(R,{sideCar:v,removeScrollBar:x,shards:C,noRelative:M,noIsolation:j,inert:k,setCallbacks:g,allowPinchZoom:!!A,lockRef:c,gapMode:P}),y?i.cloneElement(i.Children.only(w),a(a({},I),{ref:_})):i.createElement(void 0===S?"div":S,a({},I,{className:b,ref:_}),w))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:c,zeroRight:l};var g=function(e){var t=e.sideCar,n=u(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,a({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,u;(a=t).styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r)),u=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(u)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},C=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},R=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=C(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},M=b(),j="data-scroll-locked",k=function(e,t,n,r){var o=e.left,a=e.top,u=e.right,i=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(i,"px ").concat(r,";\n  }\n  body[").concat(j,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(u,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(i,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(i,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(j,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(i,"px;\n  }\n")},A=function(){var e=parseInt(document.body.getAttribute(j)||"0",10);return isFinite(e)?e:0},S=function(){i.useEffect(function(){return document.body.setAttribute(j,(A()+1).toString()),function(){var e=A()-1;e<=0?document.body.removeAttribute(j):document.body.setAttribute(j,e.toString())}},[])},P=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;S();var a=i.useMemo(function(){return R(o)},[o]);return i.createElement(M,{styles:k(a,!t,o,n?"":"!important")})},D=!1;if("undefined"!=typeof window)try{var _=Object.defineProperty({},"passive",{get:function(){return D=!0,!0}});window.addEventListener("test",_,_),window.removeEventListener("test",_,_)}catch(e){D=!1}var I=!!D&&{passive:!1},N=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},O=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),T(e,r)){var o=L(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},T=function(e,t){return"v"===e?N(t,"overflowY"):N(t,"overflowX")},L=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,n,r,o){var a,u=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),i=u*r,l=n.target,c=t.contains(l),s=!1,d=i>0,f=0,p=0;do{var v=L(e,l),h=v[0],m=v[1]-v[2]-u*h;(h||m)&&T(e,l)&&(f+=m,p+=h),l=l.parentNode.host||l.parentNode}while(!c&&l!==document.body||c&&(t.contains(l)||t===l));return d&&(o&&1>Math.abs(f)||!o&&i>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-i>p)&&(s=!0),s},K=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},G=function(e){return[e.deltaX,e.deltaY]},U=function(e){return e&&"current"in e?e.current:e},B=0,W=[];let q=(r=function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(B++)[0],a=i.useState(b)[0],u=i.useRef(e);i.useEffect(function(){u.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(U),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!u.current.allowPinchZoom;var o,a=K(e),i=n.current,l="deltaX"in e?e.deltaX:i[0]-a[0],c="deltaY"in e?e.deltaY:i[1]-a[1],s=e.target,d=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=O(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=O(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(l||c)&&(r.current=o),!o)return!0;var p=r.current||o;return F(p,t,e,"h"===p?l:c,!0)},[]),c=i.useCallback(function(e){if(W.length&&W[W.length-1]===a){var n="deltaY"in e?G(e):K(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(U).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=i.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=i.useCallback(function(e){n.current=K(e),r.current=void 0},[]),f=i.useCallback(function(t){s(t.type,G(t),t.target,l(t,e.lockRef.current))},[]),p=i.useCallback(function(t){s(t.type,K(t),t.target,l(t,e.lockRef.current))},[]);i.useEffect(function(){return W.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,I),document.addEventListener("touchmove",c,I),document.addEventListener("touchstart",d,I),function(){W=W.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,I),document.removeEventListener("touchmove",c,I),document.removeEventListener("touchstart",d,I)}},[]);var v=e.removeScrollBar,h=e.inert;return i.createElement(i.Fragment,null,h?i.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?i.createElement(P,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},v.useMedium(r),g);var V=i.forwardRef(function(e,t){return i.createElement(m,a({},e,{ref:t,sideCar:q}))});V.classNames=m.classNames;let H=V},4011:(e,t,n)=>{n.d(t,{H4:()=>R,_V:()=>C,bL:()=>E});var r=n(2115),o=n(6081),a=n(9033),u=n(2712),i=n(3655),l=n(1414);function c(){return()=>{}}var s=n(5155),d="Avatar",[f,p]=(0,o.A)(d),[v,h]=f(d),m=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[a,u]=r.useState("idle");return(0,s.jsx)(v,{scope:n,imageLoadingStatus:a,onImageLoadingStatusChange:u,children:(0,s.jsx)(i.sG.span,{...o,ref:t})})});m.displayName=d;var g="AvatarImage",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:d=()=>{},...f}=e,p=h(g,n),v=function(e,t){let{referrerPolicy:n,crossOrigin:o}=t,a=(0,l.useSyncExternalStore)(c,()=>!0,()=>!1),i=r.useRef(null),s=a?(i.current||(i.current=new window.Image),i.current):null,[d,f]=r.useState(()=>x(s,e));return(0,u.N)(()=>{f(x(s,e))},[s,e]),(0,u.N)(()=>{let e=e=>()=>{f(e)};if(!s)return;let t=e("loaded"),r=e("error");return s.addEventListener("load",t),s.addEventListener("error",r),n&&(s.referrerPolicy=n),"string"==typeof o&&(s.crossOrigin=o),()=>{s.removeEventListener("load",t),s.removeEventListener("error",r)}},[s,o,n]),d}(o,f),m=(0,a.c)(e=>{d(e),p.onImageLoadingStatusChange(e)});return(0,u.N)(()=>{"idle"!==v&&m(v)},[v,m]),"loaded"===v?(0,s.jsx)(i.sG.img,{...f,ref:t,src:o}):null});y.displayName=g;var w="AvatarFallback",b=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...a}=e,u=h(w,n),[l,c]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),l&&"loaded"!==u.imageLoadingStatus?(0,s.jsx)(i.sG.span,{...a,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=w;var E=m,C=y,R=b},4315:(e,t,n)=>{n.d(t,{jH:()=>a});var r=n(2115);n(5155);var o=r.createContext(void 0);function a(e){let t=r.useContext(o);return e||t||"ltr"}},4783:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},4788:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-help",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4835:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5040:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5452:(e,t,n)=>{n.d(t,{UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>ea,hE:()=>er,hJ:()=>et,l9:()=>$});var r=n(2115),o=n(5185),a=n(6101),u=n(6081),i=n(1285),l=n(5845),c=n(9178),s=n(7900),d=n(4378),f=n(8905),p=n(3655),v=n(2293),h=n(3795),m=n(8168),g=n(9708),y=n(5155),w="Dialog",[b,x]=(0,u.A)(w),[E,C]=b(w),R=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:a,onOpenChange:u,modal:c=!0}=e,s=r.useRef(null),d=r.useRef(null),[f,p]=(0,l.i)({prop:o,defaultProp:null!=a&&a,onChange:u,caller:w});return(0,y.jsx)(E,{scope:t,triggerRef:s,contentRef:d,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:n})};R.displayName=w;var M="DialogTrigger",j=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,u=C(M,n),i=(0,a.s)(t,u.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":u.open,"aria-controls":u.contentId,"data-state":H(u.open),...r,ref:i,onClick:(0,o.m)(e.onClick,u.onOpenToggle)})});j.displayName=M;var k="DialogPortal",[A,S]=b(k,{forceMount:void 0}),P=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:a}=e,u=C(k,t);return(0,y.jsx)(A,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,y.jsx)(f.C,{present:n||u.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:a,children:e})}))})};P.displayName=k;var D="DialogOverlay",_=r.forwardRef((e,t)=>{let n=S(D,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=C(D,e.__scopeDialog);return a.modal?(0,y.jsx)(f.C,{present:r||a.open,children:(0,y.jsx)(N,{...o,ref:t})}):null});_.displayName=D;var I=(0,g.TL)("DialogOverlay.RemoveScroll"),N=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=C(D,n);return(0,y.jsx)(h.A,{as:I,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":H(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),O="DialogContent",T=r.forwardRef((e,t)=>{let n=S(O,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=C(O,e.__scopeDialog);return(0,y.jsx)(f.C,{present:r||a.open,children:a.modal?(0,y.jsx)(L,{...o,ref:t}):(0,y.jsx)(F,{...o,ref:t})})});T.displayName=O;var L=r.forwardRef((e,t)=>{let n=C(O,e.__scopeDialog),u=r.useRef(null),i=(0,a.s)(t,n.contentRef,u);return r.useEffect(()=>{let e=u.current;if(e)return(0,m.Eq)(e)},[]),(0,y.jsx)(K,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=r.forwardRef((e,t)=>{let n=C(O,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return(0,y.jsx)(K,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,u;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(u=n.triggerRef.current)||u.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var r,u;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let i=t.target;(null==(u=n.triggerRef.current)?void 0:u.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),K=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:u,onCloseAutoFocus:i,...l}=e,d=C(O,n),f=r.useRef(null),p=(0,a.s)(t,f);return(0,v.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(s.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:u,onUnmountAutoFocus:i,children:(0,y.jsx)(c.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":H(d.open),...l,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(Y,{titleId:d.titleId}),(0,y.jsx)(J,{contentRef:f,descriptionId:d.descriptionId})]})]})}),G="DialogTitle",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=C(G,n);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});U.displayName=G;var B="DialogDescription",W=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=C(B,n);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});W.displayName=B;var q="DialogClose",V=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=C(q,n);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function H(e){return e?"open":"closed"}V.displayName=q;var z="DialogTitleWarning",[Z,X]=(0,u.q)(z,{contentName:O,titleName:G,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,n=X(z),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&document.getElementById(t)},[o,t]),null},J=e=>{let{contentRef:t,descriptionId:n}=e,o=X("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&document.getElementById(n)},[a,t,n]),null},Q=R,$=j,ee=P,et=_,en=T,er=U,eo=W,ea=V},5695:(e,t,n)=>{var r=n(8999);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},5868:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},6654:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let r=n(2115);function o(e,t){let n=(0,r.useRef)(null),o=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(n.current=a(e,r)),t&&(o.current=a(t,r))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6874:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return m},useLinkStatus:function(){return y}});let r=n(6966),o=n(5155),a=r._(n(2115)),u=n(2757),i=n(5227),l=n(9818),c=n(6654),s=n(9991),d=n(5929);n(3230);let f=n(4930),p=n(2664),v=n(6634);function h(e){return"string"==typeof e?e:(0,u.formatUrl)(e)}function m(e){let t,n,r,[u,m]=(0,a.useOptimistic)(f.IDLE_LINK_STATUS),y=(0,a.useRef)(null),{href:w,as:b,children:x,prefetch:E=null,passHref:C,replace:R,shallow:M,scroll:j,onClick:k,onMouseEnter:A,onTouchStart:S,legacyBehavior:P=!1,onNavigate:D,ref:_,unstable_dynamicOnHover:I,...N}=e;t=x,P&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let O=a.default.useContext(i.AppRouterContext),T=!1!==E,L=null===E?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:F,as:K}=a.default.useMemo(()=>{let e=h(w);return{href:e,as:b?h(b):e}},[w,b]);P&&(n=a.default.Children.only(t));let G=P?n&&"object"==typeof n&&n.ref:_,U=a.default.useCallback(e=>(null!==O&&(y.current=(0,f.mountLinkInstance)(e,F,O,L,T,m)),()=>{y.current&&((0,f.unmountLinkForCurrentNavigation)(y.current),y.current=null),(0,f.unmountPrefetchableInstance)(e)}),[T,F,O,L,m]),B={ref:(0,c.useMergedRef)(U,G),onClick(e){P||"function"!=typeof k||k(e),P&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),O&&(e.defaultPrevented||function(e,t,n,r,o,u,i){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),a.default.startTransition(()=>{if(i){let e=!1;if(i({preventDefault:()=>{e=!0}}),e)return}(0,v.dispatchNavigateAction)(n||t,o?"replace":"push",null==u||u,r.current)})}}(e,F,K,y,R,j,D))},onMouseEnter(e){P||"function"!=typeof A||A(e),P&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),O&&T&&(0,f.onNavigationIntent)(e.currentTarget,!0===I)},onTouchStart:function(e){P||"function"!=typeof S||S(e),P&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),O&&T&&(0,f.onNavigationIntent)(e.currentTarget,!0===I)}};return(0,s.isAbsoluteUrl)(K)?B.href=K:P&&!C&&("a"!==n.type||"href"in n.props)||(B.href=(0,d.addBasePath)(K)),r=P?a.default.cloneElement(n,B):(0,o.jsx)("a",{...N,...B,children:t}),(0,o.jsx)(g.Provider,{value:u,children:r})}n(3180);let g=(0,a.createContext)(f.IDLE_LINK_STATUS),y=()=>(0,a.useContext)(g);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7340:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},7489:(e,t,n)=>{n.d(t,{b:()=>c});var r=n(2115),o=n(3655),a=n(5155),u="horizontal",i=["horizontal","vertical"],l=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:l=u,...c}=e,s=(n=l,i.includes(n))?l:u;return(0,a.jsx)(o.sG.div,{"data-orientation":s,...r?{role:"none"}:{"aria-orientation":"vertical"===s?s:void 0,role:"separator"},...c,ref:t})});l.displayName="Separator";var c=l},7900:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(2115),o=n(6101),a=n(3655),u=n(9033),i=n(5155),l="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:m,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,u.c)(m),E=(0,u.c)(g),C=r.useRef(null),R=(0,o.s)(t,e=>b(e)),M=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(M.paused||!w)return;let t=e.target;w.contains(t)?C.current=t:v(C.current,{select:!0})},t=function(e){if(M.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||v(C.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&v(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,M.paused]),r.useEffect(()=>{if(w){h.add(M);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(l,s);w.addEventListener(l,x),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(v(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&v(w))}return()=>{w.removeEventListener(l,x),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||v(null!=e?e:document.body,{select:!0}),w.removeEventListener(c,E),h.remove(M)},0)}}},[w,x,E,M]);let j=r.useCallback(e=>{if(!n&&!d||M.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,a]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&a?e.shiftKey||r!==a?e.shiftKey&&r===o&&(e.preventDefault(),n&&v(a,{select:!0})):(e.preventDefault(),n&&v(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,M.paused]);return(0,i.jsx)(a.sG.div,{tabIndex:-1,...y,ref:R,onKeyDown:j})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function v(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=m(e,t)).unshift(t)},remove(t){var n;null==(n=(e=m(e,t))[0])||n.resume()}}}();function m(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},8168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,a=new WeakMap,u={},i=0,l=function(e){return e&&(e.host||l(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=l(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});u[n]||(u[n]=new WeakMap);var s=u[n],d=[],f=new Set,p=new Set(c),v=function(e){!e||f.has(e)||(f.add(e),v(e.parentNode))};c.forEach(v);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(r),u=null!==t&&"false"!==t,i=(o.get(e)||0)+1,l=(s.get(e)||0)+1;o.set(e,i),s.set(e,l),d.push(e),1===i&&u&&a.set(e,!0),1===l&&e.setAttribute(n,"true"),u||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),i++,function(){d.forEach(function(e){var t=o.get(e)-1,u=s.get(e)-1;o.set(e,t),s.set(e,u),t||(a.has(e)||e.removeAttribute(r),a.delete(e)),u||e.removeAttribute(n)}),--i||(o=new WeakMap,o=new WeakMap,a=new WeakMap,u={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),a=t||r(e);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live], script"))),c(o,a,n,"aria-hidden")):function(){return null}}},8698:(e,t,n)=>{n.d(t,{H_:()=>e8,UC:()=>e5,YJ:()=>e9,q7:()=>e4,VF:()=>te,JU:()=>e3,ZL:()=>e2,z6:()=>e7,hN:()=>e6,bL:()=>e0,wv:()=>tt,Pb:()=>tn,G5:()=>to,ZP:()=>tr,l9:()=>e1});var r=n(2115),o=n(5185),a=n(6101),u=n(6081),i=n(5845),l=n(3655),c=n(7328),s=n(4315),d=n(9178),f=n(2293),p=n(7900),v=n(1285),h=n(8795),m=n(4378),g=n(8905),y=n(9196),w=n(9708),b=n(9033),x=n(8168),E=n(3795),C=n(5155),R=["Enter"," "],M=["ArrowUp","PageDown","End"],j=["ArrowDown","PageUp","Home",...M],k={ltr:[...R,"ArrowRight"],rtl:[...R,"ArrowLeft"]},A={ltr:["ArrowLeft"],rtl:["ArrowRight"]},S="Menu",[P,D,_]=(0,c.N)(S),[I,N]=(0,u.A)(S,[_,h.Bk,y.RG]),O=(0,h.Bk)(),T=(0,y.RG)(),[L,F]=I(S),[K,G]=I(S),U=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:a,onOpenChange:u,modal:i=!0}=e,l=O(t),[c,d]=r.useState(null),f=r.useRef(!1),p=(0,b.c)(u),v=(0,s.jH)(a);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,C.jsx)(h.bL,{...l,children:(0,C.jsx)(L,{scope:t,open:n,onOpenChange:p,content:c,onContentChange:d,children:(0,C.jsx)(K,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:v,modal:i,children:o})})})};U.displayName=S;var B=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=O(n);return(0,C.jsx)(h.Mz,{...o,...r,ref:t})});B.displayName="MenuAnchor";var W="MenuPortal",[q,V]=I(W,{forceMount:void 0}),H=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,a=F(W,t);return(0,C.jsx)(q,{scope:t,forceMount:n,children:(0,C.jsx)(g.C,{present:n||a.open,children:(0,C.jsx)(m.Z,{asChild:!0,container:o,children:r})})})};H.displayName=W;var z="MenuContent",[Z,X]=I(z),Y=r.forwardRef((e,t)=>{let n=V(z,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=F(z,e.__scopeMenu),u=G(z,e.__scopeMenu);return(0,C.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(g.C,{present:r||a.open,children:(0,C.jsx)(P.Slot,{scope:e.__scopeMenu,children:u.modal?(0,C.jsx)(J,{...o,ref:t}):(0,C.jsx)(Q,{...o,ref:t})})})})}),J=r.forwardRef((e,t)=>{let n=F(z,e.__scopeMenu),u=r.useRef(null),i=(0,a.s)(t,u);return r.useEffect(()=>{let e=u.current;if(e)return(0,x.Eq)(e)},[]),(0,C.jsx)(ee,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Q=r.forwardRef((e,t)=>{let n=F(z,e.__scopeMenu);return(0,C.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),$=(0,w.TL)("MenuContent.ScrollLock"),ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:u=!1,trapFocus:i,onOpenAutoFocus:l,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:v,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:b,onDismiss:x,disableOutsideScroll:R,...k}=e,A=F(z,n),S=G(z,n),P=O(n),_=T(n),I=D(n),[N,L]=r.useState(null),K=r.useRef(null),U=(0,a.s)(t,K,A.onContentChange),B=r.useRef(0),W=r.useRef(""),q=r.useRef(0),V=r.useRef(null),H=r.useRef("right"),X=r.useRef(0),Y=R?E.A:r.Fragment,J=e=>{var t,n;let r=W.current+e,o=I().filter(e=>!e.disabled),a=document.activeElement,u=null==(t=o.find(e=>e.ref.current===a))?void 0:t.textValue,i=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=n?e.indexOf(n):-1,u=(r=Math.max(a,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(u=u.filter(e=>e!==n));let i=u.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return i!==n?i:void 0}(o.map(e=>e.textValue),r,u),l=null==(n=o.find(e=>e.textValue===i))?void 0:n.ref.current;!function e(t){W.current=t,window.clearTimeout(B.current),""!==t&&(B.current=window.setTimeout(()=>e(""),1e3))}(r),l&&setTimeout(()=>l.focus())};r.useEffect(()=>()=>window.clearTimeout(B.current),[]),(0,f.Oh)();let Q=r.useCallback(e=>{var t,n;return H.current===(null==(t=V.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let u=t[e],i=t[a],l=u.x,c=u.y,s=i.x,d=i.y;c>r!=d>r&&n<(s-l)*(r-c)/(d-c)+l&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(n=V.current)?void 0:n.area)},[]);return(0,C.jsx)(Z,{scope:n,searchRef:W,onItemEnter:r.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:r.useCallback(e=>{var t;Q(e)||(null==(t=K.current)||t.focus(),L(null))},[Q]),onTriggerLeave:r.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:q,onPointerGraceIntentChange:r.useCallback(e=>{V.current=e},[]),children:(0,C.jsx)(Y,{...R?{as:$,allowPinchZoom:!0}:void 0,children:(0,C.jsx)(p.n,{asChild:!0,trapped:i,onMountAutoFocus:(0,o.m)(l,e=>{var t;e.preventDefault(),null==(t=K.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,C.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:b,onDismiss:x,children:(0,C.jsx)(y.bL,{asChild:!0,..._,dir:S.dir,orientation:"vertical",loop:u,currentTabStopId:N,onCurrentTabStopIdChange:L,onEntryFocus:(0,o.m)(v,e=>{S.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,C.jsx)(h.UC,{role:"menu","aria-orientation":"vertical","data-state":eA(A.open),"data-radix-menu-content":"",dir:S.dir,...P,...k,ref:U,style:{outline:"none",...k.style},onKeyDown:(0,o.m)(k.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&J(e.key));let o=K.current;if(e.target!==o||!j.includes(e.key))return;e.preventDefault();let a=I().filter(e=>!e.disabled).map(e=>e.ref.current);M.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(B.current),W.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eD(e=>{let t=e.target,n=X.current!==e.clientX;e.currentTarget.contains(t)&&n&&(H.current=e.clientX>X.current?"right":"left",X.current=e.clientX)}))})})})})})})});Y.displayName=z;var et=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,C.jsx)(l.sG.div,{role:"group",...r,ref:t})});et.displayName="MenuGroup";var en=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,C.jsx)(l.sG.div,{...r,ref:t})});en.displayName="MenuLabel";var er="MenuItem",eo="menu.itemSelect",ea=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:u,...i}=e,c=r.useRef(null),s=G(er,e.__scopeMenu),d=X(er,e.__scopeMenu),f=(0,a.s)(t,c),p=r.useRef(!1);return(0,C.jsx)(eu,{...i,ref:f,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=c.current;if(!n&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==u?void 0:u(e),{once:!0}),(0,l.hO)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var t;p.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;n||t&&" "===e.key||R.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=er;var eu=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:u=!1,textValue:i,...c}=e,s=X(er,n),d=T(n),f=r.useRef(null),p=(0,a.s)(t,f),[v,h]=r.useState(!1),[m,g]=r.useState("");return r.useEffect(()=>{let e=f.current;if(e){var t;g((null!=(t=e.textContent)?t:"").trim())}},[c.children]),(0,C.jsx)(P.ItemSlot,{scope:n,disabled:u,textValue:null!=i?i:m,children:(0,C.jsx)(y.q7,{asChild:!0,...d,focusable:!u,children:(0,C.jsx)(l.sG.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":u||void 0,"data-disabled":u?"":void 0,...c,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eD(e=>{u?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eD(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>h(!0)),onBlur:(0,o.m)(e.onBlur,()=>h(!1))})})})}),ei=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...a}=e;return(0,C.jsx)(eh,{scope:e.__scopeMenu,checked:n,children:(0,C.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eS(n)?"mixed":n,...a,ref:t,"data-state":eP(n),onSelect:(0,o.m)(a.onSelect,()=>null==r?void 0:r(!!eS(n)||!n),{checkForDefaultPrevented:!1})})})});ei.displayName="MenuCheckboxItem";var el="MenuRadioGroup",[ec,es]=I(el,{value:void 0,onValueChange:()=>{}}),ed=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,a=(0,b.c)(r);return(0,C.jsx)(ec,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,C.jsx)(et,{...o,ref:t})})});ed.displayName=el;var ef="MenuRadioItem",ep=r.forwardRef((e,t)=>{let{value:n,...r}=e,a=es(ef,e.__scopeMenu),u=n===a.value;return(0,C.jsx)(eh,{scope:e.__scopeMenu,checked:u,children:(0,C.jsx)(ea,{role:"menuitemradio","aria-checked":u,...r,ref:t,"data-state":eP(u),onSelect:(0,o.m)(r.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,n)},{checkForDefaultPrevented:!1})})})});ep.displayName=ef;var ev="MenuItemIndicator",[eh,em]=I(ev,{checked:!1}),eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,a=em(ev,n);return(0,C.jsx)(g.C,{present:r||eS(a.checked)||!0===a.checked,children:(0,C.jsx)(l.sG.span,{...o,ref:t,"data-state":eP(a.checked)})})});eg.displayName=ev;var ey=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,C.jsx)(l.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});ey.displayName="MenuSeparator";var ew=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=O(n);return(0,C.jsx)(h.i3,{...o,...r,ref:t})});ew.displayName="MenuArrow";var eb="MenuSub",[ex,eE]=I(eb),eC=e=>{let{__scopeMenu:t,children:n,open:o=!1,onOpenChange:a}=e,u=F(eb,t),i=O(t),[l,c]=r.useState(null),[s,d]=r.useState(null),f=(0,b.c)(a);return r.useEffect(()=>(!1===u.open&&f(!1),()=>f(!1)),[u.open,f]),(0,C.jsx)(h.bL,{...i,children:(0,C.jsx)(L,{scope:t,open:o,onOpenChange:f,content:s,onContentChange:d,children:(0,C.jsx)(ex,{scope:t,contentId:(0,v.B)(),triggerId:(0,v.B)(),trigger:l,onTriggerChange:c,children:n})})})};eC.displayName=eb;var eR="MenuSubTrigger",eM=r.forwardRef((e,t)=>{let n=F(eR,e.__scopeMenu),u=G(eR,e.__scopeMenu),i=eE(eR,e.__scopeMenu),l=X(eR,e.__scopeMenu),c=r.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=l,f={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,C.jsx)(B,{asChild:!0,...f,children:(0,C.jsx)(eu,{id:i.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":i.contentId,"data-state":eA(n.open),...e,ref:(0,a.t)(t,i.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eD(t=>{l.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||c.current||(l.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eD(e=>{var t,r;p();let o=null==(t=n.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(r=n.content)?void 0:r.dataset.side,a="right"===t,u=o[a?"left":"right"],i=o[a?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:u,y:o.top},{x:i,y:o.top},{x:i,y:o.bottom},{x:u,y:o.bottom}],side:t}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let r=""!==l.searchRef.current;if(!e.disabled&&(!r||" "!==t.key)&&k[u.dir].includes(t.key)){var o;n.onOpenChange(!0),null==(o=n.content)||o.focus(),t.preventDefault()}})})})});eM.displayName=eR;var ej="MenuSubContent",ek=r.forwardRef((e,t)=>{let n=V(z,e.__scopeMenu),{forceMount:u=n.forceMount,...i}=e,l=F(z,e.__scopeMenu),c=G(z,e.__scopeMenu),s=eE(ej,e.__scopeMenu),d=r.useRef(null),f=(0,a.s)(t,d);return(0,C.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(g.C,{present:u||l.open,children:(0,C.jsx)(P.Slot,{scope:e.__scopeMenu,children:(0,C.jsx)(ee,{id:s.contentId,"aria-labelledby":s.triggerId,...i,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;c.isUsingKeyboardRef.current&&(null==(t=d.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=A[c.dir].includes(e.key);if(t&&n){var r;l.onOpenChange(!1),null==(r=s.trigger)||r.focus(),e.preventDefault()}})})})})})});function eA(e){return e?"open":"closed"}function eS(e){return"indeterminate"===e}function eP(e){return eS(e)?"indeterminate":e?"checked":"unchecked"}function eD(e){return t=>"mouse"===t.pointerType?e(t):void 0}ek.displayName=ej;var e_="DropdownMenu",[eI,eN]=(0,u.A)(e_,[N]),eO=N(),[eT,eL]=eI(e_),eF=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:a,defaultOpen:u,onOpenChange:l,modal:c=!0}=e,s=eO(t),d=r.useRef(null),[f,p]=(0,i.i)({prop:a,defaultProp:null!=u&&u,onChange:l,caller:e_});return(0,C.jsx)(eT,{scope:t,triggerId:(0,v.B)(),triggerRef:d,contentId:(0,v.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,C.jsx)(U,{...s,open:f,onOpenChange:p,dir:o,modal:c,children:n})})};eF.displayName=e_;var eK="DropdownMenuTrigger",eG=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...u}=e,i=eL(eK,n),c=eO(n);return(0,C.jsx)(B,{asChild:!0,...c,children:(0,C.jsx)(l.sG.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...u,ref:(0,a.t)(t,i.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eG.displayName=eK;var eU=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eO(t);return(0,C.jsx)(H,{...r,...n})};eU.displayName="DropdownMenuPortal";var eB="DropdownMenuContent",eW=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...a}=e,u=eL(eB,n),i=eO(n),l=r.useRef(!1);return(0,C.jsx)(Y,{id:u.contentId,"aria-labelledby":u.triggerId,...i,...a,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;l.current||null==(t=u.triggerRef.current)||t.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!u.modal||r)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eW.displayName=eB;var eq=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(et,{...o,...r,ref:t})});eq.displayName="DropdownMenuGroup";var eV=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(en,{...o,...r,ref:t})});eV.displayName="DropdownMenuLabel";var eH=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ea,{...o,...r,ref:t})});eH.displayName="DropdownMenuItem";var ez=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ei,{...o,...r,ref:t})});ez.displayName="DropdownMenuCheckboxItem";var eZ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ed,{...o,...r,ref:t})});eZ.displayName="DropdownMenuRadioGroup";var eX=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ep,{...o,...r,ref:t})});eX.displayName="DropdownMenuRadioItem";var eY=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(eg,{...o,...r,ref:t})});eY.displayName="DropdownMenuItemIndicator";var eJ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ey,{...o,...r,ref:t})});eJ.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ew,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var eQ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(eM,{...o,...r,ref:t})});eQ.displayName="DropdownMenuSubTrigger";var e$=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,C.jsx)(ek,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e$.displayName="DropdownMenuSubContent";var e0=eF,e1=eG,e2=eU,e5=eW,e9=eq,e3=eV,e4=eH,e8=ez,e7=eZ,e6=eX,te=eY,tt=eJ,tn=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:a}=e,u=eO(t),[l,c]=(0,i.i)({prop:r,defaultProp:null!=a&&a,onChange:o,caller:"DropdownMenuSub"});return(0,C.jsx)(eC,{...u,open:l,onOpenChange:c,children:n})},tr=eQ,to=e$},8859:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[n,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(n,r(e));else t.set(n,r(o));return t}function a(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},9196:(e,t,n)=>{n.d(t,{RG:()=>x,bL:()=>P,q7:()=>D});var r=n(2115),o=n(5185),a=n(7328),u=n(6101),i=n(6081),l=n(1285),c=n(3655),s=n(9033),d=n(5845),f=n(4315),p=n(5155),v="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[g,y,w]=(0,a.N)(m),[b,x]=(0,i.A)(m,[w]),[E,C]=b(m),R=r.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(M,{...e,ref:t})})}));R.displayName=m;var M=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:i=!1,dir:l,currentTabStopId:g,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:b,onEntryFocus:x,preventScrollOnEntryFocus:C=!1,...R}=e,M=r.useRef(null),j=(0,u.s)(t,M),k=(0,f.jH)(l),[A,P]=(0,d.i)({prop:g,defaultProp:null!=w?w:null,onChange:b,caller:m}),[D,_]=r.useState(!1),I=(0,s.c)(x),N=y(n),O=r.useRef(!1),[T,L]=r.useState(0);return r.useEffect(()=>{let e=M.current;if(e)return e.addEventListener(v,I),()=>e.removeEventListener(v,I)},[I]),(0,p.jsx)(E,{scope:n,orientation:a,dir:k,loop:i,currentTabStopId:A,onItemFocus:r.useCallback(e=>P(e),[P]),onItemShiftTab:r.useCallback(()=>_(!0),[]),onFocusableItemAdd:r.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>L(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:D||0===T?-1:0,"data-orientation":a,...R,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!O.current;if(e.target===e.currentTarget&&t&&!D){let t=new CustomEvent(v,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=N().filter(e=>e.focusable);S([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),C)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>_(!1))})})}),j="RovingFocusGroupItem",k=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:u=!1,tabStopId:i,children:s,...d}=e,f=(0,l.B)(),v=i||f,h=C(j,n),m=h.currentTabStopId===v,w=y(n),{onFocusableItemAdd:b,onFocusableItemRemove:x,currentTabStopId:E}=h;return r.useEffect(()=>{if(a)return b(),()=>x()},[a,b,x]),(0,p.jsx)(g.ItemSlot,{scope:n,id:v,focusable:a,active:u,children:(0,p.jsx)(c.sG.span,{tabIndex:m?0:-1,"data-orientation":h.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?h.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>h.onItemFocus(v)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void h.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return A[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=h.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>S(n))}}),children:"function"==typeof s?s({isCurrentTabStop:m,hasTabStop:null!=E}):s})})});k.displayName=j;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function S(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var P=R,D=k},9428:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},9991:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return v},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return g},NormalizeError:function(){return h},PageNotFoundError:function(){return m},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return u},getURL:function(){return i},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return s},stringifyError:function(){return w}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),a=0;a<r;a++)o[a]=arguments[a];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>o.test(e);function u(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function i(){let{href:e}=window.location,t=u();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function s(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&c(n))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class v extends Error{}class h extends Error{}class m extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function w(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);