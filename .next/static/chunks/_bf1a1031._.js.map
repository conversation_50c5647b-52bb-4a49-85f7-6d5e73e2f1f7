{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/FalcodeSec/frontend/src/app/auth/callback/page.tsx"], "sourcesContent": ["'use client'\n\nimport { Suspense, useEffect } from \"react\";\nimport { useRouter, useSearchParams } from \"next/navigation\";\n\nfunction AuthCallbackContent() {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n\n  useEffect(() => {\n    // Get the session token from URL parameters\n    const handleAuthCallback = async () => {\n      try {\n        // Get token from URL parameter\n        const token = searchParams.get('token');\n\n        if (!token) {\n          console.error(\"No token found in URL parameters\");\n          console.log(\"Available search params:\", Array.from(searchParams.entries()));\n          router.push(\"/login\");\n          return;\n        }\n\n        console.log(\"Token found, length:\", token.length);\n        console.log(\"Token preview:\", token.substring(0, 20) + \"...\");\n\n        // Validate the session token with the backend\n        const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000';\n        console.log(\"Backend URL:\", BACKEND_URL);\n\n        const response = await fetch(`${BACKEND_URL}/api/v1/login/validate-session`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${token}`,\n            'ngrok-skip-browser-warning': 'true', // Skip ngrok warning\n          },\n        });\n\n        console.log(\"Session validation response:\", response.status);\n\n        if (response.ok) {\n          // Store the token in localStorage for future API calls\n          localStorage.setItem('session_token', token);\n\n          // Session is valid, redirect to dashboard\n          console.log(\"Redirecting to dashboard...\");\n          router.push(\"/dashboard\");\n        } else {\n          // Session validation failed, redirect to login\n          const errorText = await response.text();\n          console.error(\"Session validation failed:\", response.status, errorText);\n          router.push(\"/login\");\n        }\n      } catch (error) {\n        console.error(\"Error during session validation:\", error);\n        router.push(\"/login\");\n      }\n    };\n\n    handleAuthCallback();\n  }, [router, searchParams]);\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-200 to-slate-100\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\n        <p className=\"text-slate-600 font-medium\">Completing authentication...</p>\n      </div>\n    </div>\n  );\n}\n\nfunction AuthCallbackFallback() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-200 to-slate-100\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\n        <p className=\"text-slate-600 font-medium\">Loading...</p>\n      </div>\n    </div>\n  );\n}\n\nexport default function AuthCallbackPage() {\n  return (\n    <Suspense fallback={<AuthCallbackFallback />}>\n      <AuthCallbackContent />\n    </Suspense>\n  );\n}\n\n"], "names": [], "mappings": ";;;AA2B4B;;AAzB5B;AACA;;;AAHA;;;AAKA,SAAS;;IACP,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,4CAA4C;YAC5C,MAAM;oEAAqB;oBACzB,IAAI;wBACF,+BAA+B;wBAC/B,MAAM,QAAQ,aAAa,GAAG,CAAC;wBAE/B,IAAI,CAAC,OAAO;4BACV,QAAQ,KAAK,CAAC;4BACd,QAAQ,GAAG,CAAC,4BAA4B,MAAM,IAAI,CAAC,aAAa,OAAO;4BACvE,OAAO,IAAI,CAAC;4BACZ;wBACF;wBAEA,QAAQ,GAAG,CAAC,wBAAwB,MAAM,MAAM;wBAChD,QAAQ,GAAG,CAAC,kBAAkB,MAAM,SAAS,CAAC,GAAG,MAAM;wBAEvD,8CAA8C;wBAC9C,MAAM,cAAc,gFAAuC;wBAC3D,QAAQ,GAAG,CAAC,gBAAgB;wBAE5B,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,8BAA8B,CAAC,EAAE;4BAC3E,QAAQ;4BACR,SAAS;gCACP,gBAAgB;gCAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO;gCAClC,8BAA8B;4BAChC;wBACF;wBAEA,QAAQ,GAAG,CAAC,gCAAgC,SAAS,MAAM;wBAE3D,IAAI,SAAS,EAAE,EAAE;4BACf,uDAAuD;4BACvD,aAAa,OAAO,CAAC,iBAAiB;4BAEtC,0CAA0C;4BAC1C,QAAQ,GAAG,CAAC;4BACZ,OAAO,IAAI,CAAC;wBACd,OAAO;4BACL,+CAA+C;4BAC/C,MAAM,YAAY,MAAM,SAAS,IAAI;4BACrC,QAAQ,KAAK,CAAC,8BAA8B,SAAS,MAAM,EAAE;4BAC7D,OAAO,IAAI,CAAC;wBACd;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,oCAAoC;wBAClD,OAAO,IAAI,CAAC;oBACd;gBACF;;YAEA;QACF;wCAAG;QAAC;QAAQ;KAAa;IAEzB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;;;;;;AAIlD;GAlES;;QACQ,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;KAF7B;AAoET,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;;;;;;AAIlD;MATS;AAWM,SAAS;IACtB,qBACE,6LAAC,6JAAA,CAAA,WAAQ;QAAC,wBAAU,6LAAC;;;;;kBACnB,cAAA,6LAAC;;;;;;;;;;AAGP;MANwB", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/FalcodeSec/frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}