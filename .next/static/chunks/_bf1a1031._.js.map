{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/FalcodeSec/frontend/src/app/auth/callback/page.tsx"], "sourcesContent": ["'use client'\n\nimport { Suspense, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { signIn } from \"next-auth/react\";\n\nfunction AuthCallbackContent() {\n  const router = useRouter();\n\n  useEffect(() => {\n    // The session token is now stored in an HTTP-only cookie by the backend\n    // We just need to validate the session with NextAuth\n    const handleSignIn = async () => {\n      try {\n        // Call the credentials provider without explicit token\n        // The backend will validate the session_token cookie automatically\n        const result = await signIn(\"credentials\", {\n          redirect: false,\n        });\n\n        if (result?.ok) {\n          // Successfully authenticated, redirect to dashboard\n          console.log(result)\n          router.push(\"/dashboard\");\n        } else {\n          // Authentication failed, redirect to login\n          console.error(\"Sign in failed:\", result?.error);\n          router.push(\"/login\");\n        }\n      } catch (error) {\n        console.error(\"Error during sign in:\", error);\n        router.push(\"/login\");\n      }\n    };\n\n    handleSignIn();\n  }, [router]);\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-200 to-slate-100\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\n        <p className=\"text-slate-600 font-medium\">Completing authentication...</p>\n      </div>\n    </div>\n  );\n}\n\nfunction AuthCallbackFallback() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-200 to-slate-100\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\n        <p className=\"text-slate-600 font-medium\">Loading...</p>\n      </div>\n    </div>\n  );\n}\n\nexport default function AuthCallbackPage() {\n  return (\n    <Suspense fallback={<AuthCallbackFallback />}>\n      <AuthCallbackContent />\n    </Suspense>\n  );\n}\n\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,SAAS;;IACP,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,wEAAwE;YACxE,qDAAqD;YACrD,MAAM;8DAAe;oBACnB,IAAI;wBACF,uDAAuD;wBACvD,mEAAmE;wBACnE,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;4BACzC,UAAU;wBACZ;wBAEA,IAAI,QAAQ,IAAI;4BACd,oDAAoD;4BACpD,QAAQ,GAAG,CAAC;4BACZ,OAAO,IAAI,CAAC;wBACd,OAAO;4BACL,2CAA2C;4BAC3C,QAAQ,KAAK,CAAC,mBAAmB,QAAQ;4BACzC,OAAO,IAAI,CAAC;wBACd;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,yBAAyB;wBACvC,OAAO,IAAI,CAAC;oBACd;gBACF;;YAEA;QACF;wCAAG;QAAC;KAAO;IAEX,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;;;;;;AAIlD;GAxCS;;QACQ,qIAAA,CAAA,YAAS;;;KADjB;AA0CT,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAA6B;;;;;;;;;;;;;;;;;AAIlD;MATS;AAWM,SAAS;IACtB,qBACE,6LAAC,6JAAA,CAAA,WAAQ;QAAC,wBAAU,6LAAC;;;;;kBACnB,cAAA,6LAAC;;;;;;;;;;AAGP;MANwB", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/FalcodeSec/frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}