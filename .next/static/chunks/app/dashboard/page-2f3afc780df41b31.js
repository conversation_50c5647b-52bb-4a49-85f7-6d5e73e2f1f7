try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="f69f48c3-6e2d-4244-9ee0-44ee6dc3f972",e._sentryDebugIdIdentifier="sentry-dbid-f69f48c3-6e2d-4244-9ee0-44ee6dc3f972")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{101:(e,r,s)=>{Promise.resolve().then(s.bind(s,2584))},381:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1253:(e,r,s)=>{"use strict";s.d(r,{cn:()=>l});var t=s(2596),a=s(9688);function l(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,a.QP)((0,t.$)(r))}},2584:(e,r,s)=>{"use strict";s.d(r,{DashboardPage:()=>h});var t=s(5155),a=s(5695),l=s(6695),o=s(9946);let n=(0,o.A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),d=(0,o.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),i=(0,o.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);var c=s(381);function h(){let e=(0,a.useRouter)();return(0,t.jsxs)("div",{className:"space-y-6 sm:space-y-8",children:[(0,t.jsxs)("div",{className:"mb-6 sm:mb-8",children:[(0,t.jsx)("h1",{className:"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-800",children:"Dashboard"}),(0,t.jsx)("p",{className:"text-gray-500 mt-2 text-sm sm:text-base",children:"Welcome to Platyfend - your AI-powered code review platform."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8",children:[(0,t.jsxs)(l.Zp,{className:"cursor-pointer hover:shadow-lg transition-all duration-200 border-gray-200 bg-white hover:border-[#00617b]/30 hover:shadow-[#00617b]/10",onClick:()=>e.push("/dashboard/repositories"),children:[(0,t.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(l.ZB,{className:"text-sm font-medium text-gray-600",children:"Repositories"}),(0,t.jsx)(n,{className:"h-4 w-4 text-[#00617b]"})]}),(0,t.jsxs)(l.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-gray-800",children:0}),(0,t.jsx)("p",{className:"text-xs text-gray-400 cursor-pointer ",children:"Connected repositories"})]})]}),(0,t.jsxs)(l.Zp,{className:"cursor-pointer hover:shadow-lg transition-all duration-200 border-gray-200 bg-white hover:border-[#00617b]/30 hover:shadow-[#00617b]/10",onClick:()=>e.push("/dashboard/reports"),children:[(0,t.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(l.ZB,{className:"text-sm font-medium text-gray-600",children:"Reports"}),(0,t.jsx)(d,{className:"h-4 w-4 text-[#00617b]"})]}),(0,t.jsxs)(l.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-gray-800",children:0}),(0,t.jsx)("p",{className:"text-xs text-gray-400 cursor-pointer",children:"View reports"})]})]}),(0,t.jsxs)(l.Zp,{className:"cursor-pointer hover:shadow-lg transition-all duration-200 border-gray-200 bg-white hover:border-[#00617b]/30 hover:shadow-[#00617b]/10",onClick:()=>e.push("/dashboard/profile"),children:[(0,t.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(l.ZB,{className:"text-sm font-medium text-gray-600",children:"Profile"}),(0,t.jsx)(i,{className:"h-4 w-4 text-[#00617b]"})]}),(0,t.jsxs)(l.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-gray-800",children:"1"}),(0,t.jsx)("p",{className:"text-xs text-gray-400 cursor-pointer",children:"User profile"})]})]}),(0,t.jsxs)(l.Zp,{className:"cursor-pointer hover:shadow-lg transition-all duration-200 border-gray-200 bg-white hover:border-[#00617b]/30 hover:shadow-[#00617b]/10",onClick:()=>e.push("/dashboard/settings"),children:[(0,t.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(l.ZB,{className:"text-sm font-medium text-gray-600",children:"Settings"}),(0,t.jsx)(c.A,{className:"h-4 w-4 text-[#00617b]"})]}),(0,t.jsxs)(l.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-gray-800",children:"Config"}),(0,t.jsx)("p",{className:"text-xs text-gray-400 cursor-pointer",children:"Account settings"})]})]})]})]})}},5695:(e,r,s)=>{"use strict";var t=s(8999);s.o(t,"usePathname")&&s.d(r,{usePathname:function(){return t.usePathname}}),s.o(t,"useRouter")&&s.d(r,{useRouter:function(){return t.useRouter}}),s.o(t,"useSearchParams")&&s.d(r,{useSearchParams:function(){return t.useSearchParams}})},6695:(e,r,s)=>{"use strict";s.d(r,{Wu:()=>i,ZB:()=>d,Zp:()=>o,aR:()=>n});var t=s(5155),a=s(2115),l=s(1253);let o=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...a})});o.displayName="Card";let n=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...a})});n.displayName="CardHeader";let d=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});d.displayName="CardTitle",a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",s),...a})}).displayName="CardDescription";let i=a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",s),...a})});i.displayName="CardContent",a.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",s),...a})}).displayName="CardFooter"},9946:(e,r,s)=>{"use strict";s.d(r,{A:()=>h});var t=s(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,s)=>s?s.toUpperCase():r.toLowerCase()),o=e=>{let r=l(e);return r.charAt(0).toUpperCase()+r.slice(1)},n=function(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return r.filter((e,r,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===r).join(" ").trim()},d=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,t.forwardRef)((e,r)=>{let{color:s="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:c="",children:h,iconNode:x,...u}=e;return(0,t.createElement)("svg",{ref:r,...i,width:a,height:a,stroke:s,strokeWidth:o?24*Number(l)/Number(a):l,className:n("lucide",c),...!h&&!d(u)&&{"aria-hidden":"true"},...u},[...x.map(e=>{let[r,s]=e;return(0,t.createElement)(r,s)}),...Array.isArray(h)?h:[h]])}),h=(e,r)=>{let s=(0,t.forwardRef)((s,l)=>{let{className:d,...i}=s;return(0,t.createElement)(c,{ref:l,iconNode:r,className:n("lucide-".concat(a(o(e))),"lucide-".concat(e),d),...i})});return s.displayName=o(e),s}}},e=>{var r=r=>e(e.s=r);e.O(0,[277,441,684,358],()=>r(101)),_N_E=e.O()}]);