try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},a=(new e.Error).stack;a&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[a]="1c643235-b78a-4fcd-a737-712cf7fb31db",e._sentryDebugIdIdentifier="sentry-dbid-1c643235-b78a-4fcd-a737-712cf7fb31db")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[954],{285:(e,a,t)=>{"use strict";t.d(a,{$:()=>o});var r=t(5155),s=t(2115),i=t(9708),n=t(2085),d=t(1253);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",platyfend:"bg-platyfend-500 hover:bg-platyfend-600 shadow-sm"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=s.forwardRef((e,a)=>{let{className:t,variant:s,size:n,asChild:o=!1,...c}=e,u=o?i.DX:"button";return(0,r.jsx)(u,{className:(0,d.cn)(l({variant:s,size:n,className:t})),ref:a,...c})});o.displayName="Button"},1253:(e,a,t)=>{"use strict";t.d(a,{cn:()=>i});var r=t(2596),s=t(9688);function i(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,s.QP)((0,r.$)(a))}},5371:(e,a,t)=>{Promise.resolve().then(t.bind(t,9382))},8133:(e,a,t)=>{"use strict";t.d(a,{Pt:()=>s,S3:()=>n,do:()=>i,z5:()=>d});let r="platyfend_session_token";function s(){return localStorage.getItem(r)}function i(e){localStorage.setItem(r,e)}function n(){localStorage.removeItem(r)}function d(){let e=s();return e?{Authorization:"Bearer ".concat(e)}:{}}},8382:(e,a,t)=>{"use strict";t.d(a,{A:()=>l,AuthProvider:()=>d});var r=t(5155),s=t(2115),i=t(8133);let n=(0,s.createContext)(void 0);function d(e){let{children:a}=e,[t,d]=(0,s.useState)(null),[l,o]=(0,s.useState)(null),[c,u]=(0,s.useState)(!0);(0,s.useEffect)(()=>{let e=(0,i.Pt)();e?(o(e),f(e)):u(!1)},[]);let f=async e=>{try{let a=await fetch("".concat("https://harmless-fit-mink.ngrok-free.app","/api/v1/login/validate-session"),{headers:{Authorization:"Bearer ".concat(e)}});if(a.ok){let e=await a.json();d(e)}else(0,i.S3)(),o(null),d(null)}catch(e){(0,i.S3)(),o(null),d(null)}finally{u(!1)}};return(0,r.jsx)(n.Provider,{value:{user:t,token:l,isLoading:c,isAuthenticated:!!l&&!!t,logout:()=>{(0,i.S3)(),o(null),d(null)}},children:a})}function l(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},9382:(e,a,t)=>{"use strict";t.d(a,{DashboardLayout:()=>eN});var r=t(5155),s=t(2115),i=t(9708),n=t(2085),d=t(2432),l=t(1253),o=t(285);let c=s.forwardRef((e,a)=>{let{className:t,type:s,...i}=e;return(0,r.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:a,...i})});c.displayName="Input";var u=t(7489);let f=s.forwardRef((e,a)=>{let{className:t,orientation:s="horizontal",decorative:i=!0,...n}=e;return(0,r.jsx)(u.b,{ref:a,decorative:i,orientation:s,className:(0,l.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",t),...n})});f.displayName=u.b.displayName;var m=t(5452),p=t(4416);let b=m.bL;m.l9,m.bm;let g=m.ZL,x=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(m.hJ,{className:(0,l.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...s,ref:a})});x.displayName=m.hJ.displayName;let h=(0,n.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),v=s.forwardRef((e,a)=>{let{side:t="right",className:s,children:i,...n}=e;return(0,r.jsxs)(g,{children:[(0,r.jsx)(x,{}),(0,r.jsxs)(m.UC,{ref:a,className:(0,l.cn)(h({side:t}),s),...n,children:[i,(0,r.jsxs)(m.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});function w(e){let{className:a,...t}=e;return(0,r.jsx)("div",{className:(0,l.cn)("animate-pulse rounded-md bg-muted",a),...t})}v.displayName=m.UC.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(m.hE,{ref:a,className:(0,l.cn)("text-lg font-semibold text-foreground",t),...s})}).displayName=m.hE.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(m.VY,{ref:a,className:(0,l.cn)("text-sm text-muted-foreground",t),...s})}).displayName=m.VY.displayName;var y=t(9613);let N=y.Provider,j=y.Root,k=y.Trigger,z=s.forwardRef((e,a)=>{let{className:t,sideOffset:s=4,...i}=e;return(0,r.jsx)(y.Content,{ref:a,sideOffset:s,className:(0,l.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...i})});z.displayName=y.Content.displayName;let S="18rem",R=s.createContext(null);function _(){let e=s.useContext(R);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}let A=s.forwardRef((e,a)=>{let{defaultOpen:t=!0,open:i,onOpenChange:n,className:d,style:o,children:c,...u}=e,f=function(){let[e,a]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{let e=()=>{a(window.innerWidth<1024)};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),e}(),[m,p]=s.useState(!1),[b,g]=s.useState(t),x=null!=i?i:b,h=s.useCallback(e=>{let a="function"==typeof e?e(x):e;n?n(a):g(a),document.cookie="".concat("sidebar:state","=").concat(a,"; path=/; max-age=").concat(604800)},[n,x]);s.useEffect(()=>{let e=()=>{let e=window.innerWidth;e>=1024&&e<1280&&x?h(!1):!(e>=1280)||x||f||h(!0)};return window.addEventListener("resize",e),e(),()=>window.removeEventListener("resize",e)},[x,h,f]);let v=s.useCallback(()=>f?p(e=>!e):h(e=>!e),[f,h,p]);s.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),v())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[v]);let w=x?"expanded":"collapsed",y=s.useMemo(()=>({state:w,open:x,setOpen:h,isMobile:f,openMobile:m,setOpenMobile:p,toggleSidebar:v}),[w,x,h,f,m,p,v]);return(0,r.jsx)(R.Provider,{value:y,children:(0,r.jsx)(N,{delayDuration:0,children:(0,r.jsx)("div",{style:{"--sidebar-width":"16rem","--sidebar-width-tablet":"14rem","--sidebar-width-mobile":S,"--sidebar-width-icon":"3rem",...o},className:(0,l.cn)("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",d),ref:a,...u,children:c})})})});A.displayName="SidebarProvider";let C=s.forwardRef((e,a)=>{let{side:t="left",variant:s="sidebar",collapsible:i="offcanvas",className:n,children:d,...o}=e,{isMobile:c,state:u,openMobile:f,setOpenMobile:m}=_();return"none"===i?(0,r.jsx)("div",{className:(0,l.cn)("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",n),ref:a,...o,children:d}):c?(0,r.jsx)(b,{open:f,onOpenChange:m,...o,children:(0,r.jsx)(v,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":S},side:t,children:(0,r.jsx)("div",{className:"flex h-full w-full flex-col",children:d})})}):(0,r.jsxs)("div",{ref:a,className:"group peer hidden lg:block text-sidebar-foreground","data-state":u,"data-collapsible":"collapsed"===u?i:"","data-variant":s,"data-side":t,children:[(0,r.jsx)("div",{className:(0,l.cn)("duration-200 relative h-svh bg-transparent transition-[width] ease-linear","w-[--sidebar-width-tablet] xl:w-[--sidebar-width]","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===s||"inset"===s?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),(0,r.jsx)("div",{className:(0,l.cn)("duration-200 fixed inset-y-0 z-10 hidden h-svh transition-[left,right,width] ease-linear lg:flex","w-[--sidebar-width-tablet] xl:w-[--sidebar-width]","left"===t?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width-tablet)*-1)] xl:group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width-tablet)*-1)] xl:group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===s||"inset"===s?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",n),...o,children:(0,r.jsx)("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:d})})]})});C.displayName="Sidebar";let E=s.forwardRef((e,a)=>{let{className:t,onClick:s,...i}=e,{toggleSidebar:n}=_();return(0,r.jsxs)(o.$,{ref:a,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:(0,l.cn)("h-7 w-7",t),onClick:e=>{null==s||s(e),n()},...i,children:[(0,r.jsx)(d.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})});E.displayName="SidebarTrigger",s.forwardRef((e,a)=>{let{className:t,...s}=e,{toggleSidebar:i}=_();return(0,r.jsx)("button",{ref:a,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:i,title:"Toggle Sidebar",className:(0,l.cn)("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",t),...s})}).displayName="SidebarRail",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("main",{ref:a,className:(0,l.cn)("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",t),...s})}).displayName="SidebarInset",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(c,{ref:a,"data-sidebar":"input",className:(0,l.cn)("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",t),...s})}).displayName="SidebarInput";let P=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"header",className:(0,l.cn)("flex flex-col gap-2 p-2",t),...s})});P.displayName="SidebarHeader";let L=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"footer",className:(0,l.cn)("flex flex-col gap-2 p-2",t),...s})});L.displayName="SidebarFooter",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(f,{ref:a,"data-sidebar":"separator",className:(0,l.cn)("mx-2 w-auto bg-sidebar-border",t),...s})}).displayName="SidebarSeparator";let I=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"content",className:(0,l.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...s})});I.displayName="SidebarContent";let D=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"group",className:(0,l.cn)("relative flex w-full min-w-0 flex-col p-2",t),...s})});D.displayName="SidebarGroup";let M=s.forwardRef((e,a)=>{let{className:t,asChild:s=!1,...n}=e,d=s?i.DX:"div";return(0,r.jsx)(d,{ref:a,"data-sidebar":"group-label",className:(0,l.cn)("duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t),...n})});M.displayName="SidebarGroupLabel",s.forwardRef((e,a)=>{let{className:t,asChild:s=!1,...n}=e,d=s?i.DX:"button";return(0,r.jsx)(d,{ref:a,"data-sidebar":"group-action",className:(0,l.cn)("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",t),...n})}).displayName="SidebarGroupAction";let U=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"group-content",className:(0,l.cn)("w-full text-sm",t),...s})});U.displayName="SidebarGroupContent";let V=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("ul",{ref:a,"data-sidebar":"menu",className:(0,l.cn)("flex w-full min-w-0 flex-col gap-1",t),...s})});V.displayName="SidebarMenu";let O=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("li",{ref:a,"data-sidebar":"menu-item",className:(0,l.cn)("group/menu-item relative",t),...s})});O.displayName="SidebarMenuItem";let T=(0,n.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!p-0"}},defaultVariants:{variant:"default",size:"default"}}),B=s.forwardRef((e,a)=>{let{asChild:t=!1,isActive:s=!1,variant:n="default",size:d="default",tooltip:o,className:c,...u}=e,f=t?i.DX:"button",{isMobile:m,state:p}=_(),b=(0,r.jsx)(f,{ref:a,"data-sidebar":"menu-button","data-size":d,"data-active":s,className:(0,l.cn)(T({variant:n,size:d}),c),...u});return o?("string"==typeof o&&(o={children:o}),(0,r.jsxs)(j,{children:[(0,r.jsx)(k,{asChild:!0,children:b}),(0,r.jsx)(z,{side:"right",align:"center",hidden:"collapsed"!==p||m,...o})]})):b});B.displayName="SidebarMenuButton",s.forwardRef((e,a)=>{let{className:t,asChild:s=!1,showOnHover:n=!1,...d}=e,o=s?i.DX:"button";return(0,r.jsx)(o,{ref:a,"data-sidebar":"menu-action",className:(0,l.cn)("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",n&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",t),...d})}).displayName="SidebarMenuAction",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"menu-badge",className:(0,l.cn)("absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",t),...s})}).displayName="SidebarMenuBadge",s.forwardRef((e,a)=>{let{className:t,showIcon:i=!1,...n}=e,d=s.useMemo(()=>"".concat(Math.floor(40*Math.random())+50,"%"),[]);return(0,r.jsxs)("div",{ref:a,"data-sidebar":"menu-skeleton",className:(0,l.cn)("rounded-md h-8 flex gap-2 px-2 items-center",t),...n,children:[i&&(0,r.jsx)(w,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,r.jsx)(w,{className:"h-4 flex-1 max-w-[--skeleton-width]","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":d}})]})}).displayName="SidebarMenuSkeleton",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("ul",{ref:a,"data-sidebar":"menu-sub",className:(0,l.cn)("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",t),...s})}).displayName="SidebarMenuSub",s.forwardRef((e,a)=>{let{...t}=e;return(0,r.jsx)("li",{ref:a,...t})}).displayName="SidebarMenuSubItem",s.forwardRef((e,a)=>{let{asChild:t=!1,size:s="md",isActive:n,className:d,...o}=e,c=t?i.DX:"a";return(0,r.jsx)(c,{ref:a,"data-sidebar":"menu-sub-button","data-size":s,"data-active":n,className:(0,l.cn)("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===s&&"text-xs","md"===s&&"text-sm","group-data-[collapsible=icon]:hidden",d),...o})}).displayName="SidebarMenuSubButton";var F=t(6874),G=t.n(F),H=t(5695),X=t(7340),q=t(2775),J=t(2892),Z=t(235),$=t(5040),W=t(381),Y=t(5868),K=t(4788),Q=t(2919),ee=t(1007),ea=t(4835),et=t(8382),er=t(4011);let es=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(er.bL,{ref:a,className:(0,l.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",t),...s})});es.displayName=er.bL.displayName;let ei=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(er._V,{ref:a,className:(0,l.cn)("aspect-square h-full w-full",t),...s})});ei.displayName=er._V.displayName;let en=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(er.H4,{ref:a,className:(0,l.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",t),...s})});en.displayName=er.H4.displayName;var ed=t(8698),el=t(3052),eo=t(5196),ec=t(9428);let eu=ed.bL,ef=ed.l9;ed.YJ,ed.ZL,ed.Pb,ed.z6,s.forwardRef((e,a)=>{let{className:t,inset:s,children:i,...n}=e;return(0,r.jsxs)(ed.ZP,{ref:a,className:(0,l.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",s&&"pl-8",t),...n,children:[i,(0,r.jsx)(el.A,{className:"ml-auto h-4 w-4"})]})}).displayName=ed.ZP.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(ed.G5,{ref:a,className:(0,l.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...s})}).displayName=ed.G5.displayName;let em=s.forwardRef((e,a)=>{let{className:t,sideOffset:s=4,...i}=e;return(0,r.jsx)(ed.ZL,{children:(0,r.jsx)(ed.UC,{ref:a,sideOffset:s,className:(0,l.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...i})})});em.displayName=ed.UC.displayName;let ep=s.forwardRef((e,a)=>{let{className:t,inset:s,...i}=e;return(0,r.jsx)(ed.q7,{ref:a,className:(0,l.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",t),...i})});ep.displayName=ed.q7.displayName,s.forwardRef((e,a)=>{let{className:t,children:s,checked:i,...n}=e;return(0,r.jsxs)(ed.H_,{ref:a,className:(0,l.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:i,...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(ed.VF,{children:(0,r.jsx)(eo.A,{className:"h-4 w-4"})})}),s]})}).displayName=ed.H_.displayName,s.forwardRef((e,a)=>{let{className:t,children:s,...i}=e;return(0,r.jsxs)(ed.hN,{ref:a,className:(0,l.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...i,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(ed.VF,{children:(0,r.jsx)(ec.A,{className:"h-2 w-2 fill-current"})})}),s]})}).displayName=ed.hN.displayName;let eb=s.forwardRef((e,a)=>{let{className:t,inset:s,...i}=e;return(0,r.jsx)(ed.JU,{ref:a,className:(0,l.cn)("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",t),...i})});eb.displayName=ed.JU.displayName;let eg=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(ed.wv,{ref:a,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",t),...s})});function ex(){var e;let{user:a,logout:t}=(0,et.A)(),s=(0,H.useRouter)();if(!a)return null;let i=a.login||(null==(e=a.email)?void 0:e.split("@")[0])||"User",n=a.name?a.name.split(" ").filter(e=>e.length>0).map(e=>e[0]).join("").toUpperCase():i.substring(0,2).toUpperCase();return(0,r.jsx)(V,{children:(0,r.jsx)(O,{children:(0,r.jsxs)(eu,{children:[(0,r.jsx)(ef,{asChild:!0,children:(0,r.jsxs)(B,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[(0,r.jsxs)(es,{className:"h-8 w-8 rounded-lg",children:[(0,r.jsx)(ei,{src:a.avatar_url||void 0,alt:a.name||i}),(0,r.jsx)(en,{className:"rounded-lg",children:n})]}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-semibold text-gray-900",children:a.name||i}),(0,r.jsx)("span",{className:"truncate text-xs text-gray-600",children:a.email||"@".concat(i)})]})]})}),(0,r.jsxs)(em,{className:"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg",side:"bottom",align:"end",sideOffset:4,children:[(0,r.jsx)(eb,{className:"p-0 font-normal",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[(0,r.jsxs)(es,{className:"h-8 w-8 rounded-lg",children:[(0,r.jsx)(ei,{src:a.avatar_url||void 0,alt:a.name||i}),(0,r.jsx)(en,{className:"rounded-lg",children:n})]}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-semibold text-gray-900",children:a.name||i}),(0,r.jsx)("span",{className:"truncate text-xs text-gray-600",children:a.email||"@".concat(i)})]})]})}),(0,r.jsx)(eg,{}),(0,r.jsx)(ep,{asChild:!0,children:(0,r.jsxs)(G(),{href:"/dashboard/profile",className:"flex items-center text-gray-700 hover:!text-gray-900 hover:!bg-gray-50 cursor-pointer",children:[(0,r.jsx)(ee.A,{className:"mr-2 h-4 w-4 text-gray-500 hover:!text-gray-700"}),"Profile"]})}),(0,r.jsx)(eg,{}),(0,r.jsxs)(ep,{onClick:()=>{t(),s.push("/login")},className:"text-gray-700 hover:!text-gray-900 hover:!bg-gray-50 cursor-pointer",children:[(0,r.jsx)(ea.A,{className:"mr-2 h-4 w-4 text-gray-500 hover:!text-gray-700"}),"Sign out"]})]})]})})})}eg.displayName=ed.wv.displayName;let eh=()=>[{title:"Home",url:"/dashboard",icon:X.A},{title:"Repositories",url:"/dashboard/repositories",icon:q.A},{title:"Integrations",url:"/dashboard/integrations",icon:J.A,locked:!0},{title:"Reports",url:"/dashboard/reports",icon:Z.A,locked:!0},{title:"Learnings",url:"/dashboard/learnings",icon:$.A},{title:"Organization Settings",url:"/dashboard/settings",icon:W.A},{title:"Subscription",url:"/dashboard/subscription",icon:Y.A}],ev=[{title:"Docs",url:"/docs",icon:$.A},{title:"Support",url:"/support",icon:K.A}];function ew(){let e=(0,H.usePathname)(),a=eh();return(0,r.jsxs)(C,{className:"shadow-sm border-r border-gray-200 flex-shrink-0 bg-white",children:[(0,r.jsx)(P,{className:"border-b border-gray-200 pb-4 space-y-4",children:(0,r.jsx)("div",{className:"px-2 py-2",children:(0,r.jsx)("h2",{className:"text-sm font-semibold",children:"Platyfend"})})}),(0,r.jsxs)(I,{className:"px-2 overflow-y-auto",children:[(0,r.jsxs)(D,{children:[(0,r.jsx)(M,{className:"text-xs font-semibold text-gray-400 uppercase tracking-wider px-2 mb-2",children:"Navigation"}),(0,r.jsx)(U,{children:(0,r.jsx)(V,{className:"space-y-1",children:a.map(a=>(0,r.jsx)(O,{children:(0,r.jsx)(B,{asChild:!0,isActive:e===a.url||"/dashboard"!==a.url&&e.startsWith(a.url),className:"w-full justify-start rounded-lg px-3 py-2 text-sm font-medium transition-all text-gray-600 hover:text-gray-800 hover:bg-gray-50 data-[active=true]:bg-[#00617b]/10 data-[active=true]:text-[#00617b] data-[active=true]:font-semibold",children:(0,r.jsxs)(G(),{href:a.url,className:"flex items-center w-full",children:[(0,r.jsx)(a.icon,{className:"h-4 w-4 mr-3"}),(0,r.jsx)("span",{className:"flex-1",children:a.title}),a.locked&&(0,r.jsx)(Q.A,{className:"h-4 w-4 text-gray-400"})]})})},a.title))})})]}),(0,r.jsxs)(D,{className:"mt-8",children:[(0,r.jsx)(M,{className:"text-xs font-semibold text-gray-400 uppercase tracking-wider px-2 mb-2",children:"Resources"}),(0,r.jsx)(U,{children:(0,r.jsx)(V,{className:"space-y-1",children:ev.map(e=>(0,r.jsx)(O,{children:(0,r.jsx)(B,{asChild:!0,className:"w-full justify-start rounded-lg px-3 py-2 text-sm font-medium transition-all text-gray-600 hover:text-gray-800 hover:bg-gray-50",children:(0,r.jsxs)(G(),{href:e.url,children:[(0,r.jsx)(e.icon,{className:"h-4 w-4 mr-3"}),(0,r.jsx)("span",{children:e.title})]})})},e.title))})})]})]}),(0,r.jsx)(L,{children:(0,r.jsx)(ex,{})})]})}var ey=t(4783);function eN(e){let{children:a}=e;return(0,r.jsx)(A,{children:(0,r.jsxs)("div",{className:"min-h-screen flex w-full bg-gray-50 overflow-hidden",children:[(0,r.jsx)(ew,{}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col bg-gray-50 min-w-0 w-full",children:[(0,r.jsx)("div",{className:"lg:hidden flex items-center p-4 border-b",children:(0,r.jsx)(E,{children:(0,r.jsxs)(o.$,{variant:"ghost",size:"icon",className:"mr-2",children:[(0,r.jsx)(ey.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle menu"})]})})}),(0,r.jsx)("main",{className:"flex-1 p-4 sm:p-6 lg:p-8 min-w-0 overflow-auto bg-gray-50 w-full",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto w-full",children:a})})]})]})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[277,695,512,729,441,684,358],()=>a(5371)),_N_E=e.O()}]);