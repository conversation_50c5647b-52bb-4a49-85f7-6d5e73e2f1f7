try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="ca5ccfb2-7317-4f2b-bec9-23dc6b396570",e._sentryDebugIdIdentifier="sentry-dbid-ca5ccfb2-7317-4f2b-bec9-23dc6b396570")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[466],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>c});var s=r(5155),n=r(2115),l=r(9708),a=r(2085),i=r(1253);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",platyfend:"bg-platyfend-500 hover:bg-platyfend-600 shadow-sm"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=n.forwardRef((e,t)=>{let{className:r,variant:n,size:a,asChild:c=!1,...d}=e,u=c?l.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(o({variant:n,size:a,className:r})),ref:t,...d})});c.displayName="Button"},859:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var s=r(5155),n=r(2115),l=r(285),a=r(9946);let i=(0,a.A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),o=(0,a.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),c=(0,a.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),d=(0,a.A)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);var u=r(8133);let m=r(9509).env.NEXT_PUBLIC_API_URL||"http://localhost:8000",h=r(9509).env.NEXT_PUBLIC_API_URL||"http://localhost:8000",f=function(){let{installations:e,loading:t,error:r}=function(){let[e,t]=(0,n.useState)([]),[r,s]=(0,n.useState)(!0),[l,a]=(0,n.useState)(null),i=async()=>{if(!(0,u.Pt)()){s(!1),a("Not authenticated");return}try{s(!0),a(null);let e=(0,u.z5)(),r={"Content-Type":"application/json",...e},n=await fetch("".concat(m,"/api/v1/github-app/installations"),{method:"GET",headers:r,credentials:"include"});if(!n.ok){let e=await n.json().catch(()=>({}));throw Error("Failed to fetch installations: ".concat(n.statusText," - ").concat(e.detail||""))}let l=await n.json();t(l.installations||[])}catch(e){a(e instanceof Error?e.message:"Unknown error")}finally{s(!1)}};return(0,n.useEffect)(()=>{i()},[]),{installations:e,loading:r,error:l,refetch:i}}(),{initiateInstall:a,loading:f,error:p}=function(){let[e,t]=(0,n.useState)(!1),[r,s]=(0,n.useState)(null);return{initiateInstall:(0,n.useCallback)(async()=>{if(!(0,u.Pt)())return void s("User not authenticated");try{t(!0),s(null);let e=await fetch("".concat(h,"/api/v1/github-app/install/authorize"),{method:"GET",headers:{"Content-Type":"application/json",...(0,u.z5)()},credentials:"include",redirect:"follow"});if(!e.ok)throw Error("Failed to initiate installation: ".concat(e.statusText));let r=e.url||e.headers.get("location");r&&(window.location.href=r)}catch(e){s(e instanceof Error?e.message:"Unknown error")}finally{t(!1)}},[]),loading:e,error:r}}(),x=async()=>{await a()},g=e.length>0;return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold text-slate-900",children:"Repositories"}),(0,s.jsx)("p",{className:"text-slate-600 mt-1",children:"Manage your connected repositories"})]}),(0,s.jsx)(l.$,{onClick:x,disabled:f,children:f?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i,{className:"h-4 w-4 mr-2 animate-spin"}),"Connecting..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o,{className:"h-4 w-4 mr-2"}),"Add Repository"]})})]}),p&&(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 flex items-start gap-3",children:[(0,s.jsx)(c,{className:"h-5 w-5 text-red-600 flex-shrink-0 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-red-900",children:"Error"}),(0,s.jsx)("p",{className:"text-red-700 text-sm",children:p})]})]}),r&&(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 flex items-start gap-3",children:[(0,s.jsx)(c,{className:"h-5 w-5 text-red-600 flex-shrink-0 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-red-900",children:"Error Loading Installations"}),(0,s.jsx)("p",{className:"text-red-700 text-sm",children:r})]})]}),t?(0,s.jsx)("div",{className:"bg-white rounded-lg border border-slate-200 shadow-sm p-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(i,{className:"h-5 w-5 animate-spin text-slate-600"}),(0,s.jsx)("p",{className:"text-slate-600",children:"Loading repositories..."})]})}):g?(0,s.jsx)("div",{className:"space-y-4",children:e.map(e=>(0,s.jsxs)("div",{className:"bg-white rounded-lg border border-slate-200 shadow-sm p-6",children:[(0,s.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("h3",{className:"text-lg font-medium text-slate-900",children:["Installation #",e.installation_id]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(d,{className:"h-4 w-4 text-green-600"}),(0,s.jsx)("span",{className:"text-sm text-green-700 font-medium",children:e.status})]})]}),(0,s.jsxs)("p",{className:"text-sm text-slate-600 mt-1",children:["Installed on ",new Date(e.installed_at).toLocaleDateString()]})]})}),e.repositories.length>0?(0,s.jsxs)("div",{children:[(0,s.jsxs)("h4",{className:"text-sm font-medium text-slate-900 mb-3",children:["Connected Repositories (",e.repositories.length,")"]}),(0,s.jsx)("div",{className:"space-y-2",children:e.repositories.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 bg-slate-50 rounded border border-slate-100",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-slate-900",children:e.full_name}),(0,s.jsx)("p",{className:"text-xs text-slate-600",children:e.private?"Private":"Public"})]}),(0,s.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-600 hover:text-blue-700",children:"View on GitHub →"})]},e.id))})]}):(0,s.jsx)("p",{className:"text-sm text-slate-600",children:"No repositories selected for this installation. Visit GitHub to add repositories."}),e.last_webhook_received&&(0,s.jsxs)("p",{className:"text-xs text-slate-500 mt-4",children:["Last webhook received:"," ",new Date(e.last_webhook_received).toLocaleString()]})]},e.installation_id))}):(0,s.jsx)("div",{className:"bg-white rounded-lg border border-slate-200 shadow-sm p-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-lg font-medium text-slate-900 mb-2",children:"No Repositories Connected"}),(0,s.jsx)("p",{className:"text-slate-600 mb-6",children:"Connect your first repository to get started with code reviews."}),(0,s.jsx)(l.$,{onClick:x,disabled:f,children:f?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i,{className:"h-4 w-4 mr-2 animate-spin"}),"Connecting..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o,{className:"h-4 w-4 mr-2"}),"Connect Repository"]})})]})})]})}},1253:(e,t,r)=>{"use strict";r.d(t,{cn:()=>l});var s=r(2596),n=r(9688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,s.$)(t))}},2085:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var s=r(2596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=s.$,a=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:i}=t,o=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],s=null==i?void 0:i[e];if(null===t)return null;let l=n(t)||n(s);return a[e][l]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return l(e,o,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...c}[t]):({...i,...c})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},3515:(e,t,r)=>{Promise.resolve().then(r.bind(r,859))},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>a,t:()=>l});var s=r(2115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let r=!1,s=e.map(e=>{let s=n(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():n(e[t],null)}}}}function a(...e){return s.useCallback(l(...e),e)}},8133:(e,t,r)=>{"use strict";r.d(t,{Pt:()=>n,S3:()=>a,do:()=>l,z5:()=>i});let s="platyfend_session_token";function n(){return localStorage.getItem(s)}function l(e){localStorage.setItem(s,e)}function a(){localStorage.removeItem(s)}function i(){let e=n();return e?{Authorization:"Bearer ".concat(e)}:{}}},9708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>i,Dc:()=>c,TL:()=>a});var s=r(2115),n=r(6101),l=r(5155);function a(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...l}=e;if(s.isValidElement(r)){var a;let e,i,o=(a=r,(i=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(i=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),c=function(e,t){let r={...t};for(let s in t){let n=e[s],l=t[s];/^on[A-Z]/.test(s)?n&&l?r[s]=(...e)=>{let t=l(...e);return n(...e),t}:n&&(r[s]=n):"style"===s?r[s]={...n,...l}:"className"===s&&(r[s]=[n,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==s.Fragment&&(c.ref=t?(0,n.t)(t,o):o),s.cloneElement(r,c)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:n,...a}=e,i=s.Children.toArray(n),o=i.find(d);if(o){let e=o.props.children,n=i.map(t=>t!==o?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...a,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,n):null})}return(0,l.jsx)(t,{...a,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var i=a("Slot"),o=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function d(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(2115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),a=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:l=2,absoluteStrokeWidth:a,className:d="",children:u,iconNode:m,...h}=e;return(0,s.createElement)("svg",{ref:t,...c,width:n,height:n,stroke:r,strokeWidth:a?24*Number(l)/Number(n):l,className:i("lucide",d),...!u&&!o(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,s.forwardRef)((r,l)=>{let{className:o,...c}=r;return(0,s.createElement)(d,{ref:l,iconNode:t,className:i("lucide-".concat(n(a(e))),"lucide-".concat(e),o),...c})});return r.displayName=a(e),r}}},e=>{var t=t=>e(e.s=t);e.O(0,[277,441,684,358],()=>t(3515)),_N_E=e.O()}]);