try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="87aeef0b-2791-498a-97ac-82a58401bac5",e._sentryDebugIdIdentifier="sentry-dbid-87aeef0b-2791-498a-97ac-82a58401bac5")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>c});var n=r(5155),o=r(2115),s=r(9708),a=r(2085),i=r(1253);let l=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",platyfend:"bg-platyfend-500 hover:bg-platyfend-600 shadow-sm"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=o.forwardRef((e,t)=>{let{className:r,variant:o,size:a,asChild:c=!1,...d}=e,u=c?s.DX:"button";return(0,n.jsx)(u,{className:(0,i.cn)(l({variant:o,size:a,className:r})),ref:t,...d})});c.displayName="Button"},1253:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(2596),o=r(9688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,n.$)(t))}},1985:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>T});var n=r(5155),o=r(2115),s=r(5695),a=r(8382),i=r(285),l=r(6695),c=r(5185),d=r(6081),u=r(9196),f=r(8905),m=r(3655),h=r(4315),x=r(5845),v=r(1285),p="Tabs",[g,b]=(0,d.A)(p,[u.RG]),w=(0,u.RG)(),[y,j]=g(p),k=o.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,onValueChange:s,defaultValue:a,orientation:i="horizontal",dir:l,activationMode:c="automatic",...d}=e,u=(0,h.jH)(l),[f,g]=(0,x.i)({prop:o,onChange:s,defaultProp:null!=a?a:"",caller:p});return(0,n.jsx)(y,{scope:r,baseId:(0,v.B)(),value:f,onValueChange:g,orientation:i,dir:u,activationMode:c,children:(0,n.jsx)(m.sG.div,{dir:u,"data-orientation":i,...d,ref:t})})});k.displayName=p;var N="TabsList",C=o.forwardRef((e,t)=>{let{__scopeTabs:r,loop:o=!0,...s}=e,a=j(N,r),i=w(r);return(0,n.jsx)(u.bL,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:o,children:(0,n.jsx)(m.sG.div,{role:"tablist","aria-orientation":a.orientation,...s,ref:t})})});C.displayName=N;var S="TabsTrigger",R=o.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,disabled:s=!1,...a}=e,i=j(S,r),l=w(r),d=A(i.baseId,o),f=I(i.baseId,o),h=o===i.value;return(0,n.jsx)(u.q7,{asChild:!0,...l,focusable:!s,active:h,children:(0,n.jsx)(m.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":f,"data-state":h?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:d,...a,ref:t,onMouseDown:(0,c.m)(e.onMouseDown,e=>{s||0!==e.button||!1!==e.ctrlKey?e.preventDefault():i.onValueChange(o)}),onKeyDown:(0,c.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&i.onValueChange(o)}),onFocus:(0,c.m)(e.onFocus,()=>{let e="manual"!==i.activationMode;h||s||!e||i.onValueChange(o)})})})});R.displayName=S;var L="TabsContent",D=o.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,forceMount:a,children:i,...l}=e,c=j(L,r),d=A(c.baseId,s),u=I(c.baseId,s),h=s===c.value,x=o.useRef(h);return o.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,n.jsx)(f.C,{present:a||h,children:r=>{let{present:o}=r;return(0,n.jsx)(m.sG.div,{"data-state":h?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!o,id:u,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:x.current?"0s":void 0},children:o&&i})}})});function A(e,t){return"".concat(e,"-trigger-").concat(t)}function I(e,t){return"".concat(e,"-content-").concat(t)}D.displayName=L;var P=r(1253);let F=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(C,{ref:t,className:(0,P.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",r),...o})});F.displayName=C.displayName;let E=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(R,{ref:t,className:(0,P.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",r),...o})});E.displayName=R.displayName;let G=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(D,{ref:t,className:(0,P.cn)("mt-2 ring-offset-background focus-visible:ring-ring focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-offset-2",r),...o})});function T(){let[e,t]=(0,o.useState)("saas"),r=(0,s.useRouter)(),{isAuthenticated:c,isLoading:d}=(0,a.A)();(0,o.useEffect)(()=>{!d&&c&&r.push("/dashboard")},[d,c,r]);let u=()=>{window.location.href="".concat("https://harmless-fit-mink.ngrok-free.app","/api/v1/login/github/authorize")},f=e=>"saas"===e?[{name:"Login with GitHub",icon:()=>(0,n.jsx)("svg",{stroke:"currentColor",fill:"currentColor",strokeWidth:"0",role:"img",viewBox:"0 0 24 24",className:"mr-2 shrink-0 transition-transform duration-300",height:"20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 **********.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"})}),onClick:u,color:"hover:text-[#24292e] hover:bg-[#f6f8fa] hover:border-[#24292e]/20"},{name:"Login with GitLab",icon:()=>(0,n.jsx)("svg",{stroke:"currentColor",fill:"currentColor",strokeWidth:"0",role:"img",viewBox:"0 0 24 24",className:"mr-2 shrink-0 transition-transform duration-300",height:"20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"m23.6004 9.5927-.0337-.0862L20.3.9814a.851.851 0 0 0-.3362-.405.8748.8748 0 0 0-.9997.0539.8748.8748 0 0 0-.29.4399l-2.2055 6.748H7.5375l-2.2057-6.748a.8573.8573 0 0 0-.29-.4412.8748.8748 0 0 0-.9997-.0537.8585.8585 0 0 0-.3362.4049L.4332 9.5015l-.0325.0862a6.0657 6.0657 0 0 0 2.0119 7.0105l.0113.0087.03.0213 4.976 3.7264 2.462 1.8633 1.4995 1.1321a1.0085 1.0085 0 0 0 1.2197 0l1.4995-1.1321 2.4619-1.8633 5.006-3.7489.0125-.01a6.0682 6.0682 0 0 0 2.0094-7.003z"})}),onClick:()=>console.log("GitLab login - Coming soon"),color:"hover:text-[#fc6d26] hover:bg-[#fef8f6] hover:border-[#fc6d26]/20"},{name:"Login with Azure DevOps",icon:()=>(0,n.jsx)("svg",{stroke:"currentColor",fill:"currentColor",strokeWidth:"0",role:"img",viewBox:"0 0 16 16",className:"mr-2 shrink-0 transition-transform duration-300",height:"20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"M15 3.62172V12.1336L11.5 15L6.075 13.025V14.9825L3.00375 10.9713L11.955 11.6704V4.00624L15 3.62172ZM12.0163 4.04994L6.99375 1V3.00125L2.3825 4.35581L1 6.12984V10.1586L2.9775 11.0325V5.86767L12.0163 4.04994Z"})}),onClick:()=>console.log("Azure DevOps login - Coming soon"),color:"hover:text-[#0078d7] hover:bg-[#f0f6fc] hover:border-[#0078d7]/20"},{name:"Login with Bitbucket Cloud",icon:()=>(0,n.jsx)("svg",{stroke:"currentColor",fill:"currentColor",strokeWidth:"0",role:"img",viewBox:"0 0 24 24",className:"mr-2 shrink-0 transition-transform duration-300",height:"20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"M.778 1.213a.768.768 0 00-.768.892l3.263 19.81c.084.5.515.868 1.022.873H19.95a.772.772 0 00.77-.646l3.27-20.03a.768.768 0 00-.768-.891zM14.52 15.53H9.522L8.17 8.466h7.561z"})}),onClick:()=>console.log("Bitbucket login - Coming soon"),color:"hover:text-[#0052cc] hover:bg-[#f4f8ff] hover:border-[#0052cc]/20"}]:[{name:"GitHub Enterprise Server",icon:()=>(0,n.jsx)("svg",{stroke:"currentColor",fill:"currentColor",strokeWidth:"0",role:"img",viewBox:"0 0 24 24",className:"mr-2 shrink-0 transition-transform duration-300",height:"20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 **********.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"})}),onClick:()=>console.log("GitHub Enterprise Server - Coming soon"),color:"hover:text-[#24292e] hover:bg-[#f6f8fa] hover:border-[#24292e]/20"},{name:"Self-Hosted GitLab",icon:()=>(0,n.jsx)("svg",{stroke:"currentColor",fill:"currentColor",strokeWidth:"0",role:"img",viewBox:"0 0 24 24",className:"mr-2 shrink-0 transition-transform duration-300",height:"20",width:"20",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"m23.6004 9.5927-.0337-.0862L20.3.9814a.851.851 0 0 0-.3362-.405.8748.8748 0 0 0-.9997.0539.8748.8748 0 0 0-.29.4399l-2.2055 6.748H7.5375l-2.2057-6.748a.8573.8573 0 0 0-.29-.4412.8748.8748 0 0 0-.9997-.0537.8585.8585 0 0 0-.3362.4049L.4332 9.5015l-.0325.0862a6.0657 6.0657 0 0 0 2.0119 7.0105l.0113.0087.03.0213 4.976 3.7264 2.462 1.8633 1.4995 1.1321a1.0085 1.0085 0 0 0 1.2197 0l1.4995-1.1321 2.4619-1.8633 5.006-3.7489.0125-.01a6.0682 6.0682 0 0 0 2.0094-7.003z"})}),onClick:()=>console.log("Self-Hosted GitLab - Coming soon"),color:"hover:text-[#fc6d26] hover:bg-[#fef8f6] hover:border-[#fc6d26]/20"}];return(0,n.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-200 to-slate-100 relative overflow-hidden",children:[(0,n.jsxs)("div",{className:"absolute inset-0",children:[(0,n.jsx)("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-primary/5 rounded-full blur-3xl animate-pulse"}),(0,n.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-96 h-96 bg-secondary/5 rounded-full blur-3xl animate-pulse delay-1000"}),(0,n.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-conic from-primary/5 via-transparent to-secondary/5 rounded-full blur-3xl"})]}),(0,n.jsx)("div",{className:"relative flex items-center justify-center min-h-screen p-4 sm:p-6 lg:p-8",children:(0,n.jsxs)("div",{className:"w-full max-w-md mx-auto",children:[(0,n.jsxs)("div",{className:"text-center mb-8 sm:mb-12",children:[(0,n.jsx)("div",{className:"flex items-center justify-center mb-6 sm:mb-8",children:(0,n.jsx)("svg",{className:"w-48 sm:w-60 h-auto",viewBox:"0 0 140.75185 26.435898",version:"1.1",id:"svg5",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsxs)("g",{id:"layer1",transform:"translate(-41.083833,-23.282913)",children:[(0,n.jsx)("g",{id:"g4138",transform:"matrix(0.13650447,0,0,0.13650447,41.083832,23.282913)",children:(0,n.jsxs)("g",{id:"layer1-5",style:{stroke:"none"},transform:"translate(-16.822974,-12.529328)",children:[(0,n.jsx)("path",{style:{display:"inline",fill:"#00617b",fillOpacity:1,stroke:"none",strokeWidth:1.858,strokeDasharray:"none",strokeOpacity:1},id:"path603",d:"M 194.6193,102.74453 A 88.898163,90.215202 0 0 1 105.92731,192.95949 88.898163,90.215202 0 0 1 16.823931,103.16297 88.898163,90.215202 0 0 1 105.10264,12.531511 88.898163,90.215202 0 0 1 194.61548,101.90765"}),(0,n.jsx)("path",{style:{fill:"#ffffff",fillOpacity:1,stroke:"none",strokeWidth:.458,strokeDasharray:"none",strokeOpacity:1},d:"m 54.897488,118.62163 c 0,0 -3.659831,-43.056851 53.175212,-46.286114 0,0 5.5974,-17.222742 25.61883,-16.576889 20.02144,0.645853 21.959,16.361604 21.959,18.945016 2.40418,4.998788 17.87455,3.696021 22.60485,2.798695 0.66722,-0.135221 2.95415,-0.11132 2.75039,2.586597 -4.87479,15.816936 -23.35383,17.257675 -32.67491,14.205577 0,0 -8.39608,10.548928 -17.65331,18.083878 0,0 -3.22926,10.11836 11.84064,16.57689 0,0 3.21189,1.37329 -0.21529,2.36813 -6.09191,-0.21671 -5.39948,-0.29908 -5.39948,-0.29908 l 3.6992,2.57623 c 0,0 1.156,0.82571 1.45325,1.28811 0.29726,0.4624 0.42937,1.25509 -0.75965,1.35418 -1.18903,0.0991 -2.64229,0.4624 -6.67177,-0.82572 -3.26983,-1.55234 -3.10469,-1.48628 -3.30286,-1.42023 -0.19817,0.0661 -0.33029,0.6936 0.49543,1.3872 0.82571,0.6936 1.81657,1.51932 1.98171,1.8496 0.16514,0.33029 0.99086,0.85874 -0.26423,1.3872 -1.25508,0.52846 -5.58183,-1.32114 -5.58183,-1.32114 0,0 -15.61084,-5.52399 -14.75848,-21.88941 0,0 -0.51141,9.37602 0,10.56933 0.51142,1.19332 -9.88744,7.84177 -19.263465,8.01224 0,0 8.694135,-12.78549 -9.205552,-20.11583 0,0 14.831167,8.18271 4.261829,21.99104 0,0 8.710931,3.71701 9.204355,4.26526 0.493424,0.54825 2.576773,2.30264 -1.864046,2.41229 -4.440815,0.10965 -5.482488,-0.54825 -5.482488,-0.54825 0,0 6.524161,4.44082 5.482488,5.59214 -1.041673,1.15132 -5.592138,-0.0548 -11.513224,-2.68642 0,0 2.412294,2.57677 2.796068,2.8509 0.383775,0.27412 0.877198,2.96054 -5.592137,0.38377 -6.469336,-2.57677 -11.677699,-6.03074 -9.868478,-12.93867 0,0 -7.511009,-3.17984 -9.046105,-4.11187 0,0 -20.723804,12.93868 -26.86419,8.05926 -6.140387,-4.87941 1.535096,-10.52638 3.728091,-11.6777 2.192995,-1.15132 14.838287,-8.15289 14.930152,-8.84621 z",id:"path2914"}),(0,n.jsx)("path",{style:{fill:"#00617b",fillOpacity:1,stroke:"none",strokeWidth:.457999,strokeDasharray:"none",strokeOpacity:1},id:"path3022",d:"m 145.35648,73.471817 a 4.9267778,4.7614498 0 0 1 -4.91535,4.761437 4.9267778,4.7614498 0 0 1 -4.93815,-4.739352 4.9267778,4.7614498 0 0 1 4.89245,-4.78342 4.9267778,4.7614498 0 0 1 4.96084,4.717166"})]})}),(0,n.jsx)("text",{xmlSpace:"preserve",style:{fontStyle:"normal",fontVariant:"normal",fontWeight:"bold",fontStretch:"normal",fontSize:"24px",fontFamily:"Poppins",fill:"#00617b",fillOpacity:1,stroke:"none",strokeWidth:.246621},x:"69.007454",y:"44.729881",id:"text4142",children:(0,n.jsx)("tspan",{id:"tspan4140",style:{fontStyle:"normal",fontVariant:"normal",fontWeight:"bold",fontStretch:"normal",fontFamily:"Frutiger",fill:"#00617b",fillOpacity:1,stroke:"none",strokeWidth:.246621},x:"69.007454",y:"44.729881",children:"Platyfend"})})]})})}),(0,n.jsx)("div",{className:"space-y-2 mb-8 sm:mb-10",children:(0,n.jsx)("div",{className:"font-500 font-frutiger text-black mb-2 text-lg sm:text-xl lg:text-2xl leading-6 sm:leading-8",children:"Welcome to Platyfend"})}),(0,n.jsxs)("div",{className:"relative bg-white/60 backdrop-blur-sm rounded-2xl p-1.5 mb-6 sm:mb-8 shadow-lg border border-white/20",children:[(0,n.jsxs)("div",{className:"flex relative",children:[(0,n.jsx)("div",{className:"absolute top-1.5 bottom-1.5 bg-white rounded-xl shadow-md transition-all duration-300 ease-out ".concat("saas"===e?"left-1.5 right-1/2 mr-0.5":"right-1.5 left-1/2 ml-0.5")}),(0,n.jsx)("button",{onClick:()=>t("saas"),className:"relative flex-1 py-2.5 sm:py-3 px-3 sm:px-4 rounded-xl text-sm font-semibold transition-all duration-300 cursor-pointer ".concat("saas"===e?"text-blue-700 z-10":"text-slate-600 hover:text-slate-800"),children:(0,n.jsx)("div",{className:"flex items-center justify-center space-x-2",children:(0,n.jsx)("span",{className:"text-xs sm:text-sm",children:"Cloud SaaS"})})}),(0,n.jsx)("button",{onClick:()=>t("self-hosted"),className:"relative flex-1 py-2.5 sm:py-3 px-3 sm:px-4 rounded-xl text-sm font-semibold transition-all duration-300 cursor-pointer ".concat("self-hosted"===e?"text-blue-700 z-10":"text-slate-600 hover:text-slate-800"),children:(0,n.jsx)("div",{className:"flex items-center justify-center space-x-2",children:(0,n.jsx)("span",{className:"text-xs sm:text-sm",children:"Self-Hosted"})})})]}),(0,n.jsx)("div",{className:"mt-3 text-xs text-slate-500 font-medium",children:"saas"===e?"Managed cloud platform with enterprise security":"Deploy on your infrastructure with full control"})]})]}),(0,n.jsx)(l.Zp,{className:"border-0 shadow-2xl shadow-slate-900/5 bg-white/70 backdrop-blur-md rounded-3xl overflow-hidden",children:(0,n.jsxs)(l.Wu,{className:"p-6 sm:p-8",children:[(0,n.jsxs)(k,{defaultValue:e,value:e,onValueChange:e=>t(e),className:"w-full",children:[(0,n.jsxs)(F,{className:"hidden",children:[(0,n.jsx)(E,{value:"saas",children:"Cloud SaaS"}),(0,n.jsx)(E,{value:"self-hosted",children:"Self-Hosted"})]}),(0,n.jsx)(G,{value:"saas",className:"ring-offset-background focus-visible:ring-ring mt-2 focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden min-h-[12rem] sm:h-[15rem]",children:f("saas").map((e,t)=>(0,n.jsx)(i.$,{onClick:e.onClick,variant:"outline",className:"\n                        inline-flex items-center justify-center whitespace-nowrap text-sm sm:text-base\n                        ring-offset-background transition-all duration-300 ease-in-out\n                        focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\n                        disabled:pointer-events-none disabled:opacity-50\n                        w-full bg-white border-none rounded-4xl h-12 sm:h-13 max-w-96 mx-auto hover:bg-white ".concat(t>0?"mt-3 sm:mt-4":""," px-3 sm:px-4 py-2\n                        text-slate-800 shadow-sm\n                        hover:bg-white hover:cursor-pointer hover:shadow-md\n                        hover:translate-y-[-2px] hover:border-blue-300\n                        active:translate-y-[0px] active:shadow-sm\n                        ").concat(e.color,"\n                      "),children:(0,n.jsxs)("span",{className:"inline-flex items-center w-[240px] sm:w-[280px] pl-12",children:[(0,n.jsx)(e.icon,{}),e.name]})},e.name))}),(0,n.jsx)(G,{value:"self-hosted",className:"ring-offset-background focus-visible:ring-ring mt-2 focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden min-h-[12rem] sm:h-[15rem]",children:f("self-hosted").map((e,t)=>(0,n.jsx)(i.$,{onClick:e.onClick,variant:"outline",className:"\n                        inline-flex items-center justify-center whitespace-nowrap text-sm sm:text-base\n                        ring-offset-background transition-all duration-300 ease-in-out\n                        focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\n                        disabled:pointer-events-none disabled:opacity-50\n                        w-full bg-white border-none rounded-4xl h-12 sm:h-13 max-w-96 mx-auto hover:bg-white ".concat(t>0?"mt-3 sm:mt-4":""," px-3 sm:px-4 py-2\n                        text-slate-800 shadow-sm\n                        hover:bg-white hover:cursor-pointer hover:shadow-md\n                        hover:translate-y-[-2px] hover:border-blue-300\n                        active:translate-y-[0px] active:shadow-sm\n                        ").concat(e.color,"\n                      "),children:(0,n.jsxs)("span",{className:"inline-flex items-center w-[240px] sm:w-[280px] pl-12",children:[(0,n.jsx)(e.icon,{}),e.name]})},e.name))})]}),(0,n.jsx)("div",{className:"mt-6 sm:mt-8 pt-6 sm:pt-8 text-center",children:(0,n.jsxs)("p",{className:"text-sm text-slate-600",children:["Don't have an account?"," ",(0,n.jsx)("button",{onClick:()=>console.log("Sign up clicked"),className:"text-blue-600 hover:text-blue-700 hover:underline transition-colors underline-offset-2 font-semibold cursor-pointer",children:"Sign up here"})]})}),(0,n.jsx)("div",{className:"text-center mt-6 sm:mt-8 pt-4 sm:pt-6 border-t border-slate-200/30",children:(0,n.jsxs)("p",{className:"text-xs text-slate-500 leading-relaxed font-medium px-2",children:["By continuing, you agree to our"," ",(0,n.jsx)("button",{onClick:()=>console.log("Terms clicked"),className:"text-blue-600 hover:text-blue-700 hover:underline transition-colors underline-offset-2 font-semibold cursor-pointer",children:"Terms of Service"})," ","and"," ",(0,n.jsx)("button",{onClick:()=>console.log("Privacy clicked"),className:"text-blue-600 hover:text-blue-700 hover:underline transition-colors underline-offset-2 font-semibold cursor-pointer",children:"Privacy Policy"})]})})]})})]})})]})}G.displayName=D.displayName},4315:(e,t,r)=>{"use strict";r.d(t,{jH:()=>s});var n=r(2115);r(5155);var o=n.createContext(void 0);function s(e){let t=n.useContext(o);return e||t||"ltr"}},5695:(e,t,r)=>{"use strict";var n=r(8999);r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},6695:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>c,ZB:()=>l,Zp:()=>a,aR:()=>i});var n=r(5155),o=r(2115),s=r(1253);let a=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...o})});a.displayName="Card";let i=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",r),...o})});i.displayName="CardHeader";let l=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)("h3",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",r),...o})});l.displayName="CardTitle",o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",r),...o})}).displayName="CardDescription";let c=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",r),...o})});c.displayName="CardContent",o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",r),...o})}).displayName="CardFooter"},6713:(e,t,r)=>{Promise.resolve().then(r.bind(r,1985))},8133:(e,t,r)=>{"use strict";r.d(t,{Pt:()=>o,S3:()=>a,do:()=>s,z5:()=>i});let n="platyfend_session_token";function o(){return localStorage.getItem(n)}function s(e){localStorage.setItem(n,e)}function a(){localStorage.removeItem(n)}function i(){let e=o();return e?{Authorization:"Bearer ".concat(e)}:{}}},8382:(e,t,r)=>{"use strict";r.d(t,{A:()=>l,AuthProvider:()=>i});var n=r(5155),o=r(2115),s=r(8133);let a=(0,o.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,o.useState)(null),[l,c]=(0,o.useState)(null),[d,u]=(0,o.useState)(!0);(0,o.useEffect)(()=>{let e=(0,s.Pt)();e?(c(e),f(e)):u(!1)},[]);let f=async e=>{try{let t=await fetch("".concat("https://harmless-fit-mink.ngrok-free.app","/api/v1/login/validate-session"),{headers:{Authorization:"Bearer ".concat(e)}});if(t.ok){let e=await t.json();i(e)}else(0,s.S3)(),c(null),i(null)}catch(e){(0,s.S3)(),c(null),i(null)}finally{u(!1)}};return(0,n.jsx)(a.Provider,{value:{user:r,token:l,isLoading:d,isAuthenticated:!!l&&!!r,logout:()=>{(0,s.S3)(),c(null),i(null)}},children:t})}function l(){let e=(0,o.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},9196:(e,t,r)=>{"use strict";r.d(t,{RG:()=>y,bL:()=>A,q7:()=>I});var n=r(2115),o=r(5185),s=r(7328),a=r(6101),i=r(6081),l=r(1285),c=r(3655),d=r(9033),u=r(5845),f=r(4315),m=r(5155),h="rovingFocusGroup.onEntryFocus",x={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[p,g,b]=(0,s.N)(v),[w,y]=(0,i.A)(v,[b]),[j,k]=w(v),N=n.forwardRef((e,t)=>(0,m.jsx)(p.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(p.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(C,{...e,ref:t})})}));N.displayName=v;var C=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:s,loop:i=!1,dir:l,currentTabStopId:p,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:w,onEntryFocus:y,preventScrollOnEntryFocus:k=!1,...N}=e,C=n.useRef(null),S=(0,a.s)(t,C),R=(0,f.jH)(l),[L,A]=(0,u.i)({prop:p,defaultProp:null!=b?b:null,onChange:w,caller:v}),[I,P]=n.useState(!1),F=(0,d.c)(y),E=g(r),G=n.useRef(!1),[T,z]=n.useState(0);return n.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(h,F),()=>e.removeEventListener(h,F)},[F]),(0,m.jsx)(j,{scope:r,orientation:s,dir:R,loop:i,currentTabStopId:L,onItemFocus:n.useCallback(e=>A(e),[A]),onItemShiftTab:n.useCallback(()=>P(!0),[]),onFocusableItemAdd:n.useCallback(()=>z(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>z(e=>e-1),[]),children:(0,m.jsx)(c.sG.div,{tabIndex:I||0===T?-1:0,"data-orientation":s,...N,ref:S,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{G.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!G.current;if(e.target===e.currentTarget&&t&&!I){let t=new CustomEvent(h,x);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=E().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===L),...e].filter(Boolean).map(e=>e.ref.current),k)}}G.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>P(!1))})})}),S="RovingFocusGroupItem",R=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:s=!0,active:a=!1,tabStopId:i,children:d,...u}=e,f=(0,l.B)(),h=i||f,x=k(S,r),v=x.currentTabStopId===h,b=g(r),{onFocusableItemAdd:w,onFocusableItemRemove:y,currentTabStopId:j}=x;return n.useEffect(()=>{if(s)return w(),()=>y()},[s,w,y]),(0,m.jsx)(p.ItemSlot,{scope:r,id:h,focusable:s,active:a,children:(0,m.jsx)(c.sG.span,{tabIndex:v?0:-1,"data-orientation":x.orientation,...u,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{s?x.onItemFocus(h):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>x.onItemFocus(h)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void x.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return L[o]}(e,x.orientation,x.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=x.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>D(r))}}),children:"function"==typeof d?d({isCurrentTabStop:v,hasTabStop:null!=j}):d})})});R.displayName=S;var L={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var A=N,I=R}},e=>{var t=t=>e(e.s=t);e.O(0,[277,695,441,684,358],()=>t(6713)),_N_E=e.O()}]);