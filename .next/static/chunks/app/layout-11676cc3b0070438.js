try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="143710f4-ccb3-4062-a744-a2696e7ca2e4",e._sentryDebugIdIdentifier="sentry-dbid-143710f4-ccb3-4062-a744-a2696e7ca2e4")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},1253:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(2596),o=r(9688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,s.$)(t))}},7835:(e,t,r)=>{Promise.resolve().then(r.bind(r,9613)),Promise.resolve().then(r.bind(r,1362)),Promise.resolve().then(r.t.bind(r,4525,23)),Promise.resolve().then(r.t.bind(r,3067,23)),Promise.resolve().then(r.t.bind(r,7752,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,9074)),Promise.resolve().then(r.bind(r,8743)),Promise.resolve().then(r.bind(r,8382))},8133:(e,t,r)=>{"use strict";r.d(t,{Pt:()=>o,S3:()=>n,do:()=>a,z5:()=>i});let s="platyfend_session_token";function o(){return localStorage.getItem(s)}function a(e){localStorage.setItem(s,e)}function n(){localStorage.removeItem(s)}function i(){let e=o();return e?{Authorization:"Bearer ".concat(e)}:{}}},8382:(e,t,r)=>{"use strict";r.d(t,{A:()=>d,AuthProvider:()=>i});var s=r(5155),o=r(2115),a=r(8133);let n=(0,o.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,o.useState)(null),[d,u]=(0,o.useState)(null),[l,c]=(0,o.useState)(!0);(0,o.useEffect)(()=>{let e=(0,a.Pt)();e?(u(e),f(e)):c(!1)},[]);let f=async e=>{try{let t=await fetch("".concat("https://harmless-fit-mink.ngrok-free.app","/api/v1/login/validate-session"),{headers:{Authorization:"Bearer ".concat(e)}});if(t.ok){let e=await t.json();i(e)}else(0,a.S3)(),u(null),i(null)}catch(e){(0,a.S3)(),u(null),i(null)}finally{c(!1)}};return(0,s.jsx)(n.Provider,{value:{user:r,token:d,isLoading:l,isAuthenticated:!!d&&!!r,logout:()=>{(0,a.S3)(),u(null),i(null)}},children:t})}function d(){let e=(0,o.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},8743:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>T});var s=r(5155),o=r(2115);let a=0,n=new Map,i=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},5e3);n.set(e,t)},d=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},u=[],l={toasts:[]};function c(e){l=d(l,e),u.forEach(e=>{e(l)})}function f(e){let{...t}=e,r=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:r});return c({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||s()}}}),{id:r,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:r}})}}var p=r(6621),m=r(2085),g=r(4416),v=r(1253);let h=p.Kq,b=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)(p.LM,{ref:t,className:(0,v.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...o})});b.displayName=p.LM.displayName;let x=(0,m.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),y=o.forwardRef((e,t)=>{let{className:r,variant:o,...a}=e;return(0,s.jsx)(p.bL,{ref:t,className:(0,v.cn)(x({variant:o}),r),...a})});y.displayName=p.bL.displayName,o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)(p.rc,{ref:t,className:(0,v.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...o})}).displayName=p.rc.displayName;let w=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)(p.bm,{ref:t,className:(0,v.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...o,children:(0,s.jsx)(g.A,{className:"h-4 w-4"})})});w.displayName=p.bm.displayName;let S=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)(p.hE,{ref:t,className:(0,v.cn)("text-sm font-semibold",r),...o})});S.displayName=p.hE.displayName;let N=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)(p.VY,{ref:t,className:(0,v.cn)("text-sm opacity-90",r),...o})});function T(){let{toasts:e}=function(){let[e,t]=o.useState(l);return o.useEffect(()=>(u.push(t),()=>{let e=u.indexOf(t);e>-1&&u.splice(e,1)}),[]),{...e,toast:f,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}();return(0,s.jsxs)(h,{children:[e.map(function(e){let{id:t,title:r,description:o,action:a,...n}=e;return(0,s.jsxs)(y,{...n,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[r&&(0,s.jsx)(S,{children:r}),o&&(0,s.jsx)(N,{children:o})]}),a,(0,s.jsx)(w,{})]},t)}),(0,s.jsx)(b,{})]})}N.displayName=p.VY.displayName},9074:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>n});var s=r(5155),o=r(1362),a=r(6671);let n=e=>{let{...t}=e,{theme:r="system"}=(0,o.D)();return(0,s.jsx)(a.l$,{theme:r,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[363,277,695,512,295,441,684,358],()=>t(7835)),_N_E=e.O()}]);