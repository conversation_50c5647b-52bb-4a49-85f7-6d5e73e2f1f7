try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},s=(new e.Error).stack;s&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[s]="f7024927-133c-4048-aa3b-13982af1f7a4",e._sentryDebugIdIdentifier="sentry-dbid-f7024927-133c-4048-aa3b-13982af1f7a4")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{3565:(e,s,a)=>{Promise.resolve().then(a.bind(a,3792))},3792:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d});var n=a(5155);function d(){return(0,n.jsxs)("div",{className:"p-8",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold",children:"Welcome to Platyfend"}),(0,n.jsx)("p",{className:"text-gray-600 mt-2",children:"This is a UI-only build. Visit the dashboard or login pages to preview the UI."}),(0,n.jsxs)("div",{className:"mt-4 flex gap-4",children:[(0,n.jsx)("a",{href:"/dashboard",className:"underline text-primary",children:"Go to Dashboard"}),(0,n.jsx)("a",{href:"/login",className:"underline text-primary",children:"Login"})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(3565)),_N_E=e.O()}]);