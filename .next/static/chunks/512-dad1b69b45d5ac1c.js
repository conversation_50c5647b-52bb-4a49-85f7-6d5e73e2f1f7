try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="8481602e-3b71-4b06-b32e-6d69df6cac79",e._sentryDebugIdIdentifier="sentry-dbid-8481602e-3b71-4b06-b32e-6d69df6cac79")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[512],{2564:(e,t,n)=>{n.d(t,{bL:()=>s,s6:()=>a});var r=n(2115),o=n(3655),i=n(5155),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{...l,...e.style}}));a.displayName="VisuallyHidden";var s=a},4378:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(2115),o=n(7650),i=n(3655),l=n(2712),a=n(5155),s=r.forwardRef((e,t)=>{var n,s;let{container:u,...c}=e,[f,d]=r.useState(!1);(0,l.N)(()=>d(!0),[]);let p=u||f&&(null==(s=globalThis)||null==(n=s.document)?void 0:n.body);return p?o.createPortal((0,a.jsx)(i.sG.div,{...c,ref:t}),p):null});s.displayName="Portal"},4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},8795:(e,t,n)=>{n.d(t,{Mz:()=>eU,i3:()=>eJ,UC:()=>eZ,bL:()=>eq,Bk:()=>eS});var r=n(2115);let o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,s=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}function y(e){return["top","bottom"].includes(p(e))?"y":"x"}function v(e){return e.replace(/start|end/g,e=>f[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function E(e,t,n){let r,{reference:o,floating:i}=e,l=y(t),a=m(y(t)),s=g(a),u=p(t),c="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,v=o[s]/2-i[s]/2;switch(u){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[a]-=v*(n&&c?-1:1);break;case"end":r[a]+=v*(n&&c?-1:1)}return r}let C=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=E(u,r,s),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:i,fn:m}=a[n],{x:g,y:y,data:v,reset:w}=await m({x:c,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,f=null!=y?y:f,p={...p,[i]:{...p[i],...v}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(u=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:f}=E(u,d,s)),n=-1)}return{x:c,y:f,placement:d,strategy:o,middlewareData:p}};async function R(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=x(h),g=a[p?"floating"===f?"reference":"floating":f],y=b(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(g)))||n?g:g.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),v="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),E=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},C=b(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:w,strategy:s}):v);return{top:(y.top-C.top+m.top)/E.y,bottom:(C.bottom-y.bottom+m.bottom)/E.y,left:(y.left-C.left+m.left)/E.x,right:(C.right-y.right+m.right)/E.x}}function T(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function L(e){return o.some(t=>e[t]>=0)}async function A(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=p(n),a=h(n),s="y"===y(n),u=["left","top"].includes(l)?-1:1,c=i&&s?-1:1,f=d(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:v}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof v&&(g="end"===a?-1*v:v),s?{x:g*c,y:m*u}:{x:m*u,y:g*c}}function k(){return"undefined"!=typeof window}function P(e){return S(e)?(e.nodeName||"").toLowerCase():"#document"}function O(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function D(e){var t;return null==(t=(S(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function S(e){return!!k()&&(e instanceof Node||e instanceof O(e).Node)}function j(e){return!!k()&&(e instanceof Element||e instanceof O(e).Element)}function N(e){return!!k()&&(e instanceof HTMLElement||e instanceof O(e).HTMLElement)}function W(e){return!!k()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof O(e).ShadowRoot)}function M(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=z(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function B(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function H(e){let t=F(),n=j(e)?z(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function F(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function _(e){return["html","body","#document"].includes(P(e))}function z(e){return O(e).getComputedStyle(e)}function I(e){return j(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function V(e){if("html"===P(e))return e;let t=e.assignedSlot||e.parentNode||W(e)&&e.host||D(e);return W(t)?t.host:t}function G(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=V(t);return _(n)?t.ownerDocument?t.ownerDocument.body:t.body:N(n)&&M(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=O(o);if(i){let e=Y(l);return t.concat(l,l.visualViewport||[],M(o)?o:[],e&&n?G(e):[])}return t.concat(o,G(o,[],n))}function Y(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function X(e){let t=z(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=N(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,s=a(n)!==i||a(r)!==l;return s&&(n=i,r=l),{width:n,height:r,$:s}}function $(e){return j(e)?e:e.contextElement}function q(e){let t=$(e);if(!N(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=X(t),l=(i?a(n.width):n.width)/r,s=(i?a(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),s&&Number.isFinite(s)||(s=1),{x:l,y:s}}let U=u(0);function Z(e){let t=O(e);return F()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:U}function J(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=$(e),a=u(1);t&&(r?j(r)&&(a=q(r)):a=q(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===O(l))&&o)?Z(l):u(0),c=(i.left+s.x)/a.x,f=(i.top+s.y)/a.y,d=i.width/a.x,p=i.height/a.y;if(l){let e=O(l),t=r&&j(r)?O(r):r,n=e,o=Y(n);for(;o&&r&&t!==n;){let e=q(o),t=o.getBoundingClientRect(),r=z(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,f*=e.y,d*=e.x,p*=e.y,c+=i,f+=l,o=Y(n=O(o))}}return b({width:d,height:p,x:c,y:f})}function K(e,t){let n=I(e).scrollLeft;return t?t.left+n:J(D(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:K(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=O(e),r=D(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,s=0;if(o){i=o.width,l=o.height;let e=F();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:i,height:l,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=D(e),n=I(e),r=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+K(e),s=-n.scrollTop;return"rtl"===z(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:s}}(D(e));else if(j(t))r=function(e,t){let n=J(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=N(e)?q(e):u(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=Z(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===z(e).position}function en(e,t){if(!N(e)||"fixed"===z(e).position)return null;if(t)return t(e);let n=e.offsetParent;return D(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=O(e);if(B(e))return n;if(!N(e)){let t=V(e);for(;t&&!_(t);){if(j(t)&&!et(t))return t;t=V(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(P(r))&&et(r);)r=en(r,t);return r&&_(r)&&et(r)&&!H(r)?n:r||function(e){let t=V(e);for(;N(t)&&!_(t);){if(H(t))return t;if(B(t))break;t=V(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=N(t),o=D(t),i="fixed"===n,l=J(e,!0,i,t),a={scrollLeft:0,scrollTop:0},s=u(0);if(r||!r&&!i)if(("body"!==P(t)||M(o))&&(a=I(t)),r){let e=J(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=K(o));i&&!r&&o&&(s.x=K(o));let c=!o||r||i?u(0):Q(o,a);return{x:l.left+a.scrollLeft-s.x-c.x,y:l.top+a.scrollTop-s.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=D(r),a=!!t&&B(t.floating);if(r===l||a&&i)return n;let s={scrollLeft:0,scrollTop:0},c=u(1),f=u(0),d=N(r);if((d||!d&&!i)&&(("body"!==P(r)||M(l))&&(s=I(r)),N(r))){let e=J(r);c=q(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!l||d||i?u(0):Q(l,s,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-s.scrollLeft*c.x+f.x+p.x,y:n.y*c.y-s.scrollTop*c.y+f.y+p.y}},getDocumentElement:D,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?B(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=G(e,[],!1).filter(e=>j(e)&&"body"!==P(e)),o=null,i="fixed"===z(e).position,l=i?V(e):e;for(;j(l)&&!_(l);){let t=z(l),n=H(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||M(l)&&!n&&function e(t,n){let r=V(t);return!(r===n||!j(r)||_(r))&&("fixed"===z(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=V(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],s=a[0],u=a.reduce((e,n)=>{let r=ee(t,n,o);return e.top=l(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,s,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=X(e);return{width:t,height:n}},getScale:q,isElement:j,isRTL:function(e){return"rtl"===z(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:a,platform:s,elements:u,middlewareData:c}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let v=x(p),w={x:n,y:r},b=m(y(o)),E=g(b),C=await s.getDimensions(f),R="y"===b,T=R?"clientHeight":"clientWidth",L=a.reference[E]+a.reference[b]-w[b]-a.floating[E],A=w[b]-a.reference[b],k=await (null==s.getOffsetParent?void 0:s.getOffsetParent(f)),P=k?k[T]:0;P&&await (null==s.isElement?void 0:s.isElement(k))||(P=u.floating[T]||a.floating[E]);let O=P/2-C[E]/2-1,D=i(v[R?"top":"left"],O),S=i(v[R?"bottom":"right"],O),j=P-C[E]-S,N=P/2-C[E]/2+(L/2-A/2),W=l(D,i(N,j)),M=!c.arrow&&null!=h(o)&&N!==W&&a.reference[E]/2-(N<D?D:S)-C[E]/2<0,B=M?N<D?N-D:N-j:0;return{[b]:w[b]+B,data:{[b]:W,centerOffset:N-W-B,...M&&{alignmentOffset:B}},reset:M}}}),es=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return C(e,t,{...o,platform:i})};var eu=n(7650),ec="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ef(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ef(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ef(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return ec(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),eg=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,s=await A(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:l}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:s=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=d(e,t),f={x:n,y:r},h=await R(t,c),g=y(p(o)),v=m(g),w=f[v],x=f[g];if(a){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=w+h[e],r=w-h[t];w=l(n,i(w,r))}if(s){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,i(x,r))}let b=u.fn({...t,[v]:w,[g]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[v]:a,[g]:s}}}}}}(e),options:[e,t]}),ev=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=d(e,t),c={x:n,y:r},f=y(o),h=m(f),g=c[h],v=c[f],w=d(a,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(s){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+x.mainAxis,n=i.reference[h]+i.reference[e]-x.mainAxis;g<t?g=t:g>n&&(g=n)}if(u){var b,E;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(b=l.offset)?void 0:b[f])||0)+(t?0:x.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[f])||0)-(t?x.crossAxis:0);v<n?v=n:v>r&&(v=r)}return{[h]:g,[f]:v}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l,a;let{placement:s,middlewareData:u,rects:c,initialPlacement:f,platform:x,elements:b}=t,{mainAxis:E=!0,crossAxis:C=!0,fallbackPlacements:T,fallbackStrategy:L="bestFit",fallbackAxisSideDirection:A="none",flipAlignment:k=!0,...P}=d(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let O=p(s),D=y(f),S=p(f)===f,j=await (null==x.isRTL?void 0:x.isRTL(b.floating)),N=T||(S||!k?[w(f)]:function(e){let t=w(e);return[v(e),t,v(t)]}(f)),W="none"!==A;!T&&W&&N.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(v)))),i}(f,k,A,j));let M=[f,...N],B=await R(t,P),H=[],F=(null==(r=u.flip)?void 0:r.overflows)||[];if(E&&H.push(B[O]),C){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(y(e)),i=g(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=w(l)),[l,w(l)]}(s,c,j);H.push(B[e[0]],B[e[1]])}if(F=[...F,{placement:s,overflows:H}],!H.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=M[e];if(t){let n="alignment"===C&&D!==y(t),r=(null==(l=F[0])?void 0:l.overflows[0])>0;if(!n||r)return{data:{index:e,overflows:F},reset:{placement:t}}}let n=null==(i=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(L){case"bestFit":{let e=null==(a=F.filter(e=>{if(W){let t=y(e.placement);return t===D||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=f}if(s!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,a,{placement:s,rects:u,platform:c,elements:f}=t,{apply:m=()=>{},...g}=d(e,t),v=await R(t,g),w=p(s),x=h(s),b="y"===y(s),{width:E,height:C}=u.floating;"top"===w||"bottom"===w?(o=w,a=x===(await (null==c.isRTL?void 0:c.isRTL(f.floating))?"start":"end")?"left":"right"):(a=w,o="end"===x?"top":"bottom");let T=C-v.top-v.bottom,L=E-v.left-v.right,A=i(C-v[o],T),k=i(E-v[a],L),P=!t.middlewareData.shift,O=A,D=k;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(D=L),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(O=T),P&&!x){let e=l(v.left,0),t=l(v.right,0),n=l(v.top,0),r=l(v.bottom,0);b?D=E-2*(0!==e||0!==t?e+t:l(v.left,v.right)):O=C-2*(0!==n||0!==r?n+r:l(v.top,v.bottom))}await m({...t,availableWidth:D,availableHeight:O});let S=await c.getDimensions(f.floating);return E!==S.width||C!==S.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=d(e,t);switch(r){case"referenceHidden":{let e=T(await R(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:L(e)}}}case"escaped":{let e=T(await R(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:L(e)}}}default:return{}}}}}(e),options:[e,t]}),eE=(e,t)=>({...em(e),options:[e,t]});var eC=n(3655),eR=n(5155),eT=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eR.jsx)(eC.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eR.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eT.displayName="Arrow";var eL=n(6101),eA=n(6081),ek=n(9033),eP=n(2712),eO="Popper",[eD,eS]=(0,eA.A)(eO),[ej,eN]=eD(eO),eW=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eR.jsx)(ej,{scope:t,anchor:o,onAnchorChange:i,children:n})};eW.displayName=eO;var eM="PopperAnchor",eB=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=eN(eM,n),a=r.useRef(null),s=(0,eL.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,eR.jsx)(eC.sG.div,{...i,ref:s})});eB.displayName=eM;var eH="PopperContent",[eF,e_]=eD(eH),ez=r.forwardRef((e,t)=>{var n,o,a,u,c,f,d,p;let{__scopePopper:h,side:m="bottom",sideOffset:g=0,align:y="center",alignOffset:v=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:E=0,sticky:C="partial",hideWhenDetached:R=!1,updatePositionStrategy:T="optimized",onPlaced:L,...A}=e,k=eN(eH,h),[P,O]=r.useState(null),S=(0,eL.s)(t,e=>O(e)),[j,N]=r.useState(null),W=function(e){let[t,n]=r.useState(void 0);return(0,eP.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(j),M=null!=(d=null==W?void 0:W.width)?d:0,B=null!=(p=null==W?void 0:W.height)?p:0,H="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},F=Array.isArray(b)?b:[b],_=F.length>0,z={padding:H,boundary:F.filter(eY),altBoundary:_},{refs:I,floatingStyles:V,placement:Y,isPositioned:X,middlewareData:q}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ef(p,o)||h(o);let[m,g]=r.useState(null),[y,v]=r.useState(null),w=r.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),b=l||m,E=a||y,C=r.useRef(null),R=r.useRef(null),T=r.useRef(f),L=null!=u,A=eh(u),k=eh(i),P=eh(c),O=r.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};k.current&&(e.platform=k.current),es(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};D.current&&!ef(T.current,t)&&(T.current=t,eu.flushSync(()=>{d(t)}))})},[p,t,n,k,P]);ec(()=>{!1===c&&T.current.isPositioned&&(T.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let D=r.useRef(!1);ec(()=>(D.current=!0,()=>{D.current=!1}),[]),ec(()=>{if(b&&(C.current=b),E&&(R.current=E),b&&E){if(A.current)return A.current(b,E,O);O()}},[b,E,O,A,L]);let S=r.useMemo(()=>({reference:C,floating:R,setReference:w,setFloating:x}),[w,x]),j=r.useMemo(()=>({reference:b,floating:E}),[b,E]),N=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=ep(j.floating,f.x),r=ep(j.floating,f.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,j.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:O,refs:S,elements:j,floatingStyles:N}),[f,O,S,j,N])}({strategy:"fixed",placement:m+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=$(e),h=a||u?[...p?G(p):[],...G(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&f?function(e,t){let n,r=null,o=D(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(c,f){void 0===c&&(c=!1),void 0===f&&(f=1),a();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(c||t(),!m||!g)return;let y=s(h),v=s(o.clientWidth-(p+m)),w={rootMargin:-y+"px "+-v+"px "+-s(o.clientHeight-(h+g))+"px "+-s(p)+"px",threshold:l(0,i(1,f))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==f){if(!x)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||el(d,e.getBoundingClientRect())||u(),x=!1}try{r=new IntersectionObserver(b,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),a}(p,n):null,g=-1,y=null;c&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),p&&!d&&y.observe(p),y.observe(t));let v=d?J(e):null;return d&&function t(){let r=J(e);v&&!el(v,r)&&n(),v=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===T})},elements:{reference:k.anchor},middleware:[eg({mainAxis:g+B,alignmentAxis:v}),x&&ey({mainAxis:!0,crossAxis:!1,limiter:"partial"===C?ev():void 0,...z}),x&&ew({...z}),ex({...z,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),j&&eE({element:j,padding:w}),eX({arrowWidth:M,arrowHeight:B}),R&&eb({strategy:"referenceHidden",...z})]}),[U,Z]=e$(Y),K=(0,ek.c)(L);(0,eP.N)(()=>{X&&(null==K||K())},[X,K]);let Q=null==(n=q.arrow)?void 0:n.x,ee=null==(o=q.arrow)?void 0:o.y,et=(null==(a=q.arrow)?void 0:a.centerOffset)!==0,[en,er]=r.useState();return(0,eP.N)(()=>{P&&er(window.getComputedStyle(P).zIndex)},[P]),(0,eR.jsx)("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:X?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(u=q.transformOrigin)?void 0:u.x,null==(c=q.transformOrigin)?void 0:c.y].join(" "),...(null==(f=q.hide)?void 0:f.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eR.jsx)(eF,{scope:h,placedSide:U,onArrowChange:N,arrowX:Q,arrowY:ee,shouldHideArrow:et,children:(0,eR.jsx)(eC.sG.div,{"data-side":U,"data-align":Z,...A,ref:S,style:{...A.style,animation:X?void 0:"none"}})})})});ez.displayName=eH;var eI="PopperArrow",eV={top:"bottom",right:"left",bottom:"top",left:"right"},eG=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e_(eI,n),i=eV[o.placedSide];return(0,eR.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eR.jsx)(eT,{...r,ref:t,style:{...r.style,display:"block"}})})});function eY(e){return null!==e}eG.displayName=eI;var eX=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:s,middlewareData:u}=t,c=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,f=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[p,h]=e$(a),m={start:"0%",center:"50%",end:"100%"}[h],g=(null!=(i=null==(r=u.arrow)?void 0:r.x)?i:0)+f/2,y=(null!=(l=null==(o=u.arrow)?void 0:o.y)?l:0)+d/2,v="",w="";return"bottom"===p?(v=c?m:"".concat(g,"px"),w="".concat(-d,"px")):"top"===p?(v=c?m:"".concat(g,"px"),w="".concat(s.floating.height+d,"px")):"right"===p?(v="".concat(-d,"px"),w=c?m:"".concat(y,"px")):"left"===p&&(v="".concat(s.floating.width+d,"px"),w=c?m:"".concat(y,"px")),{data:{x:v,y:w}}}});function e$(e){let[t,n="center"]=e.split("-");return[t,n]}var eq=eW,eU=eB,eZ=ez,eJ=eG},9178:(e,t,n)=>{n.d(t,{lg:()=>y,qW:()=>d,bL:()=>g});var r,o=n(2115),i=n(5185),l=n(3655),a=n(6101),s=n(9033),u=n(5155),c="dismissableLayer.update",f=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=o.forwardRef((e,t)=>{var n,d;let{disableOutsidePointerEvents:p=!1,onEscapeKeyDown:g,onPointerDownOutside:y,onFocusOutside:v,onInteractOutside:w,onDismiss:x,...b}=e,E=o.useContext(f),[C,R]=o.useState(null),T=null!=(d=null==C?void 0:C.ownerDocument)?d:null==(n=globalThis)?void 0:n.document,[,L]=o.useState({}),A=(0,a.s)(t,e=>R(e)),k=Array.from(E.layers),[P]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),O=k.indexOf(P),D=C?k.indexOf(C):-1,S=E.layersWithOutsidePointerEventsDisabled.size>0,j=D>=O,N=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,s.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){m("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));j&&!n&&(null==y||y(e),null==w||w(e),e.defaultPrevented||null==x||x())},T),W=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,s.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==v||v(e),null==w||w(e),e.defaultPrevented||null==x||x())},T);return!function(e,t=globalThis?.document){let n=(0,s.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{D===E.layers.size-1&&(null==g||g(e),!e.defaultPrevented&&x&&(e.preventDefault(),x()))},T),o.useEffect(()=>{if(C)return p&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=T.body.style.pointerEvents,T.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(C)),E.layers.add(C),h(),()=>{p&&1===E.layersWithOutsidePointerEventsDisabled.size&&(T.body.style.pointerEvents=r)}},[C,T,p,E]),o.useEffect(()=>()=>{C&&(E.layers.delete(C),E.layersWithOutsidePointerEventsDisabled.delete(C),h())},[C,E]),o.useEffect(()=>{let e=()=>L({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(l.sG.div,{...b,ref:A,style:{pointerEvents:S?j?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,W.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,W.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,N.onPointerDownCapture)})});d.displayName="DismissableLayer";var p=o.forwardRef((e,t)=>{let n=o.useContext(f),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(l.sG.div,{...e,ref:i})});function h(){let e=new CustomEvent(c);document.dispatchEvent(e)}function m(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,l.hO)(i,a):i.dispatchEvent(a)}p.displayName="DismissableLayerBranch";var g=d,y=p},9613:(e,t,n)=>{n.d(t,{Content:()=>V,Provider:()=>_,Root:()=>z,Trigger:()=>I});var r=n(2115),o=n(5185),i=n(6101),l=n(6081),a=n(9178),s=n(1285),u=n(8795),c=(n(4378),n(8905)),f=n(3655),d=n(9708),p=n(5845),h=n(2564),m=n(5155),[g,y]=(0,l.A)("Tooltip",[u.Bk]),v=(0,u.Bk)(),w="TooltipProvider",x="tooltip.open",[b,E]=g(w),C=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,a=r.useRef(!0),s=r.useRef(!1),u=r.useRef(0);return r.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,m.jsx)(b,{scope:t,isOpenDelayedRef:a,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:r.useCallback(e=>{s.current=e},[]),disableHoverableContent:i,children:l})};C.displayName=w;var R="Tooltip",[T,L]=g(R),A=e=>{let{__scopeTooltip:t,children:n,open:o,defaultOpen:i,onOpenChange:l,disableHoverableContent:a,delayDuration:c}=e,f=E(R,e.__scopeTooltip),d=v(t),[h,g]=r.useState(null),y=(0,s.B)(),w=r.useRef(0),b=null!=a?a:f.disableHoverableContent,C=null!=c?c:f.delayDuration,L=r.useRef(!1),[A,k]=(0,p.i)({prop:o,defaultProp:null!=i&&i,onChange:e=>{e?(f.onOpen(),document.dispatchEvent(new CustomEvent(x))):f.onClose(),null==l||l(e)},caller:R}),P=r.useMemo(()=>A?L.current?"delayed-open":"instant-open":"closed",[A]),O=r.useCallback(()=>{window.clearTimeout(w.current),w.current=0,L.current=!1,k(!0)},[k]),D=r.useCallback(()=>{window.clearTimeout(w.current),w.current=0,k(!1)},[k]),S=r.useCallback(()=>{window.clearTimeout(w.current),w.current=window.setTimeout(()=>{L.current=!0,k(!0),w.current=0},C)},[C,k]);return r.useEffect(()=>()=>{w.current&&(window.clearTimeout(w.current),w.current=0)},[]),(0,m.jsx)(u.bL,{...d,children:(0,m.jsx)(T,{scope:t,contentId:y,open:A,stateAttribute:P,trigger:h,onTriggerChange:g,onTriggerEnter:r.useCallback(()=>{f.isOpenDelayedRef.current?S():O()},[f.isOpenDelayedRef,S,O]),onTriggerLeave:r.useCallback(()=>{b?D():(window.clearTimeout(w.current),w.current=0)},[D,b]),onOpen:O,onClose:D,disableHoverableContent:b,children:n})})};A.displayName=R;var k="TooltipTrigger",P=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...l}=e,a=L(k,n),s=E(k,n),c=v(n),d=r.useRef(null),p=(0,i.s)(t,d,a.onTriggerChange),h=r.useRef(!1),g=r.useRef(!1),y=r.useCallback(()=>h.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",y),[y]),(0,m.jsx)(u.Mz,{asChild:!0,...c,children:(0,m.jsx)(f.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(g.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),g.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),g.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),h.current=!0,document.addEventListener("pointerup",y,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{h.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});P.displayName=k;var[O,D]=g("TooltipPortal",{forceMount:void 0}),S="TooltipContent",j=r.forwardRef((e,t)=>{let n=D(S,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,l=L(S,e.__scopeTooltip);return(0,m.jsx)(c.C,{present:r||l.open,children:l.disableHoverableContent?(0,m.jsx)(H,{side:o,...i,ref:t}):(0,m.jsx)(N,{side:o,...i,ref:t})})}),N=r.forwardRef((e,t)=>{let n=L(S,e.__scopeTooltip),o=E(S,e.__scopeTooltip),l=r.useRef(null),a=(0,i.s)(t,l),[s,u]=r.useState(null),{trigger:c,onClose:f}=n,d=l.current,{onPointerInTransitChange:p}=o,h=r.useCallback(()=>{u(null),p(!1)},[p]),g=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),p(!0)},[p]);return r.useEffect(()=>()=>h(),[h]),r.useEffect(()=>{if(c&&d){let e=e=>g(e,d),t=e=>g(e,c);return c.addEventListener("pointerleave",e),d.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),d.removeEventListener("pointerleave",t)}}},[c,d,g,h]),r.useEffect(()=>{if(s){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==d?void 0:d.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],s=l.x,u=l.y,c=a.x,f=a.y;u>r!=f>r&&n<(c-s)*(r-u)/(f-u)+s&&(o=!o)}return o}(n,s);r?h():o&&(h(),f())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,d,s,f,h]),(0,m.jsx)(H,{...e,ref:a})}),[W,M]=g(R,{isInside:!1}),B=(0,d.Dc)("TooltipContent"),H=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:s,...c}=e,f=L(S,n),d=v(n),{onClose:p}=f;return r.useEffect(()=>(document.addEventListener(x,p),()=>document.removeEventListener(x,p)),[p]),r.useEffect(()=>{if(f.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(f.trigger))&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[f.trigger,p]),(0,m.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,m.jsxs)(u.UC,{"data-state":f.stateAttribute,...d,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,m.jsx)(B,{children:o}),(0,m.jsx)(W,{scope:n,isInside:!0,children:(0,m.jsx)(h.bL,{id:f.contentId,role:"tooltip",children:i||o})})]})})});j.displayName=S;var F="TooltipArrow";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=v(n);return M(F,n).isInside?null:(0,m.jsx)(u.i3,{...o,...r,ref:t})}).displayName=F;var _=C,z=A,I=P,V=j},9946:(e,t,n)=>{n.d(t,{A:()=>f});var r=n(2115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()},s=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:c="",children:f,iconNode:d,...p}=e;return(0,r.createElement)("svg",{ref:t,...u,width:o,height:o,stroke:n,strokeWidth:l?24*Number(i)/Number(o):i,className:a("lucide",c),...!f&&!s(p)&&{"aria-hidden":"true"},...p},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(f)?f:[f]])}),f=(e,t)=>{let n=(0,r.forwardRef)((n,i)=>{let{className:s,...u}=n;return(0,r.createElement)(c,{ref:i,iconNode:t,className:a("lucide-".concat(o(l(e))),"lucide-".concat(e),s),...u})});return n.displayName=l(e),n}}}]);