{"version": 3, "sources": ["webpack://_N_E/node_modules/next/font/google/%3Cinput%20css%20vx-xYj%3E", "webpack://_N_E/<no source>", "webpack://_N_E/c41dbb6f8d248dcf.css", "webpack://_N_E/node_modules/next/font/google/%3Cinput%20css%201XN0rn%3E", "webpack://_N_E/node_modules/next/font/local/%3Cinput%20css%20V5Udc6%3E", "webpack://_N_E/src/app/globals.css"], "names": [], "mappings": "AACA,WACE,iBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,gFACF,CAEA,WACE,iBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,+DACF,CAEA,WACE,iBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,oBACF,CAEA,WACE,iBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,gFACF,CAEA,WACE,iBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,0JACF,CAEA,WACE,iBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,gMACF,CAEA,WACE,iBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,uEAAwE,CACxE,iKACF,CC9DA,WAAA,0BAAA,CAAA,kBAAA,CAAA,sBAAA,CAAA,uBAAA,CAAA,uBAAA,CAAA,mBC+DA,CD/DA,oBAAA,gCAAA,CAAA,iBCgEA,CDhEA,mBAAA,qCCiEA,CChEA,WACE,0BAA6B,CAC7B,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,gFACF,CAEA,WACE,0BAA6B,CAC7B,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,+DACF,CAEA,WACE,0BAA6B,CAC7B,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,gFACF,CAEA,WACE,0BAA6B,CAC7B,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,0JACF,CAEA,WACE,0BAA6B,CAC7B,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,gMACF,CAEA,WACE,0BAA6B,CAC7B,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,qEAAsE,CACtE,iKACF,CFrDA,WAAA,mCAAA,CAAA,kBAAA,CAAA,sBAAA,CAAA,uBAAA,CAAA,uBAAA,CAAA,mBCyHA,CDzHA,oBAAA,kDAAA,CAAA,iBC0HA,CD1HA,mBAAA,gEC2HA,CE3HA,WACA,oBAAqB,CACrB,qEAAsE,CACtE,iBAAkB,CAClB,eAAgB,CAChB,iBACA,CHNA,WAAA,6BAAA,CAAA,kBAAA,CAAA,sBAAA,CAAA,uBAAA,CAAA,uBAAA,CAAA,mBCoIA,CDpIA,oBAAA,sCAAA,CAAA,eAAA,CAAA,iBCqIA,CDrIA,mBAAA,8CCsIA;;AGtIA,iEAAiE,CACjE,kBAAkB,kIAAoI,4BAA4B,kBAAkB,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,8BAA8B,CAAC,wBAAwB,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,2BAA2B,CAAC,+BAA+B,CAAC,8BAA8B,CAAC,8BAA8B,CAAC,8BAA8B,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,2BAA2B,CAAC,4BAA4B,CAAC,6BAA6B,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,sBAAsB,CAAC,2BAA2B,CAAC,+BAA+B,CAAC,4BAA4B,CAAC,uBAAuB,CAAC,0BAA0B,CAAC,6BAA6B,CAAC,gCAAgC,CAAC,uBAAuB,CAAC,0BAA0B,CAAC,2BAA2B,CAAC,iCAAiC,CAAC,wBAAwB,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,qBAAqB,CAAC,sBAAsB,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,8BAA8B,CAAC,2BAA2B,CAAC,6BAA6B,CAAC,0BAA0B,CAAC,gCAAgC,CAAC,8BAA8B,CAAC,+BAA+B,CAAC,gCAAgC,CAAC,4BAA4B,CAAC,6BAA6B,CAAC,8BAA8B,CAAC,2BAA2B,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC,CAAC,aAAa,YAAY,wHAAwH,CAAC,uGAAuG,CAAC,sCAAsC,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,2CAA2C,CAAC,yCAAyC,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,yCAAyC,CAAC,0CAA0C,CAAC,0CAA0C,CAAC,wCAAwC,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,kCAAkC,CAAC,iBAAiB,CAAC,sCAAsC,CAAC,gBAAgB,CAAC,oCAAoC,CAAC,kBAAkB,CAAC,uCAAuC,CAAC,iBAAiB,CAAC,sCAAsC,CAAC,iBAAiB,CAAC,mCAAmC,CAAC,mBAAmB,CAAC,wCAAwC,CAAC,kBAAkB,CAAC,sCAAsC,CAAC,wBAAwB,CAAC,wBAAwB,CAAC,0BAA0B,CAAC,sBAAsB,CAAC,wBAAwB,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,qCAAqC,CAAC,qCAAqC,CAAC,yBAAyB,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,iCAAiC,CAAC,qCAAqC,CAAC,sCAAsC,CAAC,wDAAwD,CAAC,aAAa,CAAC,cAAc,CAAC,eAAe,CAAC,mBAAmB,CAAC,kCAAkC,CAAC,4DAA4D,CAAC,sCAAsC,CAAC,2CAA2C,CAAC,gBAAgB,CAAC,oDAAoD,CAAC,gDAAgD,CAAC,6BAA6B,CAAC,6BAA6B,CAAC,yCAAyC,CAAC,yCAAyC,CAAC,6BAA6B,CAAC,mDAAmD,CAAC,mCAAmC,CAAC,yDAAyD,CAAC,mCAAmC,CAAC,yDAAyD,CAAC,uCAAuC,CAAC,6DAA6D,CAAC,+BAA+B,CAAC,qDAAqD,CAAC,iCAAiC,CAAC,uDAAuD,CAAC,2CAA2C,CAAC,iEAAiE,CAAC,iCAAiC,CAAC,+BAA+B,CAAC,6BAA6B,CAAC,mCAAmC,CAAC,yDAAyD,CAAC,iDAAiD,CAAC,uEAAuE,CAAC,iDAAiD,CAAC,6CAA6C,CAAC,CAAC,YAAY,4BAA4B,qBAAqB,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,uBAAuB,qBAAqB,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,6BAA6B,CAAC,eAAU,CAAV,aAAU,CAAV,UAAU,CAAC,eAAe,CAAC,mJAAmJ,CAAC,iEAAiE,CAAC,qEAAqE,CAAC,uCAAuC,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,oBAAoB,CAAC,oBAAoB,wCAAwC,CAAC,gCAAgC,CAAC,kBAAkB,iBAAiB,CAAC,mBAAmB,CAAC,EAAE,aAAa,CAAiE,+BAA+B,CAAC,uBAAuB,CAAC,SAAS,kBAAkB,CAAC,kBAAkB,uIAAuI,CAAC,sEAAsE,CAAC,0EAA0E,CAAC,aAAa,CAAC,MAAM,aAAa,CAAC,QAAQ,uBAAuB,CAAC,aAAa,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,aAAa,CAAC,IAAI,SAAS,CAAC,MAAM,aAAa,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,gBAAgB,YAAY,CAAC,SAAS,uBAAuB,CAAC,QAAQ,iBAAiB,CAAC,WAAW,eAAe,CAAC,+CAA+C,qBAAqB,CAAC,aAAa,CAAC,UAAU,cAAc,CAAC,WAAW,CAAC,sCAAsC,YAAY,CAAC,6BAA6B,CAAC,+BAA+B,CAAC,sBAAsB,CAAC,aAAa,CAAC,SAAS,CAAC,sBAAsB,CAAC,eAAe,CAAC,uBAAuB,YAAY,CAAC,6BAA6B,CAAC,+BAA+B,CAAC,sBAAsB,CAAC,aAAa,CAAC,SAAS,CAAC,sBAAsB,CAAC,eAAe,CAAC,8CAA8C,kBAAkB,CAAC,qDAAqD,yBAAyB,CAAC,uBAAuB,qBAAqB,CAAC,mBAAc,SAAS,CAAvB,cAAc,SAAS,CAAC,uFAAyF,mBAAc,kBAAkB,CAAhC,cAAc,kBAAkB,CAAC,4CAA8C,mBAAc,sDAAsD,CAApE,cAAc,sDAAsD,CAAC,CAAC,CAAC,SAAS,eAAe,CAAC,4BAA4B,uBAAuB,CAAC,8BAA8B,cAAc,CAAC,kBAAkB,CAAC,wBAAwB,mBAAmB,CAAC,uCAAuC,SAAS,CAAyC,2DAAmC,eAAe,CAAqD,sEAAkC,eAAe,CAAoD,wEAAqC,eAAe,CAAsD,+EAA0C,eAAe,CAAC,uCAAuC,eAAe,CAAC,iBAAiB,eAAe,CAAC,6DAA6D,yBAAgB,CAAhB,sBAAgB,CAAhB,iBAAiB,CAAC,uBAAuB,yBAAgB,CAAhB,sBAAgB,CAAhB,iBAAiB,CAAyC,wDAA4B,WAAW,CAAC,2CAA2C,sBAAsB,CAAC,CAAC,iBAAiB,CAAC,iBAAiB,qBAAqB,mBAAmB,CAAC,qBAAqB,mBAAmB,CAAC,WAAW,iBAAiB,CAAC,SAAS,kBAAkB,CAAC,kBAAkB,CAAC,cAAc,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,CAAmB,eAAe,CAAC,mBAAlC,iBAA6D,CAAC,OAAO,cAAc,CAAC,UAAU,iBAAiB,CAAC,QAAQ,eAAe,CAAC,SAAS,4BAA4B,CAAC,WAAW,mCAAmC,CAAC,WAAW,kCAAkC,CAAC,SAAS,4BAA4B,CAAC,OAAO,0BAA0B,CAAC,UAAU,4BAA4B,CAAC,UAAU,OAAO,CAAC,UAAU,OAAO,CAAC,OAAO,0BAA0B,CAAC,UAAU,4BAA4B,CAAC,OAAO,0BAA0B,CAAC,aAAa,OAAO,CAAC,cAAc,OAAO,CAAC,cAAc,OAAO,CAAC,UAAU,QAAQ,CAAC,WAAW,8BAA8B,CAAC,SAAS,4BAA4B,CAAC,SAAS,4BAA4B,CAAC,YAAY,8BAA8B,CAAC,YAAY,SAAS,CAAC,YAAY,SAAS,CAAC,SAAS,4BAA4B,CAAC,SAAS,4BAA4B,CAAC,SAAS,4BAA4B,CAAC,YAAY,+BAA+B,CAAC,UAAU,6BAA6B,CAAC,aAAa,+BAA+B,CAAC,aAAa,UAAU,CAAC,UAAU,6BAA6B,CAAC,QAAQ,2BAA2B,CAAC,QAAQ,2BAA2B,CAAC,WAAW,6BAA6B,CAAC,WAAW,QAAQ,CAAC,WAAW,QAAQ,CAAC,QAAQ,2BAA2B,CAAC,eAAe,QAAQ,CAAC,MAAM,UAAU,CAAC,MAAM,UAAU,CAAC,MAAM,UAAU,CAAC,SAAS,SAAS,CAAC,WAAW,WAAW,CAAC,WAAW,UAAU,CAAC,yBAAyB,WAAW,eAAe,CAAC,CAAC,yBAAyB,WAAW,eAAe,CAAC,CAAC,yBAAyB,WAAW,eAAe,CAAC,CAAC,0BAA0B,WAAW,gBAAgB,CAAC,CAAC,0BAA0B,WAAW,gBAAgB,CAAC,CAAC,0BAA0B,WAAW,gBAAgB,CAAC,CAAC,KAAK,6BAA6B,CAAC,OAAO,qCAAqC,CAAC,MAAM,oCAAoC,CAAC,SAAS,sCAAsC,CAAC,SAAS,kBAAkB,CAAC,SAAS,oCAAoC,CAAC,MAAM,mCAAmC,CAAC,OAAO,kCAAkC,CAAC,SAAS,kCAAkC,CAAC,MAAM,iCAAiC,CAAC,SAAS,mCAAmC,CAAC,MAAM,iCAAiC,CAAC,MAAM,iCAAiC,CAAC,MAAM,iCAAiC,CAAC,MAAM,iCAAiC,CAAC,MAAM,iCAAiC,CAAC,OAAO,kCAAkC,CAAC,SAAS,eAAe,CAAC,SAAS,oCAAoC,CAAC,MAAM,mCAAmC,CAAC,MAAM,mCAAmC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,OAAO,mCAAmC,CAAC,SAAS,mCAAmC,CAAC,MAAM,kCAAkC,CAAC,SAAS,gBAAgB,CAAC,OAAO,aAAa,CAAC,MAAM,YAAY,CAAC,MAAM,YAAY,CAAC,QAAQ,YAAY,CAAC,QAAQ,cAAc,CAAC,aAAa,mBAAmB,CAAC,OAAO,aAAa,CAAC,eAAe,cAAc,CAAC,cAAc,gCAAgC,CAAC,QAAQ,4BAA4B,CAAC,6BAA6B,CAAC,QAAQ,+BAA+B,CAAC,KAAK,6BAA6B,CAAC,QAAQ,+BAA+B,CAAC,KAAK,6BAA6B,CAAC,QAAQ,+BAA+B,CAAC,KAAK,6BAA6B,CAAC,KAAK,6BAA6B,CAAC,KAAK,6BAA6B,CAAC,KAAK,6BAA6B,CAAC,KAAK,6BAA6B,CAAC,KAAK,6BAA6B,CAAC,MAAM,8BAA8B,CAAC,MAAM,8BAA8B,CAAC,MAAM,8BAA8B,CAAC,MAAM,8BAA8B,CAAC,WAAW,UAAU,CAAC,aAAa,YAAY,CAAC,sDAAsD,mDAAmD,CAAC,4CAA4C,yCAAyC,CAAC,QAAQ,WAAW,CAAC,QAAQ,WAAW,CAAC,MAAM,UAAU,CAAC,UAAU,YAAY,CAAC,OAAO,aAAa,CAAC,UAAU,kCAAkC,CAAC,UAAU,kCAAkC,CAAC,iBAAiB,gBAAgB,CAAC,cAAc,gBAAgB,CAAC,SAAS,iCAAiC,CAAC,iBAAiB,gBAAgB,CAAC,gBAAgB,eAAe,CAAC,iBAAiB,gBAAgB,CAAC,cAAc,gBAAgB,CAAC,WAAW,iBAAiB,CAAC,KAAK,4BAA4B,CAAC,KAAK,4BAA4B,CAAC,KAAK,4BAA4B,CAAC,QAAQ,8BAA8B,CAAC,KAAK,4BAA4B,CAAC,QAAQ,8BAA8B,CAAC,QAAQ,SAAS,CAAC,KAAK,4BAA4B,CAAC,KAAK,4BAA4B,CAAC,KAAK,4BAA4B,CAAC,KAAK,4BAA4B,CAAC,KAAK,4BAA4B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,MAAM,6BAA6B,CAAC,2CAA2C,yCAAyC,CAAC,8BAA8B,4BAA4B,CAAC,uBAAuB,qBAAqB,CAAC,WAAW,SAAS,CAAC,aAAa,WAAW,CAAC,aAAa,WAAW,CAAC,aAAa,WAAW,CAAC,QAAQ,UAAU,CAAC,QAAQ,UAAU,CAAC,OAAO,sBAAgB,CAAhB,iBAAiB,CAAC,MAAM,SAAS,CAAC,WAAW,8BAA8B,CAAC,WAAW,8BAA8B,CAAC,UAAU,iCAAiC,CAAC,4BAA4B,0BAA0B,CAAC,UAAU,6BAA6B,CAAC,WAAW,0BAAoB,CAApB,qBAAqB,CAAC,UAAU,6BAA6B,CAAC,SAAS,gCAAgC,CAAC,SAAS,gCAAgC,CAAC,UAAU,iCAAiC,CAAC,gBAAgB,cAAc,CAAC,iBAAiB,eAAe,CAAC,+CAA+C,2CAA2C,CAAC,QAAQ,MAAM,CAAC,yBAAyB,aAAa,CAAC,MAAM,WAAW,CAAC,QAAQ,WAAW,CAAC,YAAY,eAAe,CAAC,gBAAgB,mBAAmB,CAAC,iBAAiB,wBAAwB,CAAC,mBAAmB,wCAA6F,CAAC,oCAArD,oDAAgJ,CAA3F,iBAAiB,qBAA0E,CAAC,uBAAuB,qBAA0E,CAAC,uCAArD,oDAA8I,CAAzF,gBAAgB,oBAAyE,CAAC,mBAAmB,wCAA6F,CAAC,0CAArD,oDAAsJ,CAAjG,uBAAuB,qBAA0E,CAAC,WAAW,YAAY,CAAC,WAAW,YAAY,CAAC,WAAW,qGAAqG,CAAC,eAAe,8BAA8B,CAAC,cAAc,6BAA6B,CAAC,gBAAgB,cAAc,CAAC,gBAAgB,cAAc,CAAC,YAAY,iBAAiB,CAAC,QAAQ,WAAW,CAAC,WAAW,oBAAoB,CAAC,aAAa,6CAA6C,CAAC,UAAU,qBAAqB,CAAC,kBAAkB,6BAA6B,CAAC,UAAU,kBAAkB,CAAC,WAAW,cAAc,CAAC,cAAc,kBAAkB,CAAC,WAAW,oBAAoB,CAAC,aAAa,sBAAsB,CAAC,eAAe,mBAAmB,CAAC,iBAAiB,6BAA6B,CAAC,gBAAgB,sBAAsB,CAAC,eAAe,0BAA0B,CAAC,OAAO,0BAA0B,CAAC,UAAU,4BAA4B,CAAC,OAAO,0BAA0B,CAAC,OAAO,0BAA0B,CAAC,OAAO,0BAA0B,CAAC,qCAAqC,sBAAsB,CAAC,yEAAyE,CAAC,iFAAiF,CAAC,qCAAqC,sBAAsB,CAAC,yEAAyE,CAAC,iFAAiF,CAAC,wCAAwC,sBAAsB,CAAC,2EAA2E,CAAC,mFAAmF,CAAC,qCAAqC,sBAAsB,CAAC,yEAAyE,CAAC,iFAAiF,CAAC,qCAAqC,sBAAsB,CAAC,yEAAyE,CAAC,iFAAiF,CAAC,qCAAqC,sBAAsB,CAAC,yEAAyE,CAAC,iFAAiF,CAAC,qCAAqC,sBAAsB,CAAC,yEAAyE,CAAC,iFAAiF,CAAC,qCAAqC,sBAAsB,CAAC,0EAA0E,CAAC,kFAAkF,CAAC,qCAAqC,sBAAsB,CAAC,0EAA0E,CAAC,kFAAkF,CAAC,qCAAqC,sBAAsB,CAAC,0EAA0E,CAAC,kFAAkF,CAAC,UAAU,sBAAsB,CAAC,kBAAkB,CAAC,eAAe,CAAC,eAAe,aAAa,CAAC,iBAAiB,eAAe,CAAC,mBAAmB,iBAAiB,CAAC,iBAAiB,eAAe,CAAC,SAAS,2BAA2B,CAAC,aAAa,+BAA+B,CAAC,aAAa,+BAA+B,CAAC,aAAa,+BAA+B,CAAC,iBAAiB,iBAAiB,CAAC,qBAAqB,qBAAqB,CAAC,cAAc,2BAA0B,CAAC,YAAY,8BAA8B,CAAC,YAAY,8BAA8B,CAAC,YAAY,8BAA8B,CAAC,YAAY,8BAA8B,CAAC,oBAAoB,2BAA2B,CAAC,4BAA4B,CAAC,eAAe,uCAAuC,CAAC,QAAQ,mCAAmC,CAAC,gBAAgB,CAAC,UAAU,mCAAmC,CAAC,cAAc,CAAC,UAAU,mCAAmC,CAAC,gBAAgB,CAAC,mBAAmB,mCAAmC,CAAC,kBAAkB,CAAC,UAAU,yCAAyC,CAAC,sBAAsB,CAAC,UAAU,uCAAuC,CAAC,oBAAoB,CAAC,UAAU,yCAAyC,CAAC,sBAAsB,CAAC,UAAU,0CAA0C,CAAC,uBAAuB,CAAC,YAAY,0CAA0C,CAAC,uBAAuB,CAAC,UAAU,wCAAwC,CAAC,qBAAqB,CAAC,eAAe,wBAAwB,CAAC,mBAAmB,CAAC,aAAa,sBAAsB,CAAC,iBAAiB,CAAC,qBAAqB,oBAAoB,CAAC,2BAA2B,2BAA2B,CAAC,mBAAmB,iEAAiE,CAAC,4CAA8C,mBAAmB,mEAAmE,CAAC,CAAC,oBAAoB,qCAAqC,CAAC,wBAAwB,sEAAsE,CAAC,4CAA8C,wBAAwB,wEAAwE,CAAC,CAAC,iBAAiB,kCAAkC,CAAC,cAAc,+BAA+B,CAAC,gBAAgB,iCAAiC,CAAC,gBAAgB,iCAAiC,CAAC,uBAAuB,wCAAwC,CAAC,kBAAkB,mCAAmC,CAAC,kBAAkB,mCAAmC,CAAC,sBAAsB,sBAAsB,CAAC,4CAA8C,sBAAsB,sEAAsE,CAAC,CAAC,oBAAoB,kBAAkB,CAAC,kBAAkB,kBAAkB,CAAC,4CAA8C,kBAAkB,kEAAkE,CAAC,CAAC,sBAAsB,sBAAsB,CAAC,sBAAsB,uBAAuB,CAAC,mBAAmB,2BAA2B,CAAC,WAAW,oCAAoC,CAAC,eAAe,wCAAwC,CAAC,cAAc,sBAAsB,CAAC,4CAA8C,cAAc,sEAAsE,CAAC,CAAC,WAAW,oCAAoC,CAAC,SAAS,kCAAkC,CAAC,gBAAgB,yCAAyC,CAAC,eAAe,wCAAwC,CAAC,YAAY,qCAAqC,CAAC,UAAU,mCAAmC,CAAC,cAAc,oEAAoE,CAAC,4CAA8C,cAAc,sEAAsE,CAAC,CAAC,kBAAkB,2CAA2C,CAAC,YAAY,qCAAqC,CAAC,YAAY,qCAAqC,CAAC,eAAe,qEAAqE,CAAC,4CAA8C,eAAe,uEAAuE,CAAC,CAAC,WAAW,oCAAoC,CAAC,cAAc,uCAAuC,CAAC,iBAAiB,uEAAuE,CAAC,4CAA8C,iBAAiB,yEAAyE,CAAC,CAAC,YAAY,qCAAqC,CAAC,mBAAmB,4CAA4C,CAAC,aAAa,sCAAsC,CAAC,gBAAgB,sBAAsB,CAAC,UAAU,mCAAmC,CAAC,cAAc,sBAAsB,CAAC,4CAA8C,cAAc,sEAAsE,CAAC,CAAC,cAAc,0BAA0B,CAAC,4CAA8C,cAAc,sEAAsE,CAAC,CAAC,mBAAmB,+CAA+C,CAAC,0DAA0D,CAAC,iBAAiB,uEAAuE,CAAC,4CAA8C,iBAAiB,yEAAyE,CAAC,CAAC,iBAAiB,wLAAwL,CAAC,eAAe,wCAAwC,CAAC,wLAAwL,CAAC,cAAc,uCAAuC,CAAC,qNAAqN,CAAC,gDAAgD,CAAC,iBAAiB,6BAA6B,CAAC,qNAAqN,CAAC,gDAAgD,CAAC,iBAAiB,uEAAuE,CAAC,4CAA8C,iBAAiB,yEAAyE,CAAC,CAAC,iBAAiB,wLAAwL,CAAC,cAAc,uCAAuC,CAAC,wLAAwL,CAAC,cAAc,iBAAiB,CAAC,KAAK,8BAA8B,CAAC,KAAK,8BAA8B,CAAC,QAAQ,gCAAgC,CAAC,KAAK,8BAA8B,CAAC,KAAK,8BAA8B,CAAC,KAAK,8BAA8B,CAAC,KAAK,8BAA8B,CAAC,KAAK,8BAA8B,CAAC,WAAW,WAAW,CAAC,MAAM,qCAAqC,CAAC,MAAM,qCAAqC,CAAC,SAAS,uCAAuC,CAAC,MAAM,qCAAqC,CAAC,MAAM,qCAAqC,CAAC,MAAM,qCAAqC,CAAC,MAAM,qCAAqC,CAAC,SAAS,qCAAqC,CAAC,MAAM,oCAAoC,CAAC,SAAS,sCAAsC,CAAC,MAAM,oCAAoC,CAAC,SAAS,sCAAsC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,MAAM,oCAAoC,CAAC,MAAM,kCAAkC,CAAC,MAAM,kCAAkC,CAAC,MAAM,kCAAkC,CAAC,MAAM,kCAAkC,CAAC,MAAM,kCAAkC,CAAC,MAAM,oCAAoC,CAAC,SAAS,sCAAsC,CAAC,MAAM,oCAAoC,CAAC,MAAM,qCAAqC,CAAC,MAAM,qCAAqC,CAAC,MAAM,qCAAqC,CAAC,SAAS,qCAAqC,CAAC,MAAM,mCAAmC,CAAC,MAAM,mCAAmC,CAAC,OAAO,oCAAoC,CAAC,aAAa,iBAAiB,CAAC,WAAW,eAAe,CAAC,cAAc,qBAAqB,CAAC,WAAW,4BAA4B,CAAC,UAAU,yBAAyB,CAAC,0DAA0D,CAAC,WAAW,0BAA0B,CAAC,2DAA2D,CAAC,SAAS,wBAAwB,CAAC,yDAAyD,CAAC,SAAS,wBAAwB,CAAC,yDAAyD,CAAC,SAAS,wBAAwB,CAAC,yDAAyD,CAAC,SAAS,wBAAwB,CAAC,yDAAyD,CAAC,kBAAkB,eAAe,CAAC,WAAW,mCAAmC,CAAC,kCAAkC,CAAC,cAAc,cAAc,CAAC,aAAa,CAAC,iBAAiB,mCAAmC,CAAC,kCAAkC,CAAC,eAAe,iCAAiC,CAAC,gCAAgC,CAAC,WAAW,wCAAwC,CAAC,mCAAmC,CAAC,aAAa,0CAA0C,CAAC,qCAAqC,CAAC,aAAa,0CAA0C,CAAC,qCAAqC,CAAC,eAAe,4CAA4C,CAAC,uCAAuC,CAAC,gBAAgB,mCAAmC,CAAC,oCAAoC,CAAC,gBAAgB,mCAAmC,CAAC,oCAAoC,CAAC,iBAAiB,oCAAoC,CAAC,qCAAqC,CAAC,aAAa,wBAAwB,CAAC,WAAW,oBAAoB,CAAC,mBAAmB,kBAAkB,CAAC,mBAAmB,aAAa,CAAC,wBAAwB,oCAAoC,CAAC,YAAY,wBAAwB,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,sBAAsB,kCAAkC,CAAC,cAAc,kBAAkB,CAAC,kBAAkB,8BAA8B,CAAC,6BAA6B,yCAAyC,CAAC,iBAAiB,6BAA6B,CAAC,qBAAqB,8DAA8D,CAAC,4CAA8C,qBAAqB,gEAAgE,CAAC,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,eAAe,2BAA2B,CAAC,gBAAgB,4BAA4B,CAAC,gBAAgB,4BAA4B,CAAC,uBAAuB,mCAAmC,CAAC,yBAAyB,qCAAqC,CAAC,cAAc,0BAA0B,CAAC,yBAAyB,qCAAqC,CAAC,cAAc,0BAA0B,CAAC,cAAc,0BAA0B,CAAC,cAAc,0BAA0B,CAAC,2BAA2B,uCAAuC,CAAC,yBAAyB,qCAAqC,CAAC,6BAA6B,sEAAsE,CAAC,4CAA8C,6BAA6B,wEAAwE,CAAC,CAAC,gBAAgB,4BAA4B,CAAC,gBAAgB,4BAA4B,CAAC,gBAAgB,4BAA4B,CAAC,gBAAgB,4BAA4B,CAAC,WAAW,wBAAwB,CAAC,cAAc,iCAAiC,CAAC,4IAA4I,CAAC,WAAW,8BAA8B,CAAC,oBAAoB,yBAAyB,CAAC,oBAAoB,yBAAyB,CAAC,WAAW,SAAS,CAAC,YAAY,UAAU,CAAC,YAAY,UAAU,CAAC,YAAY,UAAU,CAAC,YAAY,UAAU,CAAC,YAAY,8DAAgM,CAAC,iEAAlI,iIAAgY,CAA9P,qDAAqD,uEAAyM,CAAC,WAAW,6GAA+O,CAAC,sBAAlI,iIAA0X,CAAxP,WAAW,2GAA6O,CAAC,aAAa,qBAAuJ,CAAC,wBAAlI,iIAAuX,CAArP,WAAW,wGAA0O,CAAC,WAAW,8GAAgP,CAAC,mBAAlI,iIAAgY,CAA9P,QAAQ,oHAAsP,CAAC,QAAQ,oHAAoH,CAAC,iIAAiI,CAAC,qBAAqB,2BAA2B,CAAC,4CAA8C,qBAAqB,gIAAgI,CAAC,CAAC,WAAW,iCAAiC,CAAC,mBAAmB,yCAAyC,CAAC,wBAAwB,8CAA8C,CAAC,SAAS,qCAAqC,CAAC,iBAAiB,CAAC,UAAU,+BAA+B,CAAC,iLAAiL,CAAC,kBAAkB,uCAA+jB,CAAC,oCAAxhB,+QAA+Q,CAAC,uQAAy1B,CAAjlB,kBAAkB,uCAA+jB,CAAC,YAAY,6TAA6T,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,mCAAmC,oCAAoC,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,4BAA4B,8BAA8B,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,uCAAuC,wCAAwC,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,sBAAsB,yBAAyB,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,gBAAgB,uBAAuB,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,mBAAmB,6JAA6J,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,oBAAoB,2BAA2B,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,sBAAsB,oDAAoD,CAAC,mFAAmF,CAAC,yEAAyE,CAAC,YAAY,mBAAmB,CAAC,cAAc,iBAAiB,CAAC,uBAAuB,CAAC,cAAc,iBAAiB,CAAC,uBAAuB,CAAC,eAAe,gBAAgB,CAAC,sBAAsB,CAAC,aAAa,4BAA4B,CAAC,6CAA6C,CAAC,aAAa,gBAAgB,CAAC,iCAAiC,CAAC,UAAU,yBAAyB,CAAC,0CAA0C,CAAC,cAAc,uBAAuB,CAAC,kBAAkB,CAAC,aAAa,wBAAwB,CAAC,qBAAe,CAAf,gBAAgB,CAAC,yFAAyF,SAAS,CAAC,qBAAqB,gIAAgI,SAAS,CAAC,CAAC,8HAA8H,oCAAoC,CAAC,qFAAqF,kCAAkC,CAAC,sFAAsF,YAAY,CAAC,wFAAwF,sCAAsC,CAAC,uCAAuC,CAAC,0GAA0G,0BAA0B,CAAC,gJAAgJ,4CAA4C,CAAC,sJAAsJ,kDAAkD,CAAC,+FAA+F,eAAe,CAAC,qFAAqF,wCAAwC,CAAC,qFAAqF,wCAAwC,CAAC,yFAAyF,SAAS,CAAC,6IAA6I,0CAA0C,CAAC,4IAA4I,yCAAyC,CAAC,6FAA6F,4BAA4B,CAAC,uGAAuG,uCAAuC,CAAC,oDAAoD,CAAC,0EAA0E,6BAA6B,CAAC,0EAA0E,yCAAyC,CAAC,sBAAsB,CAAC,0EAA0E,2BAA2B,CAAC,8EAA8E,aAAa,CAAC,4EAA4E,wCAAwC,CAAC,qBAAqB,CAAC,8EAA8E,aAAa,CAAC,0FAA0F,8BAA8B,CAAC,sFAAsF,mCAAmC,CAAC,gBAAgB,CAAC,qGAAqG,wCAAwC,CAAC,sFAAsF,wGAAwG,CAAC,iIAAiI,CAAC,4EAA4E,gEAAgE,CAAC,4CAA8C,4EAA4E,kEAAkE,CAAC,CAAC,wEAAwE,0BAA0B,CAAC,wDAAwD,mCAAmC,CAAC,0DAA0D,qCAAqC,CAAC,qEAAqE,mCAAmC,CAAC,uEAAuE,qCAAqC,CAAC,iEAAiE,gCAAgC,CAAC,iEAAiE,wCAAwC,CAAC,mEAAmE,6BAA6B,CAAC,6DAA6D,6GAA6G,CAAC,iIAAiI,CAAC,qBAAqB,gGAAgG,4CAA4C,CAAC,CAAC,gEAAgE,kBAAkB,CAAC,wDAAwD,UAAU,CAAC,4HAA4H,4CAA4C,CAAC,wGAAwG,4BAA4B,CAAC,8FAA8F,4BAA4B,CAAC,2FAA2F,0BAA0B,CAAC,oHAAoH,8BAA8B,CAAC,sCAAsC,mCAAmC,CAAC,cAAc,CAAC,4CAA4C,sBAAsB,CAAC,qCAAqC,wBAAwB,CAAC,yDAAyD,CAAC,yCAAyC,0CAA0C,CAAC,qCAAqC,CAAC,6CAA6C,6BAA6B,CAAC,sDAAiD,mCAAmC,CAApF,iDAAiD,mCAAmC,CAAC,uBAAuB,yBAAyB,CAAC,iBAAiB,CAAC,uBAAuB,yBAAyB,CAAC,6BAA6B,CAAC,wBAAwB,yBAAyB,CAAC,kCAAkC,CAAC,wBAAwB,yBAAyB,CAAC,QAAQ,CAAC,kBAAkB,yBAAyB,CAAC,4BAA4B,CAAC,wBAAwB,yBAAyB,CAAC,SAAS,CAAC,gCAAgC,yBAAyB,CAAC,wCAAwC,CAAC,oDAAoD,CAAC,gHAAgH,yBAAyB,CAAC,SAAS,CAAC,iCAAiC,uCAAuC,CAAC,0CAA0C,CAAC,6BAA6B,wCAAwC,CAAC,qBAAqB,CAAC,+BAA+B,wCAAwC,CAAC,2CAA2C,CAAC,qCAAqC,iBAAiB,CAAC,iCAAiC,UAAU,CAAC,qBAAqB,mCAAmC,qBAAqB,CAAC,oDAAoD,CAAC,6BAA6B,cAAc,CAAC,sCAAsC,kDAAkD,CAAC,sCAAsC,kDAAkD,CAAC,sCAAsC,mDAAmD,CAAC,sCAAsC,oDAAoD,CAAC,sCAAsC,+CAA+C,CAAC,8BAA8B,kCAAkC,CAAC,2BAA2B,+CAA+C,CAAC,8BAA8B,wBAAwB,CAAC,8BAA8B,wBAAwB,CAAC,8BAA8B,wBAAwB,CAAC,8BAA8B,wBAAwB,CAAC,wBAAwB,oCAAoC,CAAC,iCAAiC,0EAA0E,CAAC,4CAA8C,iCAAiC,4EAA4E,CAAC,CAAC,iCAAiC,0EAA0E,CAAC,4CAA8C,iCAAiC,4EAA4E,CAAC,CAAC,yBAAyB,qCAAqC,CAAC,uBAAuB,mCAAmC,CAAC,2BAA2B,oEAAoE,CAAC,4CAA8C,2BAA2B,sEAAsE,CAAC,CAAC,+BAA+B,2CAA2C,CAAC,yBAAyB,qCAAqC,CAAC,6BAA6B,sEAAsE,CAAC,4CAA8C,6BAA6B,wEAAwE,CAAC,CAAC,6BAA6B,sEAAsE,CAAC,4CAA8C,6BAA6B,wEAAwE,CAAC,CAAC,2BAA2B,uCAAuC,CAAC,+BAA+B,wEAAwE,CAAC,4CAA8C,+BAA+B,0EAA0E,CAAC,CAAC,gCAAgC,4CAA4C,CAAC,uBAAuB,mCAAmC,CAAC,8BAA8B,qCAAqC,CAAC,8BAA8B,qCAAqC,CAAC,gCAAgC,aAAa,CAAC,gCAAgC,aAAa,CAAC,gCAAgC,aAAa,CAAC,gCAAgC,aAAa,CAAC,qCAAqC,oCAAoC,CAAC,4BAA4B,2BAA2B,CAAC,8BAA8B,6BAA6B,CAAC,4BAA4B,2BAA2B,CAAC,oCAAoC,mCAAmC,CAAC,sCAAsC,qCAAqC,CAAC,6CAA6C,4CAA4C,CAAC,6BAA6B,4BAA4B,CAAC,wBAAwB,8BAA8B,CAAC,0BAA0B,SAAS,CAAC,kEAAkE,uEAAyM,CAAC,0FAAlI,iIAAyY,CAAvQ,wBAAwB,6GAA+O,CAAC,wBAAwB,2GAA2G,CAAC,iIAAiI,CAAC,sCAAsC,2BAA2B,CAAC,4CAA8C,sCAAsC,+GAA+G,CAAC,CAAC,iHAAiH,qCAAqC,CAAC,+FAA+F,sEAAsE,CAAC,4CAA8C,+FAA+F,wEAAwE,CAAC,CAAC,uFAAuF,yCAAyC,CAAC,oGAAoG,yCAAyC,CAAC,oFAAoF,yBAAyB,CAAC,6CAA6C,yBAAyB,CAAC,4CAA4C,CAAC,CAAC,wBAAwB,oCAAoC,CAAC,yBAAyB,qCAAqC,CAAC,qCAAqC,oCAAoC,CAAC,sCAAsC,qCAAqC,CAAC,0BAA0B,SAAS,CAAC,qBAAqB,oHAAoH,CAAC,iIAAiI,CAAC,wBAAwB,iCAAiC,CAAC,4BAA4B,0BAA0B,CAAC,yGAAyG,CAAC,2BAA2B,uBAAuB,CAAC,kBAAkB,CAAC,yFAAyF,wCAAwC,CAAC,qFAAqF,oCAAoC,CAAC,4FAA4F,2CAA2C,CAAC,qCAAqC,oHAAoH,CAAC,iIAAiI,CAAC,qCAAqC,oHAAoH,CAAC,iIAAiI,CAAC,wCAAwC,iCAAiC,CAAC,gDAAgD,yCAAyC,CAAC,4CAA4C,0BAA0B,CAAC,yGAAyG,CAAC,4CAA4C,0BAA0B,CAAC,yGAAyG,CAAC,qDAAqD,8CAA8C,CAAC,6CAA6C,uBAAuB,CAAC,kBAAkB,CAAC,8BAA8B,6CAA6C,kBAAkB,CAAC,uBAAuB,CAAC,CAAC,2CAA2C,uBAAuB,CAAC,kBAAkB,CAAC,oCAAoC,oBAAoB,CAAC,oDAAoD,CAAC,kCAAkC,4CAA4C,CAAC,+CAA+C,4CAA4C,CAAC,0BAA0B,wGAAwG,CAAC,iIAAiI,CAAC,wCAAwC,mBAAmB,CAAC,uCAAuC,kBAAkB,CAAC,8EAA8E,UAAU,CAAC,uEAAuE,qCAAqC,CAAC,wDAAwD,mBAAmB,CAAC,+CAA+C,UAAU,CAAC,8CAA8C,oCAAoC,CAAC,kDAAkD,qEAAqE,CAAC,4CAA8C,kDAAkD,uEAAuE,CAAC,CAAC,2DAA2D,oCAAoC,CAAC,0DAA0D,mCAAmC,CAAC,+CAA+C,UAAU,CAAC,gDAAgD,SAAS,CAAC,6CAA6C,qEAAqE,CAAC,4CAA8C,6CAA6C,uEAAuE,CAAC,CAAC,8DAA8D,uDAAuD,CAAC,4DAA4D,4CAA4C,CAAC,sDAAsD,0CAA0C,CAAC,qCAAqC,CAAC,wDAAwD,4CAA4C,CAAC,uCAAuC,CAAC,4DAA4D,aAAa,CAAC,yEAAyE,4CAA4C,CAAC,uDAAuD,mBAAmB,CAAC,8CAA8C,UAAU,CAAC,kEAAkE,mBAAmB,CAAC,yDAAyD,UAAU,CAAC,qFAAqF,UAAU,CAAC,uFAAuF,UAAU,CAAC,yFAAyF,qBAAqB,CAAC,oGAAoG,yBAAyB,CAAC,2BAA2B,CAAC,iGAAiG,yBAAyB,CAAC,6BAA6B,CAAC,oGAAoG,yBAAyB,CAAC,UAAU,CAAC,2GAA2G,yBAAyB,CAAC,uCAAuC,CAAC,oDAAoD,CAAC,+GAA+G,yBAAyB,CAAC,wCAAwC,CAAC,oDAAoD,CAAC,4DAA4D,oCAAoC,CAAC,qEAAqE,oCAAoC,CAAC,wDAAwD,uCAAuC,CAAC,oDAAoD,CAAC,qDAAqD,wCAAwC,CAAC,oDAAoD,CAAC,sDAAsD,uCAAuC,CAAC,oDAAoD,CAAC,mDAAmD,wCAAwC,CAAC,oDAAoD,CAAC,0DAA0D,wCAAwC,CAAC,4DAA4D,6BAA6B,CAAC,sDAAsD,wGAAwG,CAAC,iIAAiI,CAAC,4DAA4D,uCAAuC,CAAC,oDAAoD,CAAC,yDAAyD,qCAAqC,CAAC,sEAAsE,qCAAqC,CAAC,iEAAiE,qCAAqC,CAAC,yDAAyD,iBAAiB,CAAC,uBAAuB,CAAC,8CAA8C,oCAAoC,CAAC,2DAA2D,oCAAoC,CAAC,+DAA+D,uCAAuC,CAAC,kDAAkD,oCAAoC,CAAC,sDAAsD,qEAAqE,CAAC,4CAA8C,sDAAsD,uEAAuE,CAAC,CAAC,qDAAqD,uCAAuC,CAAC,0DAA0D,4CAA4C,CAAC,+DAA+D,oCAAoC,CAAC,8DAA8D,mCAAmC,CAAC,uEAAuE,4CAA4C,CAAC,oDAAoD,SAAS,CAAC,qDAAqD,iBAAiB,CAAC,uBAAuB,CAAC,qBAAqB,uEAAuE,4CAA4C,CAAC,oFAAoF,4CAA4C,CAAC,CAAC,yDAAyD,mCAAmC,CAAC,gEAAgE,uCAAuC,CAAC,oDAAoD,CAAC,2DAA2D,mCAAmC,CAAC,0DAA0D,uCAAuC,CAAC,oDAAoD,CAAC,uFAAuF,+CAA+C,CAAC,oDAAoD,CAAC,0FAA0F,gDAAgD,CAAC,oDAAoD,CAAC,wDAAwD,wBAAwB,CAAC,yBAAyB,cAAc,QAAQ,CAAC,aAAa,4BAA4B,CAAC,cAAc,6BAA6B,CAAC,UAAU,iCAAiC,CAAC,UAAU,iCAAiC,CAAC,UAAU,iCAAiC,CAAC,UAAU,oCAAoC,CAAC,WAAW,qCAAqC,CAAC,WAAW,qCAAqC,CAAC,UAAU,YAAY,CAAC,UAAU,8BAA8B,CAAC,iBAAiB,YAAY,CAAC,UAAU,6BAA6B,CAAC,iBAAiB,WAAW,CAAC,YAAY,UAAU,CAAC,cAAc,6BAA6B,CAAC,iBAAiB,6CAA6C,CAAC,cAAc,qBAAqB,CAAC,cAAc,kBAAkB,CAAC,iBAAiB,wBAAwB,CAAC,cAAc,4BAA4B,CAAC,WAAW,0BAA0B,CAAC,yCAAyC,sBAAsB,CAAC,yEAAyE,CAAC,iFAAiF,CAAC,yCAAyC,sBAAsB,CAAC,yEAAyE,CAAC,iFAAiF,CAAC,yCAAyC,sBAAsB,CAAC,0EAA0E,CAAC,kFAAkF,CAAC,yCAAyC,sBAAsB,CAAC,0EAA0E,CAAC,kFAAkF,CAAC,gBAAgB,8BAA8B,CAAC,SAAS,8BAA8B,CAAC,SAAS,8BAA8B,CAAC,UAAU,qCAAqC,CAAC,UAAU,qCAAqC,CAAC,UAAU,oCAAoC,CAAC,WAAW,qCAAqC,CAAC,UAAU,kCAAkC,CAAC,UAAU,kCAAkC,CAAC,eAAe,eAAe,CAAC,cAAc,yBAAyB,CAAC,0DAA0D,CAAC,cAAc,yBAAyB,CAAC,0DAA0D,CAAC,eAAe,0BAA0B,CAAC,2DAA2D,CAAC,aAAa,wBAAwB,CAAC,yDAAyD,CAAC,aAAa,wBAAwB,CAAC,yDAAyD,CAAC,eAAe,mCAAmC,CAAC,kCAAkC,CAAC,CAAC,yBAAyB,cAAc,iBAAiB,CAAC,yDAAyD,iDAAiD,CAAC,YAAY,UAAU,CAAC,qBAAqB,eAAe,CAAC,aAAa,wBAAwB,CAAC,yDAAyD,CAAC,eAAe,SAAS,CAAC,+EAA+E,6BAA6B,CAAC,gFAAgF,kCAAkC,CAAC,sFAAsF,8BAA8B,CAAC,kFAAkF,wGAAwG,CAAC,iIAAiI,CAAC,0JAA0J,kCAAkC,CAAC,CAAC,yBAAyB,yBAAyB,CAAC,yBAAyB,yBAAyB,YAAY,CAAC,CAAC,0BAA0B,WAAW,aAAa,CAAC,UAAU,YAAY,CAAC,YAAY,YAAY,CAAC,iBAAiB,6CAA6C,CAAC,SAAS,8BAA8B,CAAC,UAAU,qCAAqC,CAAC,cAAc,yBAAyB,CAAC,0DAA0D,CAAC,cAAc,yBAAyB,CAAC,0DAA0D,CAAC,CAAC,0BAA0B,2BAA2B,qBAAqB,CAAC,0IAA0I,mCAAmC,CAAC,yIAAyI,kCAAkC,CAAC,CAAC,mCAAmC,0BAA0B,qCAAqC,CAAC,CAAC,sGAAsG,kCAAkC,CAAC,2HAA2H,2DAA2D,CAAC,4CAA8C,2HAA2H,6DAA6D,CAAC,CAAC,yGAAyG,0BAA0B,CAAC,4FAA4F,YAAY,CAAC,uDAAuD,uBAAuB,CAAC,kBAAkB,CAAC,uGAAuG,0BAA0B,CAAC,iNAAiN,uBAAuB,CAAC,+GAA+G,0BAA0B,CAAC,yDAAyD,uBAAuB,CAAC,kBAAkB,CAAC,kGAAkG,YAAY,CAAC,2DAA2D,uBAAuB,CAAC,kBAAkB,CAAC,0DAA0D,qCAAqC,CAAC,6DAA6D,sCAAsC,CAAC,6DAA6D,wBAAwB,CAAC,yDAAyD,CAAC,iEAAiE,0CAA0C,CAAC,qCAAqC,CAAC,2EAA2E,mCAAmC,CAAC,0CAA0C,qCAAqC,CAAC,yGAAyG,kCAAkC,CAAC,iEAAiE,6BAA6B,CAAC,iEAAiE,4BAA4B,CAAC,0CAA0C,8BAA8B,CAAC,wCAAwC,qCAAqC,CAAC,wCAAwC,oCAAoC,CAAC,+CAA+C,6BAA6B,CAAC,+CAA+C,4BAA4B,CAAC,6BAA6B,mCAAmC,CAAC,kCAAkC,CAAC,qCAAqC,mBAAmB,CAAC,wBAAwB,4BAA4B,CAAC,6BAA6B,CAAC,0BAA0B,aAAa,CAAC,wBAAwB,0CAA0C,CAAC,uBAAuB,CAAC,+CAA+C,mCAAmC,CAAC,cAAc,CAAC,kEAAkE,oCAAoC,CAAC,wFAAwF,uCAAuC,CAAC,0CAA0C,CAAC,sFAAsF,wCAAwC,CAAC,2CAA2C,CAAC,+FAA+F,qEAAqE,CAAC,4CAA8C,+FAA+F,uEAAuE,CAAC,CAAC,kGAAkG,wCAAwC,CAAC,2CAA2C,CAAC,8DAA8D,oCAAoC,CAAC,+BAA+B,YAAY,CAAC,iCAAiC,oBAAoB,CAAC,2BAA2B,CAAC,mBAAmB,CAAC,eAAe,CAAC,oDAAoD,sBAAsB,CAAC,kBAAkB,CAAC,eAAe,CAAC,2BAA2B,iBAAiB,CAAC,wBAAwB,0BAA0B,CAAC,yBAAyB,2BAA2B,CAAC,4BAA4B,8BAA8B,CAAC,+BAA+B,CAAC,yBAAyB,4BAA4B,CAAC,6BAA6B,CAAC,yBAAyB,+BAA+B,CAAC,sBAAsB,6BAA6B,CAAC,yBAAyB,8BAA8B,CAAC,sBAAsB,4BAA4B,CAAC,2BAA2B,aAAa,CAAC,mCAAmC,8BAA8B,CAAC,kCAAkC,6BAA6B,CAAC,wCAAwC,mCAAmC,CAAC,iDAAiD,4CAA4C,CAAC,gDAAgD,qBAAqB,CAAC,oDAAoD,CAAC,6BAA6B,mCAAmC,CAAC,4CAA4C,0CAA0C,CAAC,qBAAqB,CAAC,yGAAyG,YAAY,CAAC,kEAAkE,aAAa,CAAC,8DAA8D,eAAe,CAAC,kHAAkH,6BAA6B,CAAC,6KAA6K,eAAe,CAAC,mHAAmH,4BAA4B,CAAC,+GAA+G,eAAe,CAAC,CAAC,MAAM,wBAAwB,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,qBAAqB,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,sBAAsB,CAAC,gCAAgC,CAAC,iBAAiB,CAAC,gCAAgC,CAAC,mBAAmB,CAAC,8BAA8B,CAAC,mBAAmB,CAAC,4BAA4B,CAAC,uBAAuB,CAAC,oCAAoC,CAAC,cAAc,CAAC,mBAAmB,CAAC,eAAe,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,8BAA8B,CAAC,0BAA0B,CAAC,wCAAwC,CAAC,2BAA2B,CAAC,oCAAoC,CAAC,4BAA4B,CAAC,uBAAuB,CAAC,oCAAoC,CAAC,mCAAmC,CAAC,4DAA4D,CAAC,yDAAyD,CAAC,4DAA4D,CAAC,4DAA4D,CAAC,6DAA6D,CAAC,oCAAoC,CAAC,0BAA0B,GAAG,QAAQ,CAAC,GAAG,4CAA4C,CAAC,CAAC,wBAAwB,GAAG,4CAA4C,CAAC,GAAG,QAAQ,CAAC,CAAC,mBAAmB,GAAG,SAAS,CAAC,0BAA0B,CAAC,GAAG,SAAS,CAAC,uBAAuB,CAAC,CAAC,oBAAoB,GAAG,SAAS,CAAC,0BAA0B,CAAC,GAAG,SAAS,CAAC,sBAAsB,CAAC,CAAC,gBAAgB,MAAM,6BAA6B,CAAC,IAAI,6BAA6B,CAAC,CAAC,iBAAiB,MAAM,uBAAuB,CAAC,IAAI,0BAA0B,CAAC,CAAC,kBAAkB,GAAG,sBAAsB,CAAC,GAAG,yBAAyB,CAAC,CAAC,gBAAgB,oCAAoC,CAAC,sBAAsB,2BAA2B,CAAC,sBAAsB,iBAAiB,CAAC,kBAAkB,CAAC,yBAAyB,sBAAsB,mBAAmB,CAAC,oBAAoB,CAAC,CAAC,0BAA0B,sBAAsB,iBAAiB,CAAC,kBAAkB,CAAC,CAAC,iBAAiB,QAAQ,CAAC,yBAAyB,iBAAiB,UAAU,CAAC,CAAC,0BAA0B,iBAAiB,QAAQ,CAAC,CAAC,oBAAoB,gBAAgB,CAAC,gBAAgB,CAAC,yBAAyB,oBAAoB,iBAAiB,CAAC,mBAAmB,CAAC,CAAC,oBAAoB,iBAAiB,CAAC,mBAAmB,CAAC,yBAAyB,oBAAoB,cAAc,CAAC,kBAAkB,CAAC,CAAC,sBAAsB,cAAc,CAAC,kBAAkB,CAAC,yBAAyB,sBAAsB,kBAAkB,CAAC,mBAAmB,CAAC,CAAC,oBAAoB,kBAAkB,CAAC,mBAAmB,CAAC,yBAAyB,oBAAoB,iBAAiB,CAAC,mBAAmB,CAAC,CAAC,0BAA0B,oBAAoB,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,oBAAoB,iBAAiB,CAAC,mBAAmB,CAAC,yBAAyB,oBAAoB,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,0BAA0B,oBAAoB,kBAAkB,CAAC,mBAAmB,CAAC,CAAC,qBAAqB,gBAAgB,CAAC,gBAAgB,CAAC,yBAAyB,qBAAqB,kBAAkB,CAAC,mBAAmB,CAAC,CAAC,0BAA0B,qBAAqB,iBAAiB,CAAC,kBAAkB,CAAC,CAAC,qBAAqB,kBAAkB,CAAC,mBAAmB,CAAC,yBAAyB,qBAAqB,iBAAiB,CAAC,kBAAkB,CAAC,CAAC,0BAA0B,qBAAqB,cAAc,CAAC,aAAa,CAAC,CAAC,sBAAsB,eAAe,CAAC,yBAAyB,sBAAsB,iBAAiB,CAAC,CAAC,0BAA0B,sBAAsB,eAAe,CAAC,CAAC,cAAc,YAAY,CAAC,yBAAyB,cAAc,cAAc,CAAC,CAAC,0BAA0B,cAAc,YAAY,CAAC,CAAC,eAAe,iBAAiB,CAAC,kBAAkB,CAAC,yBAAyB,eAAe,mBAAmB,CAAC,oBAAoB,CAAC,CAAC,0BAA0B,eAAe,iBAAiB,CAAC,kBAAkB,CAAC,CAAC,eAAe,gBAAgB,CAAC,mBAAmB,CAAC,yBAAyB,eAAe,kBAAkB,CAAC,qBAAqB,CAAC,CAAC,0BAA0B,eAAe,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,iBAAiB,YAAY,CAAC,yBAAyB,iBAAiB,cAAc,CAAC,CAAC,0BAA0B,iBAAiB,YAAY,CAAC,CAAC,iBAAiB,qBAAqB,CAAC,YAAY,CAAC,yBAAyB,iBAAiB,kBAAkB,CAAC,CAAC,eAAe,YAAY,CAAC,yBAAyB,eAAe,aAAa,CAAC,CAAC,aAAa,aAAa,CAAC,yBAAyB,aAAa,YAAY,CAAC,CAAC,gBAAgB,kBAAkB,CAAC,yBAAyB,gBAAgB,qBAAqB,CAAC,CAAC,kBAAkB,UAAU,CAAC,oBAAoB,CAAC,yBAAyB,kBAAkB,mBAAmB,CAAC,CAAC,mBAAwI,iHAA0C,CAAC,kBAAkB,iBAAiB,CAAC,kBAAkB,CAAC,eAAe,CAAC,yBAAyB,kBAAkB,aAAa,CAAC,cAAc,CAAC,CAAC,wBAAwB,cAAc,CAAC,kBAAkB,UAAU,CAAC,eAAe,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,yBAAyB,kBAAkB,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,oBAAoB,UAAU,CAAC,yBAAyB,oBAAoB,WAAW,CAAC,CAAC,0BAA0B,oBAAoB,WAAW,CAAC,CAAC,mBAAmB,UAAU,CAAC,gBAAgB,CAAC,YAAY,CAAC,eAAe,CAAC,sBAAsB,qBAAqB,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,YAAY,CAAC,iDAAiD,MAAM,gCAAgC,CAAC,CAAC,0BAA0B,MAAM,gCAAgC,CAAC,CAAC,qBAAqB,eAAe,CAAC,yBAAyB,qBAAqB,iBAAiB,CAAC,CAAC,6BAA6B,qBAAqB,CAAC,YAAY,CAAC,iCAAiC,gBAAgB,CAAC,yBAAyB,6BAA6B,kBAAkB,CAAC,kBAAkB,CAAC,iCAAiC,YAAY,CAAC,gBAAgB,CAAC,CAAC,uBAAuB,iBAAiB,CAAC,eAAe,CAAC,yBAAyB,uBAAuB,aAAa,CAAC,UAAU,CAAC,CAAC,yEAAyE,UAAU,CAAC,yBAAyB,yEAAyE,MAAM,CAAC,CAAC,gBAAgB,qBAAqB,CAAC,YAAY,CAAC,oBAAoB,gBAAgB,CAAC,yBAAyB,gBAAgB,kBAAkB,CAAC,oBAAoB,YAAY,CAAC,gBAAgB,CAAC,CAAC,kBAAkB,6CAA6C,CAAC,QAAQ,CAAC,YAAY,CAAC,yBAAyB,kBAAkB,6CAA6C,CAAC,UAAU,CAAC,CAAC,0BAA0B,kBAAkB,6CAA6C,CAAC,CAAC,0BAA0B,kBAAkB,6CAA6C,CAAC,CAAC,sBAAsB,qBAAqB,CAAC,gBAAgB,CAAC,YAAY,CAAC,0BAA0B,sBAAsB,kBAAkB,CAAC,CAAC,mBAAmB,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,yBAAyB,mBAAmB,cAAc,CAAC,CAAC,0BAA0B,mBAAmB,YAAY,CAAC,CAAC,sBAAsB,gBAAgB,CAAC,eAAe,CAAC,gBAAgB,CAAC,yBAAyB,sBAAsB,kBAAkB,CAAC,mBAAmB,CAAC,CAAC,0BAA0B,sBAAsB,iBAAiB,CAAC,kBAAkB,CAAC,CAAC,sBAAsB,iBAAiB,CAAC,eAAe,CAAC,mBAAmB,CAAC,yBAAyB,sBAAsB,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,0BAA0B,sBAAsB,kBAAkB,CAAC,mBAAmB,CAAC,CAAC,sBAAsB,kBAAkB,CAAC,eAAe,CAAC,mBAAmB,CAAC,yBAAyB,sBAAsB,iBAAiB,CAAC,mBAAmB,CAAC,CAAC,0BAA0B,sBAAsB,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,yBAAyB,qBAAqB,CAAC,YAAY,CAAC,6BAA6B,gBAAgB,CAAC,yBAAyB,yBAAyB,kBAAkB,CAAC,6BAA6B,YAAY,CAAC,iBAAiB,CAAC,CAAC,kBAAkB,mBAAgB,CAAhB,gBAAgB,CAAC,UAAU,CAAC,WAAW,CAAC,qBAAqB,gBAAgB,CAAC,yBAAyB,qBAAqB,iBAAiB,CAAC,CAAC,sBAAsB,qBAAqB,CAAC,cAAc,CAAC,YAAY,CAAC,yBAAyB,sBAAsB,kBAAkB,CAAC,6BAA6B,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,yBAAyB,WAAW,iBAAiB,CAAC,kBAAkB,CAAC,gBAAgB,qBAAqB,CAAC,YAAY,CAAC,oBAAoB,gBAAgB,CAAC,iBAAiB,UAAU,CAAC,gBAAgB,iBAAiB,CAAC,mBAAmB,CAAC,gBAAgB,YAAY,CAAC,eAAe,aAAa,CAAC,CAAC,gDAAgD,aAAa,6CAA6C,CAAC,gBAAgB,WAAW,CAAC,CAAC,0BAA0B,qBAAqB,wBAAwB,CAAC,qBAAqB,CAAC,cAAc,6CAA6C,CAAC,CAAC,2BAA2B,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,2BAA2B,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,2BAA2B,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,wBAAwB,UAAU,CAAC,cAAc,CAAC,wBAAwB,UAAU,CAAC,cAAc,CAAC,wBAAwB,UAAU,CAAC,cAAc,CAAC,sBAAsB,UAAU,CAAC,cAAc,CAAC,sBAAsB,UAAU,CAAC,cAAc,CAAC,+BAA+B,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,+BAA+B,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,4BAA4B,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAAC,iCAAiC,UAAU,CAAC,cAAc,CAAC,6BAA6B,gBAAgB,CAAC,cAAc,CAAC,mBAAmB,CAAC,4BAA4B,gBAAgB,CAAC,cAAc,CAAC,mBAAmB,CAAC,2BAA2B,gBAAgB,CAAC,cAAc,CAAC,mBAAmB,CAAC,8BAA8B,UAAU,CAAC,cAAc,CAAC,kCAAkC,UAAU,CAAC,cAAc,CAAC,sCAAsC,4BAA4B,CAAC,cAAc,CAAC,eAAgB,CAAC,qCAAqC,4BAA4B,CAAC,cAAc,CAAC,iBAAiB,CAAC,oCAAoC,4BAA4B,CAAC,cAAc,CAAC,kBAAkB,CAAC,uBAAuB,UAAU,CAAC,cAAc,CAAC,2BAA2B,UAAU,CAAC,cAAc,CAAC,wBAAwB,UAAU,CAAC,cAAc,CAAC,uBAAuB,UAAU,CAAC,cAAc,CAAC,4BAA4B,UAAU,CAAC,cAAc,CAAC,8BAA8B,UAAU,CAAC,cAAc,CAAC,+BAA+B,UAAU,CAAC,cAAc,CAAC,gCAAgC,UAAU,CAAC,cAAc,CAAC,sBAAsB,UAAU,CAAC,cAAc,CAAC,uBAAuB,CAAC,4BAA4B,UAAU,CAAC,cAAc,CAAC,4BAA4B,qBAAqB,CAAC,cAAc,CAAC,kBAAkB,CAAC,4BAA4B,UAAU,CAAC,cAAc,CAAC,uBAAuB,CAAC,kCAAkC,UAAU,CAAC,cAAc,CAAC,kCAAkC,qBAAqB,CAAC,cAAc,CAAC,kBAAkB,CAAC,0BAA0B,UAAU,CAAC,cAAc,CAAC,2BAA2B,UAAU,CAAC,cAAc,CAAC,uBAAuB,CAAC,gCAAgC,UAAU,CAAC,cAAc,CAAC,iCAAiC,UAAU,CAAC,cAAc,CAAC,uBAAuB,CAAC,0BAA0B,UAAU,CAAC,cAAc,CAAC,iCAAiC,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC,iCAAiC,UAAU,CAAC,cAAc,CAAC,kBAAkB,CAAC,kCAAkC,UAAU,CAAC,cAAc,CAAC,uBAAuB,CAAC,6BAA6B,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAAC,oBAAoB,UAAU,CAAC,cAAc,CAAC,0BAA0B,UAAU,CAAC,cAAc,CAAC,wBAAwB,UAAU,CAAC,cAAc,CAAC,yBAAyB,UAAU,CAAC,cAAc,CAAC,0BAA0B,UAAU,CAAC,cAAc,CAAC,sBAAsB,UAAU,CAAC,cAAc,CAAC,uBAAuB,UAAU,CAAC,cAAc,CAAC,wBAAwB,UAAU,CAAC,cAAc,CAAC,qBAAqB,UAAU,CAAC,cAAc,CAAC,2BAA2B,UAAU,CAAC,cAAc,CAAC,iCAAiC,UAAU,CAAC,cAAc,CAAC,iCAAiC,qBAAqB,CAAC,cAAc,CAAC,kBAAkB,CAAC,gCAAgC,UAAU,CAAC,cAAc,CAAC,6BAA6B,UAAU,CAAC,cAAc,CAAC,mCAAmC,UAAU,CAAC,cAAc,CAAC,iCAAiC,UAAU,CAAC,cAAc,CAAC,kCAAkC,UAAU,CAAC,cAAc,CAAC,mCAAmC,UAAU,CAAC,cAAc,CAAC,+BAA+B,UAAU,CAAC,cAAc,CAAC,gCAAgC,UAAU,CAAC,cAAc,CAAC,iCAAiC,UAAU,CAAC,cAAc,CAAC,8BAA8B,UAAU,CAAC,cAAc,CAAC,wBAAwB,UAAU,CAAC,cAAc,CAAC,oBAAoB,UAAU,CAAC,cAAc,CAAC,uBAAuB,UAAU,CAAC,cAAc,CAAC,gBAAgB,CAAC,gBAAgB,GAAG,uBAAwB,CAAC,CAAC,iBAAiB,IAAI,UAAU,CAAC", "file": "static/css/c41dbb6f8d248dcf.css", "sourcesContent": ["/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/ba9851c3c22cd980-s.woff2) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/21350d82a1f187e9-s.woff2) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/c5fe6dc8356a8c31-s.woff2) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/19cfc7226ec3afaa-s.woff2) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n", null, "/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/ba9851c3c22cd980-s.woff2) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/21350d82a1f187e9-s.woff2) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/c5fe6dc8356a8c31-s.woff2) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/19cfc7226ec3afaa-s.woff2) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}@font-face {font-family: 'Inter Fallback';src: local(\"Arial\");ascent-override: 90.44%;descent-override: 22.52%;line-gap-override: 0.00%;size-adjust: 107.12%\n}.__className_f367f3 {font-family: 'Inter', 'Inter Fallback';font-style: normal\n}.__variable_f367f3 {--font-inter: 'Inter', 'Inter Fallback'\n}\n\n/* cyrillic-ext */\n@font-face {\n  font-family: 'JetBrains Mono';\n  font-style: normal;\n  font-weight: 100 800;\n  font-display: swap;\n  src: url(/_next/static/media/886030b0b59bc5a7-s.woff2) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'JetBrains Mono';\n  font-style: normal;\n  font-weight: 100 800;\n  font-display: swap;\n  src: url(/_next/static/media/0aa834ed78bf6d07-s.woff2) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek */\n@font-face {\n  font-family: 'JetBrains Mono';\n  font-style: normal;\n  font-weight: 100 800;\n  font-display: swap;\n  src: url(/_next/static/media/67957d42bae0796d-s.woff2) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'JetBrains Mono';\n  font-style: normal;\n  font-weight: 100 800;\n  font-display: swap;\n  src: url(/_next/static/media/f911b923c6adde36-s.woff2) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'JetBrains Mono';\n  font-style: normal;\n  font-weight: 100 800;\n  font-display: swap;\n  src: url(/_next/static/media/939c4f875ee75fbb-s.woff2) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'JetBrains Mono';\n  font-style: normal;\n  font-weight: 100 800;\n  font-display: swap;\n  src: url(/_next/static/media/bb3ef058b751a6ad-s.woff2) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}@font-face {font-family: 'JetBrains Mono Fallback';src: local(\"Arial\");ascent-override: 75.79%;descent-override: 22.29%;line-gap-override: 0.00%;size-adjust: 134.59%\n}.__className_694534 {font-family: 'JetBrains Mono', 'JetBrains Mono Fallback';font-style: normal\n}.__variable_694534 {--font-jetbrains-mono: 'JetBrains Mono', 'JetBrains Mono Fallback'\n}\n\n@font-face {\nfont-family: 'frutiger';\nsrc: url(/_next/static/media/7f0892586759a7e3-s.p.woff) format('woff');\nfont-display: swap;\nfont-weight: 700;\nfont-style: normal;\n}@font-face {font-family: 'frutiger Fallback';src: local(\"Arial\");ascent-override: 69.85%;descent-override: 21.32%;line-gap-override: 0.00%;size-adjust: 103.53%\n}.__className_0b2d39 {font-family: 'frutiger', 'frutiger Fallback';font-weight: 700;font-style: normal\n}.__variable_0b2d39 {--font-frutiger: 'frutiger', 'frutiger Fallback'\n}\n\n/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\n@layer properties{@supports ((-webkit-hyphens:none) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-space-y-reverse:0;--tw-space-x-reverse:0;--tw-border-style:solid;--tw-gradient-position:initial;--tw-gradient-from:#0000;--tw-gradient-via:#0000;--tw-gradient-to:#0000;--tw-gradient-stops:initial;--tw-gradient-via-stops:initial;--tw-gradient-from-position:0%;--tw-gradient-via-position:50%;--tw-gradient-to-position:100%;--tw-leading:initial;--tw-font-weight:initial;--tw-tracking:initial;--tw-ordinal:initial;--tw-slashed-zero:initial;--tw-numeric-figure:initial;--tw-numeric-spacing:initial;--tw-numeric-fraction:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial;--tw-duration:initial;--tw-ease:initial;--tw-content:\"\"}}}@layer theme{:root,:host{--font-sans:ui-sans-serif,system-ui,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Segoe UI Symbol\",\"Noto Color Emoji\";--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,\"Liberation Mono\",\"Courier New\",monospace;--color-red-50:oklch(97.1% .013 17.38);--color-red-200:oklch(88.5% .062 18.334);--color-red-300:oklch(80.8% .114 19.571);--color-red-400:oklch(70.4% .191 22.216);--color-red-600:oklch(57.7% .245 27.325);--color-red-700:oklch(50.5% .213 27.518);--color-red-900:oklch(39.6% .141 25.723);--color-green-600:oklch(62.7% .194 149.214);--color-green-700:oklch(52.7% .154 150.069);--color-blue-200:oklch(88.2% .059 254.128);--color-blue-300:oklch(80.9% .105 251.813);--color-blue-600:oklch(54.6% .245 262.881);--color-blue-700:oklch(48.8% .243 264.376);--color-slate-50:oklch(98.4% .003 247.858);--color-slate-100:oklch(96.8% .007 247.896);--color-slate-200:oklch(92.9% .013 255.508);--color-slate-500:oklch(55.4% .046 257.417);--color-slate-600:oklch(44.6% .043 257.281);--color-slate-800:oklch(27.9% .041 260.031);--color-slate-900:oklch(20.8% .042 265.755);--color-gray-50:oklch(98.5% .002 247.839);--color-gray-200:oklch(92.8% .006 264.531);--color-gray-400:oklch(70.7% .022 261.325);--color-gray-500:oklch(55.1% .027 264.364);--color-gray-600:oklch(44.6% .03 256.802);--color-gray-700:oklch(37.3% .034 259.733);--color-gray-800:oklch(27.8% .033 256.848);--color-gray-900:oklch(21% .034 264.665);--color-black:#000;--color-white:#fff;--spacing:.25rem;--container-sm:24rem;--container-md:28rem;--container-lg:32rem;--container-4xl:56rem;--container-7xl:80rem;--text-xs:.75rem;--text-xs--line-height:calc(1/.75);--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-base:1rem;--text-base--line-height:calc(1.5/1);--text-lg:1.125rem;--text-lg--line-height:calc(1.75/1.125);--text-xl:1.25rem;--text-xl--line-height:calc(1.75/1.25);--text-2xl:1.5rem;--text-2xl--line-height:calc(2/1.5);--text-3xl:1.875rem;--text-3xl--line-height:calc(2.25/1.875);--text-4xl:2.25rem;--text-4xl--line-height:calc(2.5/2.25);--font-weight-normal:400;--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700;--tracking-tight:-.025em;--tracking-wider:.05em;--tracking-widest:.1em;--leading-tight:1.25;--leading-relaxed:1.625;--radius-sm:calc(var(--radius) - 4px);--radius-md:calc(var(--radius) - 2px);--radius-lg:var(--radius);--radius-xl:.75rem;--radius-2xl:1rem;--radius-3xl:1.5rem;--radius-4xl:2rem;--ease-out:cubic-bezier(0,0,.2,1);--ease-in-out:cubic-bezier(.4,0,.2,1);--animate-spin:spin 1s linear infinite;--animate-pulse:pulse 2s cubic-bezier(.4,0,.6,1)infinite;--blur-sm:8px;--blur-md:12px;--blur-3xl:64px;--aspect-video:16/9;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono);--radius:.625rem;--animate-accordion-down:accordion-down .2s ease-out;--animate-accordion-up:accordion-up .2s ease-out;--color-platyfend-500:#00617b;--color-platyfend-600:#005569;--color-background:rgb(var(--background));--color-foreground:rgb(var(--foreground));--color-card:rgb(var(--card));--color-card-foreground:rgb(var(--card-foreground));--color-popover:rgb(var(--popover));--color-popover-foreground:rgb(var(--popover-foreground));--color-primary:rgb(var(--primary));--color-primary-foreground:rgb(var(--primary-foreground));--color-secondary:rgb(var(--secondary));--color-secondary-foreground:rgb(var(--secondary-foreground));--color-muted:rgb(var(--muted));--color-muted-foreground:rgb(var(--muted-foreground));--color-accent:rgb(var(--accent));--color-accent-foreground:rgb(var(--accent-foreground));--color-destructive:rgb(var(--destructive));--color-destructive-foreground:rgb(var(--destructive-foreground));--color-border:rgb(var(--border));--color-input:rgb(var(--input));--color-ring:rgb(var(--ring));--color-sidebar:rgb(var(--sidebar));--color-sidebar-foreground:rgb(var(--sidebar-foreground));--color-sidebar-accent:rgb(var(--sidebar-accent));--color-sidebar-accent-foreground:rgb(var(--sidebar-accent-foreground));--color-sidebar-border:rgb(var(--sidebar-border));--color-sidebar-ring:rgb(var(--sidebar-ring))}}@layer base{*,:after,:before,::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}html,:host{-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Segoe UI Symbol\",\"Noto Color Emoji\");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,\"Liberation Mono\",\"Courier New\",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::-moz-placeholder{opacity:1}::placeholder{opacity:1}@supports (not (-webkit-appearance:-apple-pay-button)) or (contain-intrinsic-size:1px){::-moz-placeholder{color:currentColor}::placeholder{color:currentColor}@supports (color:color-mix(in lab, red, red)){::-moz-placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit{padding-block:0}::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-day-field{padding-block:0}::-webkit-datetime-edit-hour-field{padding-block:0}::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-millisecond-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){-webkit-appearance:button;-moz-appearance:button;appearance:button}::file-selector-button{-webkit-appearance:button;-moz-appearance:button;appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}}@layer components;@layer utilities{.pointer-events-auto{pointer-events:auto}.pointer-events-none{pointer-events:none}.invisible{visibility:hidden}.sr-only{clip:rect(0,0,0,0);white-space:nowrap;border-width:0;width:1px;height:1px;margin:-1px;padding:0;position:absolute;overflow:hidden}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.static{position:static}.inset-0{inset:calc(var(--spacing)*0)}.inset-x-0{inset-inline:calc(var(--spacing)*0)}.inset-y-0{inset-block:calc(var(--spacing)*0)}.-top-12{top:calc(var(--spacing)*-12)}.top-0{top:calc(var(--spacing)*0)}.top-1\\.5{top:calc(var(--spacing)*1.5)}.top-1\\/2{top:50%}.top-1\\/4{top:25%}.top-2{top:calc(var(--spacing)*2)}.top-3\\.5{top:calc(var(--spacing)*3.5)}.top-4{top:calc(var(--spacing)*4)}.top-\\[1px\\]{top:1px}.top-\\[50\\%\\]{top:50%}.top-\\[60\\%\\]{top:60%}.top-full{top:100%}.-right-12{right:calc(var(--spacing)*-12)}.right-0{right:calc(var(--spacing)*0)}.right-1{right:calc(var(--spacing)*1)}.right-1\\.5{right:calc(var(--spacing)*1.5)}.right-1\\/2{right:50%}.right-1\\/4{right:25%}.right-2{right:calc(var(--spacing)*2)}.right-3{right:calc(var(--spacing)*3)}.right-4{right:calc(var(--spacing)*4)}.-bottom-12{bottom:calc(var(--spacing)*-12)}.bottom-0{bottom:calc(var(--spacing)*0)}.bottom-1\\.5{bottom:calc(var(--spacing)*1.5)}.bottom-1\\/4{bottom:25%}.-left-12{left:calc(var(--spacing)*-12)}.left-0{left:calc(var(--spacing)*0)}.left-1{left:calc(var(--spacing)*1)}.left-1\\.5{left:calc(var(--spacing)*1.5)}.left-1\\/2{left:50%}.left-1\\/4{left:25%}.left-2{left:calc(var(--spacing)*2)}.left-\\[50\\%\\]{left:50%}.z-10{z-index:10}.z-20{z-index:20}.z-50{z-index:50}.z-\\[1\\]{z-index:1}.z-\\[100\\]{z-index:100}.container{width:100%}@media (min-width:475px){.container{max-width:475px}}@media (min-width:640px){.container{max-width:640px}}@media (min-width:768px){.container{max-width:768px}}@media (min-width:1024px){.container{max-width:1024px}}@media (min-width:1280px){.container{max-width:1280px}}@media (min-width:1536px){.container{max-width:1536px}}.m-4{margin:calc(var(--spacing)*4)}.-mx-1{margin-inline:calc(var(--spacing)*-1)}.mx-2{margin-inline:calc(var(--spacing)*2)}.mx-3\\.5{margin-inline:calc(var(--spacing)*3.5)}.mx-auto{margin-inline:auto}.my-0\\.5{margin-block:calc(var(--spacing)*.5)}.my-1{margin-block:calc(var(--spacing)*1)}.-mt-4{margin-top:calc(var(--spacing)*-4)}.mt-0\\.5{margin-top:calc(var(--spacing)*.5)}.mt-1{margin-top:calc(var(--spacing)*1)}.mt-1\\.5{margin-top:calc(var(--spacing)*1.5)}.mt-2{margin-top:calc(var(--spacing)*2)}.mt-3{margin-top:calc(var(--spacing)*3)}.mt-4{margin-top:calc(var(--spacing)*4)}.mt-6{margin-top:calc(var(--spacing)*6)}.mt-8{margin-top:calc(var(--spacing)*8)}.mt-24{margin-top:calc(var(--spacing)*24)}.mt-auto{margin-top:auto}.mr-0\\.5{margin-right:calc(var(--spacing)*.5)}.mr-2{margin-right:calc(var(--spacing)*2)}.mr-3{margin-right:calc(var(--spacing)*3)}.mb-1{margin-bottom:calc(var(--spacing)*1)}.mb-2{margin-bottom:calc(var(--spacing)*2)}.mb-3{margin-bottom:calc(var(--spacing)*3)}.mb-4{margin-bottom:calc(var(--spacing)*4)}.mb-6{margin-bottom:calc(var(--spacing)*6)}.mb-8{margin-bottom:calc(var(--spacing)*8)}.-ml-4{margin-left:calc(var(--spacing)*-4)}.ml-0\\.5{margin-left:calc(var(--spacing)*.5)}.ml-1{margin-left:calc(var(--spacing)*1)}.ml-auto{margin-left:auto}.block{display:block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.inline{display:inline}.inline-flex{display:inline-flex}.table{display:table}.aspect-square{aspect-ratio:1}.aspect-video{aspect-ratio:var(--aspect-video)}.size-4{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.h-1\\.5{height:calc(var(--spacing)*1.5)}.h-2{height:calc(var(--spacing)*2)}.h-2\\.5{height:calc(var(--spacing)*2.5)}.h-3{height:calc(var(--spacing)*3)}.h-3\\.5{height:calc(var(--spacing)*3.5)}.h-4{height:calc(var(--spacing)*4)}.h-5{height:calc(var(--spacing)*5)}.h-6{height:calc(var(--spacing)*6)}.h-7{height:calc(var(--spacing)*7)}.h-8{height:calc(var(--spacing)*8)}.h-9{height:calc(var(--spacing)*9)}.h-10{height:calc(var(--spacing)*10)}.h-11{height:calc(var(--spacing)*11)}.h-12{height:calc(var(--spacing)*12)}.h-96{height:calc(var(--spacing)*96)}.h-\\[1px\\]{height:1px}.h-\\[800px\\]{height:800px}.h-\\[var\\(--radix-navigation-menu-viewport-height\\)\\]{height:var(--radix-navigation-menu-viewport-height)}.h-\\[var\\(--radix-select-trigger-height\\)\\]{height:var(--radix-select-trigger-height)}.h-auto{height:auto}.h-full{height:100%}.h-px{height:1px}.h-screen{height:100vh}.h-svh{height:100svh}.max-h-32{max-height:calc(var(--spacing)*32)}.max-h-96{max-height:calc(var(--spacing)*96)}.max-h-\\[300px\\]{max-height:300px}.max-h-screen{max-height:100vh}.min-h-0{min-height:calc(var(--spacing)*0)}.min-h-\\[12rem\\]{min-height:12rem}.min-h-\\[80px\\]{min-height:80px}.min-h-\\[400px\\]{min-height:400px}.min-h-screen{min-height:100vh}.min-h-svh{min-height:100svh}.w-0{width:calc(var(--spacing)*0)}.w-1{width:calc(var(--spacing)*1)}.w-2{width:calc(var(--spacing)*2)}.w-2\\.5{width:calc(var(--spacing)*2.5)}.w-3{width:calc(var(--spacing)*3)}.w-3\\.5{width:calc(var(--spacing)*3.5)}.w-3\\/4{width:75%}.w-4{width:calc(var(--spacing)*4)}.w-5{width:calc(var(--spacing)*5)}.w-7{width:calc(var(--spacing)*7)}.w-8{width:calc(var(--spacing)*8)}.w-9{width:calc(var(--spacing)*9)}.w-10{width:calc(var(--spacing)*10)}.w-11{width:calc(var(--spacing)*11)}.w-12{width:calc(var(--spacing)*12)}.w-48{width:calc(var(--spacing)*48)}.w-56{width:calc(var(--spacing)*56)}.w-64{width:calc(var(--spacing)*64)}.w-72{width:calc(var(--spacing)*72)}.w-96{width:calc(var(--spacing)*96)}.w-\\[--radix-dropdown-menu-trigger-width\\]{width:--radix-dropdown-menu-trigger-width}.w-\\[--sidebar-width-tablet\\]{width:--sidebar-width-tablet}.w-\\[--sidebar-width\\]{width:--sidebar-width}.w-\\[1px\\]{width:1px}.w-\\[100px\\]{width:100px}.w-\\[240px\\]{width:240px}.w-\\[800px\\]{width:800px}.w-auto{width:auto}.w-full{width:100%}.w-max{width:-moz-max-content;width:max-content}.w-px{width:1px}.max-w-4xl{max-width:var(--container-4xl)}.max-w-7xl{max-width:var(--container-7xl)}.max-w-96{max-width:calc(var(--spacing)*96)}.max-w-\\[--skeleton-width\\]{max-width:--skeleton-width}.max-w-lg{max-width:var(--container-lg)}.max-w-max{max-width:-moz-max-content;max-width:max-content}.max-w-md{max-width:var(--container-md)}.min-w-0{min-width:calc(var(--spacing)*0)}.min-w-5{min-width:calc(var(--spacing)*5)}.min-w-56{min-width:calc(var(--spacing)*56)}.min-w-\\[8rem\\]{min-width:8rem}.min-w-\\[12rem\\]{min-width:12rem}.min-w-\\[var\\(--radix-select-trigger-width\\)\\]{min-width:var(--radix-select-trigger-width)}.flex-1{flex:1}.flex-shrink-0,.shrink-0{flex-shrink:0}.grow{flex-grow:1}.grow-0{flex-grow:0}.basis-full{flex-basis:100%}.caption-bottom{caption-side:bottom}.border-collapse{border-collapse:collapse}.-translate-x-1\\/2{--tw-translate-x:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-x-px{--tw-translate-x:-1px;translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-x-\\[-50\\%\\]{--tw-translate-x:-50%;translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-x-px{--tw-translate-x:1px;translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-y-1\\/2{--tw-translate-y:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-y-\\[-50\\%\\]{--tw-translate-y:-50%;translate:var(--tw-translate-x)var(--tw-translate-y)}.rotate-45{rotate:45deg}.rotate-90{rotate:90deg}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.animate-pulse{animation:var(--animate-pulse)}.animate-spin{animation:var(--animate-spin)}.cursor-default{cursor:default}.cursor-pointer{cursor:pointer}.touch-none{touch-action:none}.resize{resize:both}.list-none{list-style-type:none}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.flex-col{flex-direction:column}.flex-col-reverse{flex-direction:column-reverse}.flex-row{flex-direction:row}.flex-wrap{flex-wrap:wrap}.items-center{align-items:center}.items-end{align-items:flex-end}.items-start{align-items:flex-start}.items-stretch{align-items:stretch}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.justify-start{justify-content:flex-start}.gap-1{gap:calc(var(--spacing)*1)}.gap-1\\.5{gap:calc(var(--spacing)*1.5)}.gap-2{gap:calc(var(--spacing)*2)}.gap-3{gap:calc(var(--spacing)*3)}.gap-4{gap:calc(var(--spacing)*4)}:where(.space-y-0>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*0)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*0)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-1>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-1\\.5>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*1.5)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*1.5)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-2>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*2)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-3>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*3)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-4>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-6>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*6)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-x-1>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*1)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-2>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-4>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*4)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-x-reverse)))}.truncate{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.overflow-x-hidden{overflow-x:hidden}.overflow-y-auto{overflow-y:auto}.rounded{border-radius:var(--radius)}.rounded-2xl{border-radius:var(--radius-2xl)}.rounded-3xl{border-radius:var(--radius-3xl)}.rounded-4xl{border-radius:var(--radius-4xl)}.rounded-\\[2px\\]{border-radius:2px}.rounded-\\[inherit\\]{border-radius:inherit}.rounded-full{border-radius:3.40282e38px}.rounded-lg{border-radius:var(--radius-lg)}.rounded-md{border-radius:var(--radius-md)}.rounded-sm{border-radius:var(--radius-sm)}.rounded-xl{border-radius:var(--radius-xl)}.rounded-t-\\[10px\\]{border-top-left-radius:10px;border-top-right-radius:10px}.rounded-tl-sm{border-top-left-radius:var(--radius-sm)}.border{border-style:var(--tw-border-style);border-width:1px}.border-0{border-style:var(--tw-border-style);border-width:0}.border-2{border-style:var(--tw-border-style);border-width:2px}.border-\\[1\\.5px\\]{border-style:var(--tw-border-style);border-width:1.5px}.border-y{border-block-style:var(--tw-border-style);border-block-width:1px}.border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.border-r{border-right-style:var(--tw-border-style);border-right-width:1px}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-b-2{border-bottom-style:var(--tw-border-style);border-bottom-width:2px}.border-l{border-left-style:var(--tw-border-style);border-left-width:1px}.border-dashed{--tw-border-style:dashed;border-style:dashed}.border-none{--tw-border-style:none;border-style:none}.border-\\[\\#00617b\\]{border-color:#00617b}.border-\\[--color-border\\]{border-color:--color-border}.border-border\\/50{border-color:color-mix(in srgb,rgb(var(--border))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.border-border\\/50{border-color:color-mix(in oklab,var(--color-border)50%,transparent)}}.border-destructive{border-color:var(--color-destructive)}.border-destructive\\/50{border-color:color-mix(in srgb,rgb(var(--destructive))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.border-destructive\\/50{border-color:color-mix(in oklab,var(--color-destructive)50%,transparent)}}.border-gray-200{border-color:var(--color-gray-200)}.border-input{border-color:var(--color-input)}.border-primary{border-color:var(--color-primary)}.border-red-200{border-color:var(--color-red-200)}.border-sidebar-border{border-color:var(--color-sidebar-border)}.border-slate-100{border-color:var(--color-slate-100)}.border-slate-200{border-color:var(--color-slate-200)}.border-slate-200\\/30{border-color:#e2e8f04d}@supports (color:color-mix(in lab, red, red)){.border-slate-200\\/30{border-color:color-mix(in oklab,var(--color-slate-200)30%,transparent)}}.border-transparent{border-color:#0000}.border-white\\/20{border-color:#fff3}@supports (color:color-mix(in lab, red, red)){.border-white\\/20{border-color:color-mix(in oklab,var(--color-white)20%,transparent)}}.border-t-transparent{border-top-color:#0000}.border-l-transparent{border-left-color:#0000}.bg-\\[--color-bg\\]{background-color:--color-bg}.bg-accent{background-color:var(--color-accent)}.bg-background{background-color:var(--color-background)}.bg-black\\/80{background-color:#000c}@supports (color:color-mix(in lab, red, red)){.bg-black\\/80{background-color:color-mix(in oklab,var(--color-black)80%,transparent)}}.bg-border{background-color:var(--color-border)}.bg-card{background-color:var(--color-card)}.bg-destructive{background-color:var(--color-destructive)}.bg-foreground{background-color:var(--color-foreground)}.bg-gray-50{background-color:var(--color-gray-50)}.bg-muted{background-color:var(--color-muted)}.bg-muted\\/50{background-color:color-mix(in srgb,rgb(var(--muted))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.bg-muted\\/50{background-color:color-mix(in oklab,var(--color-muted)50%,transparent)}}.bg-platyfend-500{background-color:var(--color-platyfend-500)}.bg-popover{background-color:var(--color-popover)}.bg-primary{background-color:var(--color-primary)}.bg-primary\\/5{background-color:color-mix(in srgb,rgb(var(--primary))5%,transparent)}@supports (color:color-mix(in lab, red, red)){.bg-primary\\/5{background-color:color-mix(in oklab,var(--color-primary)5%,transparent)}}.bg-red-50{background-color:var(--color-red-50)}.bg-secondary{background-color:var(--color-secondary)}.bg-secondary\\/5{background-color:color-mix(in srgb,rgb(var(--secondary))5%,transparent)}@supports (color:color-mix(in lab, red, red)){.bg-secondary\\/5{background-color:color-mix(in oklab,var(--color-secondary)5%,transparent)}}.bg-sidebar{background-color:var(--color-sidebar)}.bg-sidebar-border{background-color:var(--color-sidebar-border)}.bg-slate-50{background-color:var(--color-slate-50)}.bg-transparent{background-color:#0000}.bg-white{background-color:var(--color-white)}.bg-white\\/60{background-color:#fff9}@supports (color:color-mix(in lab, red, red)){.bg-white\\/60{background-color:color-mix(in oklab,var(--color-white)60%,transparent)}}.bg-white\\/70{background-color:#ffffffb3}@supports (color:color-mix(in lab, red, red)){.bg-white\\/70{background-color:color-mix(in oklab,var(--color-white)70%,transparent)}}.bg-gradient-to-br{--tw-gradient-position:to bottom right in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}.from-primary\\/5{--tw-gradient-from:color-mix(in srgb,rgb(var(--primary))5%,transparent)}@supports (color:color-mix(in lab, red, red)){.from-primary\\/5{--tw-gradient-from:color-mix(in oklab,var(--color-primary)5%,transparent)}}.from-primary\\/5{--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-slate-50{--tw-gradient-from:var(--color-slate-50);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-blue-200{--tw-gradient-via:var(--color-blue-200);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.via-transparent{--tw-gradient-via:transparent;--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-secondary\\/5{--tw-gradient-to:color-mix(in srgb,rgb(var(--secondary))5%,transparent)}@supports (color:color-mix(in lab, red, red)){.to-secondary\\/5{--tw-gradient-to:color-mix(in oklab,var(--color-secondary)5%,transparent)}}.to-secondary\\/5{--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-slate-100{--tw-gradient-to:var(--color-slate-100);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.fill-current{fill:currentColor}.p-0{padding:calc(var(--spacing)*0)}.p-1{padding:calc(var(--spacing)*1)}.p-1\\.5{padding:calc(var(--spacing)*1.5)}.p-2{padding:calc(var(--spacing)*2)}.p-3{padding:calc(var(--spacing)*3)}.p-4{padding:calc(var(--spacing)*4)}.p-6{padding:calc(var(--spacing)*6)}.p-8{padding:calc(var(--spacing)*8)}.p-\\[1px\\]{padding:1px}.px-1{padding-inline:calc(var(--spacing)*1)}.px-2{padding-inline:calc(var(--spacing)*2)}.px-2\\.5{padding-inline:calc(var(--spacing)*2.5)}.px-3{padding-inline:calc(var(--spacing)*3)}.px-4{padding-inline:calc(var(--spacing)*4)}.px-5{padding-inline:calc(var(--spacing)*5)}.px-8{padding-inline:calc(var(--spacing)*8)}.py-0\\.5{padding-block:calc(var(--spacing)*.5)}.py-1{padding-block:calc(var(--spacing)*1)}.py-1\\.5{padding-block:calc(var(--spacing)*1.5)}.py-2{padding-block:calc(var(--spacing)*2)}.py-2\\.5{padding-block:calc(var(--spacing)*2.5)}.py-3{padding-block:calc(var(--spacing)*3)}.py-4{padding-block:calc(var(--spacing)*4)}.py-6{padding-block:calc(var(--spacing)*6)}.py-8{padding-block:calc(var(--spacing)*8)}.pt-0{padding-top:calc(var(--spacing)*0)}.pt-1{padding-top:calc(var(--spacing)*1)}.pt-3{padding-top:calc(var(--spacing)*3)}.pt-4{padding-top:calc(var(--spacing)*4)}.pt-6{padding-top:calc(var(--spacing)*6)}.pr-2{padding-right:calc(var(--spacing)*2)}.pr-2\\.5{padding-right:calc(var(--spacing)*2.5)}.pr-8{padding-right:calc(var(--spacing)*8)}.pb-2{padding-bottom:calc(var(--spacing)*2)}.pb-3{padding-bottom:calc(var(--spacing)*3)}.pb-4{padding-bottom:calc(var(--spacing)*4)}.pl-2\\.5{padding-left:calc(var(--spacing)*2.5)}.pl-4{padding-left:calc(var(--spacing)*4)}.pl-8{padding-left:calc(var(--spacing)*8)}.pl-12{padding-left:calc(var(--spacing)*12)}.text-center{text-align:center}.text-left{text-align:left}.align-middle{vertical-align:middle}.font-mono{font-family:var(--font-mono)}.text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.text-base{font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height))}.text-lg{font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height))}.text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.text-xs{font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.text-\\[0\\.8rem\\]{font-size:.8rem}.leading-6{--tw-leading:calc(var(--spacing)*6);line-height:calc(var(--spacing)*6)}.leading-none{--tw-leading:1;line-height:1}.leading-relaxed{--tw-leading:var(--leading-relaxed);line-height:var(--leading-relaxed)}.leading-tight{--tw-leading:var(--leading-tight);line-height:var(--leading-tight)}.font-bold{--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold)}.font-medium{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.font-normal{--tw-font-weight:var(--font-weight-normal);font-weight:var(--font-weight-normal)}.font-semibold{--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.tracking-tight{--tw-tracking:var(--tracking-tight);letter-spacing:var(--tracking-tight)}.tracking-wider{--tw-tracking:var(--tracking-wider);letter-spacing:var(--tracking-wider)}.tracking-widest{--tw-tracking:var(--tracking-widest);letter-spacing:var(--tracking-widest)}.break-words{overflow-wrap:break-word}.break-all{word-break:break-all}.whitespace-nowrap{white-space:nowrap}.text-\\[\\#00617b\\]{color:#00617b}.text-accent-foreground{color:var(--color-accent-foreground)}.text-black{color:var(--color-black)}.text-blue-600{color:var(--color-blue-600)}.text-blue-700{color:var(--color-blue-700)}.text-card-foreground{color:var(--color-card-foreground)}.text-current{color:currentColor}.text-destructive{color:var(--color-destructive)}.text-destructive-foreground{color:var(--color-destructive-foreground)}.text-foreground{color:var(--color-foreground)}.text-foreground\\/50{color:color-mix(in srgb,rgb(var(--foreground))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.text-foreground\\/50{color:color-mix(in oklab,var(--color-foreground)50%,transparent)}}.text-gray-400{color:var(--color-gray-400)}.text-gray-500{color:var(--color-gray-500)}.text-gray-600{color:var(--color-gray-600)}.text-gray-700{color:var(--color-gray-700)}.text-gray-800{color:var(--color-gray-800)}.text-gray-900{color:var(--color-gray-900)}.text-green-600{color:var(--color-green-600)}.text-green-700{color:var(--color-green-700)}.text-muted-foreground{color:var(--color-muted-foreground)}.text-popover-foreground{color:var(--color-popover-foreground)}.text-primary{color:var(--color-primary)}.text-primary-foreground{color:var(--color-primary-foreground)}.text-red-600{color:var(--color-red-600)}.text-red-700{color:var(--color-red-700)}.text-red-900{color:var(--color-red-900)}.text-secondary-foreground{color:var(--color-secondary-foreground)}.text-sidebar-foreground{color:var(--color-sidebar-foreground)}.text-sidebar-foreground\\/70{color:color-mix(in srgb,rgb(var(--sidebar-foreground))70%,transparent)}@supports (color:color-mix(in lab, red, red)){.text-sidebar-foreground\\/70{color:color-mix(in oklab,var(--color-sidebar-foreground)70%,transparent)}}.text-slate-500{color:var(--color-slate-500)}.text-slate-600{color:var(--color-slate-600)}.text-slate-800{color:var(--color-slate-800)}.text-slate-900{color:var(--color-slate-900)}.uppercase{text-transform:uppercase}.tabular-nums{--tw-numeric-spacing:tabular-nums;font-variant-numeric:var(--tw-ordinal,)var(--tw-slashed-zero,)var(--tw-numeric-figure,)var(--tw-numeric-spacing,)var(--tw-numeric-fraction,)}.underline{text-decoration-line:underline}.underline-offset-2{text-underline-offset:2px}.underline-offset-4{text-underline-offset:4px}.opacity-0{opacity:0}.opacity-50{opacity:.5}.opacity-60{opacity:.6}.opacity-70{opacity:.7}.opacity-90{opacity:.9}.shadow-2xl{--tw-shadow:0 25px 50px -12px var(--tw-shadow-color,#00000040);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-border\\)\\)\\]{--tw-shadow:0 0 0 1px var(--tw-shadow-color,hsl(var(--sidebar-border)));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-md{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-none{--tw-shadow:0 0 #0000;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-xl{--tw-shadow:0 20px 25px -5px var(--tw-shadow-color,#0000001a),0 8px 10px -6px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-0{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-2{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-slate-900\\/5{--tw-shadow-color:#0f172b0d}@supports (color:color-mix(in lab, red, red)){.shadow-slate-900\\/5{--tw-shadow-color:color-mix(in oklab,color-mix(in oklab,var(--color-slate-900)5%,transparent)var(--tw-shadow-alpha),transparent)}}.ring-ring{--tw-ring-color:var(--color-ring)}.ring-sidebar-ring{--tw-ring-color:var(--color-sidebar-ring)}.ring-offset-background{--tw-ring-offset-color:var(--color-background)}.outline{outline-style:var(--tw-outline-style);outline-width:1px}.blur-3xl{--tw-blur:blur(var(--blur-3xl));filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.backdrop-blur-md{--tw-backdrop-blur:blur(var(--blur-md));-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.backdrop-blur-sm{--tw-backdrop-blur:blur(var(--blur-sm));-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\\[left\\,right\\,width\\]{transition-property:left,right,width;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\\[margin\\,opa\\]{transition-property:margin,opa;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\\[width\\,height\\,padding\\]{transition-property:width,height,padding;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\\[width\\]{transition-property:width;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-all{transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-opacity{transition-property:opacity;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-transform{transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.delay-1000{transition-delay:1s}.duration-200{--tw-duration:.2s;transition-duration:.2s}.duration-300{--tw-duration:.3s;transition-duration:.3s}.duration-1000{--tw-duration:1s;transition-duration:1s}.ease-in-out{--tw-ease:var(--ease-in-out);transition-timing-function:var(--ease-in-out)}.ease-linear{--tw-ease:linear;transition-timing-function:linear}.ease-out{--tw-ease:var(--ease-out);transition-timing-function:var(--ease-out)}.outline-none{--tw-outline-style:none;outline-style:none}.select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.group-focus-within\\/menu-item\\:opacity-100:is(:where(.group\\/menu-item):focus-within *){opacity:1}@media (hover:hover){.group-hover\\:opacity-100:is(:where(.group):hover *),.group-hover\\/menu-item\\:opacity-100:is(:where(.group\\/menu-item):hover *){opacity:1}}.group-has-\\[\\[data-sidebar\\=menu-action\\]\\]\\/menu-item\\:pr-8:is(:where(.group\\/menu-item):has([data-sidebar=menu-action]) *){padding-right:calc(var(--spacing)*8)}.group-data-\\[collapsible\\=icon\\]\\:-mt-8:is(:where(.group)[data-collapsible=icon] *){margin-top:calc(var(--spacing)*-8)}.group-data-\\[collapsible\\=icon\\]\\:hidden:is(:where(.group)[data-collapsible=icon] *){display:none}.group-data-\\[collapsible\\=icon\\]\\:\\!size-8:is(:where(.group)[data-collapsible=icon] *){width:calc(var(--spacing)*8)!important;height:calc(var(--spacing)*8)!important}.group-data-\\[collapsible\\=icon\\]\\:w-\\[--sidebar-width-icon\\]:is(:where(.group)[data-collapsible=icon] *){width:--sidebar-width-icon}.group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)_\\+_theme\\(spacing\\.4\\)\\)\\]:is(:where(.group)[data-collapsible=icon] *){width:calc(var(--sidebar-width-icon) + 1rem)}.group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)_\\+_theme\\(spacing\\.4\\)_\\+2px\\)\\]:is(:where(.group)[data-collapsible=icon] *){width:calc(var(--sidebar-width-icon) + 1rem + 2px)}.group-data-\\[collapsible\\=icon\\]\\:overflow-hidden:is(:where(.group)[data-collapsible=icon] *){overflow:hidden}.group-data-\\[collapsible\\=icon\\]\\:\\!p-0:is(:where(.group)[data-collapsible=icon] *){padding:calc(var(--spacing)*0)!important}.group-data-\\[collapsible\\=icon\\]\\:\\!p-2:is(:where(.group)[data-collapsible=icon] *){padding:calc(var(--spacing)*2)!important}.group-data-\\[collapsible\\=icon\\]\\:opacity-0:is(:where(.group)[data-collapsible=icon] *){opacity:0}.group-data-\\[collapsible\\=offcanvas\\]\\:right-\\[calc\\(var\\(--sidebar-width-tablet\\)\\*-1\\)\\]:is(:where(.group)[data-collapsible=offcanvas] *){right:calc(var(--sidebar-width-tablet)*-1)}.group-data-\\[collapsible\\=offcanvas\\]\\:left-\\[calc\\(var\\(--sidebar-width-tablet\\)\\*-1\\)\\]:is(:where(.group)[data-collapsible=offcanvas] *){left:calc(var(--sidebar-width-tablet)*-1)}.group-data-\\[collapsible\\=offcanvas\\]\\:w-0:is(:where(.group)[data-collapsible=offcanvas] *){width:calc(var(--spacing)*0)}.group-data-\\[collapsible\\=offcanvas\\]\\:translate-x-0:is(:where(.group)[data-collapsible=offcanvas] *){--tw-translate-x:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.group-data-\\[side\\=left\\]\\:-right-4:is(:where(.group)[data-side=left] *){right:calc(var(--spacing)*-4)}.group-data-\\[side\\=left\\]\\:border-r:is(:where(.group)[data-side=left] *){border-right-style:var(--tw-border-style);border-right-width:1px}.group-data-\\[side\\=right\\]\\:left-0:is(:where(.group)[data-side=right] *){left:calc(var(--spacing)*0)}.group-data-\\[side\\=right\\]\\:rotate-180:is(:where(.group)[data-side=right] *){rotate:180deg}.group-data-\\[side\\=right\\]\\:border-l:is(:where(.group)[data-side=right] *){border-left-style:var(--tw-border-style);border-left-width:1px}.group-data-\\[state\\=open\\]\\:rotate-180:is(:where(.group)[data-state=open] *){rotate:180deg}.group-data-\\[variant\\=floating\\]\\:rounded-lg:is(:where(.group)[data-variant=floating] *){border-radius:var(--radius-lg)}.group-data-\\[variant\\=floating\\]\\:border:is(:where(.group)[data-variant=floating] *){border-style:var(--tw-border-style);border-width:1px}.group-data-\\[variant\\=floating\\]\\:border-sidebar-border:is(:where(.group)[data-variant=floating] *){border-color:var(--color-sidebar-border)}.group-data-\\[variant\\=floating\\]\\:shadow:is(:where(.group)[data-variant=floating] *){--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.group-\\[\\.destructive\\]\\:border-muted\\/40:is(:where(.group).destructive *){border-color:color-mix(in srgb,rgb(var(--muted))40%,transparent)}@supports (color:color-mix(in lab, red, red)){.group-\\[\\.destructive\\]\\:border-muted\\/40:is(:where(.group).destructive *){border-color:color-mix(in oklab,var(--color-muted)40%,transparent)}}.group-\\[\\.destructive\\]\\:text-red-300:is(:where(.group).destructive *){color:var(--color-red-300)}.group-\\[\\.toast\\]\\:bg-muted:is(:where(.group).toast *){background-color:var(--color-muted)}.group-\\[\\.toast\\]\\:bg-primary:is(:where(.group).toast *){background-color:var(--color-primary)}.group-\\[\\.toast\\]\\:text-muted-foreground:is(:where(.group).toast *){color:var(--color-muted-foreground)}.group-\\[\\.toast\\]\\:text-primary-foreground:is(:where(.group).toast *){color:var(--color-primary-foreground)}.group-\\[\\.toaster\\]\\:border-border:is(:where(.group).toaster *){border-color:var(--color-border)}.group-\\[\\.toaster\\]\\:bg-background:is(:where(.group).toaster *){background-color:var(--color-background)}.group-\\[\\.toaster\\]\\:text-foreground:is(:where(.group).toaster *){color:var(--color-foreground)}.group-\\[\\.toaster\\]\\:shadow-lg:is(:where(.group).toaster *){--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.peer-hover\\/menu-button\\:text-sidebar-accent-foreground:is(:where(.peer\\/menu-button):hover~*){color:var(--color-sidebar-accent-foreground)}}.peer-disabled\\:cursor-not-allowed:is(:where(.peer):disabled~*){cursor:not-allowed}.peer-disabled\\:opacity-70:is(:where(.peer):disabled~*){opacity:.7}.peer-data-\\[active\\=true\\]\\/menu-button\\:text-sidebar-accent-foreground:is(:where(.peer\\/menu-button)[data-active=true]~*){color:var(--color-sidebar-accent-foreground)}.peer-data-\\[size\\=default\\]\\/menu-button\\:top-1\\.5:is(:where(.peer\\/menu-button)[data-size=default]~*){top:calc(var(--spacing)*1.5)}.peer-data-\\[size\\=lg\\]\\/menu-button\\:top-2\\.5:is(:where(.peer\\/menu-button)[data-size=lg]~*){top:calc(var(--spacing)*2.5)}.peer-data-\\[size\\=sm\\]\\/menu-button\\:top-1:is(:where(.peer\\/menu-button)[data-size=sm]~*){top:calc(var(--spacing)*1)}.peer-data-\\[variant\\=inset\\]\\:min-h-\\[calc\\(100svh-theme\\(spacing\\.4\\)\\)\\]:is(:where(.peer)[data-variant=inset]~*){min-height:calc(100svh - 1rem)}.file\\:border-0::file-selector-button{border-style:var(--tw-border-style);border-width:0}.file\\:bg-transparent::file-selector-button{background-color:#0000}.file\\:text-sm::file-selector-button{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.file\\:font-medium::file-selector-button{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.file\\:text-foreground::file-selector-button{color:var(--color-foreground)}.placeholder\\:text-muted-foreground::-moz-placeholder{color:var(--color-muted-foreground)}.placeholder\\:text-muted-foreground::placeholder{color:var(--color-muted-foreground)}.after\\:absolute:after{content:var(--tw-content);position:absolute}.after\\:-inset-2:after{content:var(--tw-content);inset:calc(var(--spacing)*-2)}.after\\:inset-y-0:after{content:var(--tw-content);inset-block:calc(var(--spacing)*0)}.after\\:left-1\\/2:after{content:var(--tw-content);left:50%}.after\\:w-1:after{content:var(--tw-content);width:calc(var(--spacing)*1)}.after\\:w-\\[2px\\]:after{content:var(--tw-content);width:2px}.after\\:-translate-x-1\\/2:after{content:var(--tw-content);--tw-translate-x:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.group-data-\\[collapsible\\=offcanvas\\]\\:after\\:left-full:is(:where(.group)[data-collapsible=offcanvas] *):after{content:var(--tw-content);left:100%}.first\\:rounded-l-md:first-child{border-top-left-radius:var(--radius-md);border-bottom-left-radius:var(--radius-md)}.first\\:border-l:first-child{border-left-style:var(--tw-border-style);border-left-width:1px}.last\\:rounded-r-md:last-child{border-top-right-radius:var(--radius-md);border-bottom-right-radius:var(--radius-md)}.focus-within\\:relative:focus-within{position:relative}.focus-within\\:z-20:focus-within{z-index:20}@media (hover:hover){.hover\\:translate-y-\\[-2px\\]:hover{--tw-translate-y:-2px;translate:var(--tw-translate-x)var(--tw-translate-y)}.hover\\:cursor-pointer:hover{cursor:pointer}.hover\\:border-\\[\\#0052cc\\]\\/20:hover{border-color:oklab(48.0172% -.0332745 -.198382/.2)}.hover\\:border-\\[\\#0078d7\\]\\/20:hover{border-color:oklab(56.9708% -.0527268 -.161904/.2)}.hover\\:border-\\[\\#00617b\\]\\/30:hover{border-color:oklab(45.7853% -.0617035 -.0602073/.3)}.hover\\:border-\\[\\#24292e\\]\\/20:hover{border-color:oklab(27.8058% -.00433138 -.0108552/.2)}.hover\\:border-\\[\\#fc6d26\\]\\/20:hover{border-color:oklab(70.1676% .140013 .130359/.2)}.hover\\:border-blue-300:hover{border-color:var(--color-blue-300)}.hover\\:\\!bg-gray-50:hover{background-color:var(--color-gray-50)!important}.hover\\:bg-\\[\\#f0f6fc\\]:hover{background-color:#f0f6fc}.hover\\:bg-\\[\\#f4f8ff\\]:hover{background-color:#f4f8ff}.hover\\:bg-\\[\\#f6f8fa\\]:hover{background-color:#f6f8fa}.hover\\:bg-\\[\\#fef8f6\\]:hover{background-color:#fef8f6}.hover\\:bg-accent:hover{background-color:var(--color-accent)}.hover\\:bg-destructive\\/80:hover{background-color:color-mix(in srgb,rgb(var(--destructive))80%,transparent)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-destructive\\/80:hover{background-color:color-mix(in oklab,var(--color-destructive)80%,transparent)}}.hover\\:bg-destructive\\/90:hover{background-color:color-mix(in srgb,rgb(var(--destructive))90%,transparent)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-destructive\\/90:hover{background-color:color-mix(in oklab,var(--color-destructive)90%,transparent)}}.hover\\:bg-gray-50:hover{background-color:var(--color-gray-50)}.hover\\:bg-muted:hover{background-color:var(--color-muted)}.hover\\:bg-muted\\/50:hover{background-color:color-mix(in srgb,rgb(var(--muted))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-muted\\/50:hover{background-color:color-mix(in oklab,var(--color-muted)50%,transparent)}}.hover\\:bg-platyfend-600:hover{background-color:var(--color-platyfend-600)}.hover\\:bg-primary:hover{background-color:var(--color-primary)}.hover\\:bg-primary\\/80:hover{background-color:color-mix(in srgb,rgb(var(--primary))80%,transparent)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-primary\\/80:hover{background-color:color-mix(in oklab,var(--color-primary)80%,transparent)}}.hover\\:bg-primary\\/90:hover{background-color:color-mix(in srgb,rgb(var(--primary))90%,transparent)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-primary\\/90:hover{background-color:color-mix(in oklab,var(--color-primary)90%,transparent)}}.hover\\:bg-secondary:hover{background-color:var(--color-secondary)}.hover\\:bg-secondary\\/80:hover{background-color:color-mix(in srgb,rgb(var(--secondary))80%,transparent)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-secondary\\/80:hover{background-color:color-mix(in oklab,var(--color-secondary)80%,transparent)}}.hover\\:bg-sidebar-accent:hover{background-color:var(--color-sidebar-accent)}.hover\\:bg-white:hover{background-color:var(--color-white)}.hover\\:\\!text-gray-700:hover{color:var(--color-gray-700)!important}.hover\\:\\!text-gray-900:hover{color:var(--color-gray-900)!important}.hover\\:text-\\[\\#0052cc\\]:hover{color:#0052cc}.hover\\:text-\\[\\#0078d7\\]:hover{color:#0078d7}.hover\\:text-\\[\\#24292e\\]:hover{color:#24292e}.hover\\:text-\\[\\#fc6d26\\]:hover{color:#fc6d26}.hover\\:text-accent-foreground:hover{color:var(--color-accent-foreground)}.hover\\:text-blue-700:hover{color:var(--color-blue-700)}.hover\\:text-foreground:hover{color:var(--color-foreground)}.hover\\:text-gray-800:hover{color:var(--color-gray-800)}.hover\\:text-muted-foreground:hover{color:var(--color-muted-foreground)}.hover\\:text-primary-foreground:hover{color:var(--color-primary-foreground)}.hover\\:text-sidebar-accent-foreground:hover{color:var(--color-sidebar-accent-foreground)}.hover\\:text-slate-800:hover{color:var(--color-slate-800)}.hover\\:underline:hover{text-decoration-line:underline}.hover\\:opacity-100:hover{opacity:1}.hover\\:shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-accent\\)\\)\\]:hover{--tw-shadow:0 0 0 1px var(--tw-shadow-color,hsl(var(--sidebar-accent)));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.hover\\:shadow-lg:hover{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.hover\\:shadow-md:hover{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.hover\\:shadow-\\[\\#00617b\\]\\/10:hover{--tw-shadow-color:#00617b1a}@supports (color:color-mix(in lab, red, red)){.hover\\:shadow-\\[\\#00617b\\]\\/10:hover{--tw-shadow-color:color-mix(in oklab,oklab(45.7853% -.0617035 -.0602073/.1) var(--tw-shadow-alpha),transparent)}}.group-data-\\[collapsible\\=offcanvas\\]\\:hover\\:bg-sidebar:is(:where(.group)[data-collapsible=offcanvas] *):hover{background-color:var(--color-sidebar)}.group-\\[\\.destructive\\]\\:hover\\:border-destructive\\/30:is(:where(.group).destructive *):hover{border-color:color-mix(in srgb,rgb(var(--destructive))30%,transparent)}@supports (color:color-mix(in lab, red, red)){.group-\\[\\.destructive\\]\\:hover\\:border-destructive\\/30:is(:where(.group).destructive *):hover{border-color:color-mix(in oklab,var(--color-destructive)30%,transparent)}}.group-\\[\\.destructive\\]\\:hover\\:bg-destructive:is(:where(.group).destructive *):hover{background-color:var(--color-destructive)}.group-\\[\\.destructive\\]\\:hover\\:text-destructive-foreground:is(:where(.group).destructive *):hover{color:var(--color-destructive-foreground)}.group-\\[\\.destructive\\]\\:hover\\:text-red-50:is(:where(.group).destructive *):hover{color:var(--color-red-50)}.hover\\:after\\:bg-sidebar-border:hover:after{content:var(--tw-content);background-color:var(--color-sidebar-border)}}.focus\\:bg-accent:focus{background-color:var(--color-accent)}.focus\\:bg-primary:focus{background-color:var(--color-primary)}.focus\\:text-accent-foreground:focus{color:var(--color-accent-foreground)}.focus\\:text-primary-foreground:focus{color:var(--color-primary-foreground)}.focus\\:opacity-100:focus{opacity:1}.focus\\:ring-2:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\\:ring-ring:focus{--tw-ring-color:var(--color-ring)}.focus\\:ring-offset-2:focus{--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus\\:outline-none:focus{--tw-outline-style:none;outline-style:none}.group-\\[\\.destructive\\]\\:focus\\:ring-destructive:is(:where(.group).destructive *):focus{--tw-ring-color:var(--color-destructive)}.group-\\[\\.destructive\\]\\:focus\\:ring-red-400:is(:where(.group).destructive *):focus{--tw-ring-color:var(--color-red-400)}.group-\\[\\.destructive\\]\\:focus\\:ring-offset-red-600:is(:where(.group).destructive *):focus{--tw-ring-offset-color:var(--color-red-600)}.focus-visible\\:ring-1:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\\:ring-2:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\\:ring-ring:focus-visible{--tw-ring-color:var(--color-ring)}.focus-visible\\:ring-sidebar-ring:focus-visible{--tw-ring-color:var(--color-sidebar-ring)}.focus-visible\\:ring-offset-1:focus-visible{--tw-ring-offset-width:1px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus-visible\\:ring-offset-2:focus-visible{--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus-visible\\:ring-offset-background:focus-visible{--tw-ring-offset-color:var(--color-background)}.focus-visible\\:outline-hidden:focus-visible{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.focus-visible\\:outline-hidden:focus-visible{outline-offset:2px;outline:2px solid #0000}}.focus-visible\\:outline-none:focus-visible{--tw-outline-style:none;outline-style:none}.active\\:translate-y-\\[0px\\]:active{--tw-translate-y:0px;translate:var(--tw-translate-x)var(--tw-translate-y)}.active\\:bg-sidebar-accent:active{background-color:var(--color-sidebar-accent)}.active\\:text-sidebar-accent-foreground:active{color:var(--color-sidebar-accent-foreground)}.active\\:shadow-sm:active{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.disabled\\:pointer-events-none:disabled{pointer-events:none}.disabled\\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\\:opacity-50:disabled,.has-\\[\\:disabled\\]\\:opacity-50:has(:disabled){opacity:.5}.has-\\[\\[data-variant\\=inset\\]\\]\\:bg-sidebar:has([data-variant=inset]){background-color:var(--color-sidebar)}.aria-disabled\\:pointer-events-none[aria-disabled=true]{pointer-events:none}.aria-disabled\\:opacity-50[aria-disabled=true]{opacity:.5}.aria-selected\\:bg-accent[aria-selected=true]{background-color:var(--color-accent)}.aria-selected\\:bg-accent\\/50[aria-selected=true]{background-color:color-mix(in srgb,rgb(var(--accent))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.aria-selected\\:bg-accent\\/50[aria-selected=true]{background-color:color-mix(in oklab,var(--color-accent)50%,transparent)}}.aria-selected\\:text-accent-foreground[aria-selected=true]{color:var(--color-accent-foreground)}.aria-selected\\:text-muted-foreground[aria-selected=true]{color:var(--color-muted-foreground)}.aria-selected\\:opacity-30[aria-selected=true]{opacity:.3}.aria-selected\\:opacity-100[aria-selected=true]{opacity:1}.data-\\[active\\]\\:bg-accent\\/50[data-active]{background-color:color-mix(in srgb,rgb(var(--accent))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.data-\\[active\\]\\:bg-accent\\/50[data-active]{background-color:color-mix(in oklab,var(--color-accent)50%,transparent)}}.data-\\[active\\=true\\]\\:bg-\\[\\#00617b\\]\\/10[data-active=true]{background-color:oklab(45.7853% -.0617035 -.0602073/.1)}.data-\\[active\\=true\\]\\:bg-sidebar-accent[data-active=true]{background-color:var(--color-sidebar-accent)}.data-\\[active\\=true\\]\\:font-medium[data-active=true]{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.data-\\[active\\=true\\]\\:font-semibold[data-active=true]{--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.data-\\[active\\=true\\]\\:text-\\[\\#00617b\\][data-active=true]{color:#00617b}.data-\\[active\\=true\\]\\:text-sidebar-accent-foreground[data-active=true]{color:var(--color-sidebar-accent-foreground)}.data-\\[disabled\\]\\:pointer-events-none[data-disabled]{pointer-events:none}.data-\\[disabled\\]\\:opacity-50[data-disabled]{opacity:.5}.data-\\[disabled\\=true\\]\\:pointer-events-none[data-disabled=true]{pointer-events:none}.data-\\[disabled\\=true\\]\\:opacity-50[data-disabled=true]{opacity:.5}.data-\\[panel-group-direction\\=vertical\\]\\:h-px[data-panel-group-direction=vertical]{height:1px}.data-\\[panel-group-direction\\=vertical\\]\\:w-full[data-panel-group-direction=vertical]{width:100%}.data-\\[panel-group-direction\\=vertical\\]\\:flex-col[data-panel-group-direction=vertical]{flex-direction:column}.data-\\[panel-group-direction\\=vertical\\]\\:after\\:left-0[data-panel-group-direction=vertical]:after{content:var(--tw-content);left:calc(var(--spacing)*0)}.data-\\[panel-group-direction\\=vertical\\]\\:after\\:h-1[data-panel-group-direction=vertical]:after{content:var(--tw-content);height:calc(var(--spacing)*1)}.data-\\[panel-group-direction\\=vertical\\]\\:after\\:w-full[data-panel-group-direction=vertical]:after{content:var(--tw-content);width:100%}.data-\\[panel-group-direction\\=vertical\\]\\:after\\:translate-x-0[data-panel-group-direction=vertical]:after{content:var(--tw-content);--tw-translate-x:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[panel-group-direction\\=vertical\\]\\:after\\:-translate-y-1\\/2[data-panel-group-direction=vertical]:after{content:var(--tw-content);--tw-translate-y:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[selected\\=\\'true\\'\\]\\:bg-accent[data-selected=true]{background-color:var(--color-accent)}.data-\\[selected\\=true\\]\\:text-accent-foreground[data-selected=true]{color:var(--color-accent-foreground)}.data-\\[side\\=bottom\\]\\:translate-y-1[data-side=bottom]{--tw-translate-y:calc(var(--spacing)*1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[side\\=left\\]\\:-translate-x-1[data-side=left]{--tw-translate-x:calc(var(--spacing)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[side\\=right\\]\\:translate-x-1[data-side=right]{--tw-translate-x:calc(var(--spacing)*1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[side\\=top\\]\\:-translate-y-1[data-side=top]{--tw-translate-y:calc(var(--spacing)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[state\\=active\\]\\:bg-background[data-state=active]{background-color:var(--color-background)}.data-\\[state\\=active\\]\\:text-foreground[data-state=active]{color:var(--color-foreground)}.data-\\[state\\=active\\]\\:shadow-sm[data-state=active]{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.data-\\[state\\=checked\\]\\:translate-x-5[data-state=checked]{--tw-translate-x:calc(var(--spacing)*5);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[state\\=checked\\]\\:bg-primary[data-state=checked]{background-color:var(--color-primary)}.data-\\[state\\=checked\\]\\:text-primary-foreground[data-state=checked]{color:var(--color-primary-foreground)}.data-\\[state\\=closed\\]\\:animate-accordion-up[data-state=closed]{animation:var(--animate-accordion-up)}.data-\\[state\\=closed\\]\\:duration-300[data-state=closed]{--tw-duration:.3s;transition-duration:.3s}.data-\\[state\\=on\\]\\:bg-accent[data-state=on]{background-color:var(--color-accent)}.data-\\[state\\=on\\]\\:text-accent-foreground[data-state=on]{color:var(--color-accent-foreground)}.data-\\[state\\=open\\]\\:animate-accordion-down[data-state=open]{animation:var(--animate-accordion-down)}.data-\\[state\\=open\\]\\:bg-accent[data-state=open]{background-color:var(--color-accent)}.data-\\[state\\=open\\]\\:bg-accent\\/50[data-state=open]{background-color:color-mix(in srgb,rgb(var(--accent))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.data-\\[state\\=open\\]\\:bg-accent\\/50[data-state=open]{background-color:color-mix(in oklab,var(--color-accent)50%,transparent)}}.data-\\[state\\=open\\]\\:bg-secondary[data-state=open]{background-color:var(--color-secondary)}.data-\\[state\\=open\\]\\:bg-sidebar-accent[data-state=open]{background-color:var(--color-sidebar-accent)}.data-\\[state\\=open\\]\\:text-accent-foreground[data-state=open]{color:var(--color-accent-foreground)}.data-\\[state\\=open\\]\\:text-muted-foreground[data-state=open]{color:var(--color-muted-foreground)}.data-\\[state\\=open\\]\\:text-sidebar-accent-foreground[data-state=open]{color:var(--color-sidebar-accent-foreground)}.data-\\[state\\=open\\]\\:opacity-100[data-state=open]{opacity:1}.data-\\[state\\=open\\]\\:duration-500[data-state=open]{--tw-duration:.5s;transition-duration:.5s}@media (hover:hover){.data-\\[state\\=open\\]\\:hover\\:bg-sidebar-accent[data-state=open]:hover{background-color:var(--color-sidebar-accent)}.data-\\[state\\=open\\]\\:hover\\:text-sidebar-accent-foreground[data-state=open]:hover{color:var(--color-sidebar-accent-foreground)}}.data-\\[state\\=selected\\]\\:bg-muted[data-state=selected]{background-color:var(--color-muted)}.data-\\[state\\=unchecked\\]\\:translate-x-0[data-state=unchecked]{--tw-translate-x:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[state\\=unchecked\\]\\:bg-input[data-state=unchecked]{background-color:var(--color-input)}.data-\\[swipe\\=cancel\\]\\:translate-x-0[data-swipe=cancel]{--tw-translate-x:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[swipe\\=end\\]\\:translate-x-\\[var\\(--radix-toast-swipe-end-x\\)\\][data-swipe=end]{--tw-translate-x:var(--radix-toast-swipe-end-x);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[swipe\\=move\\]\\:translate-x-\\[var\\(--radix-toast-swipe-move-x\\)\\][data-swipe=move]{--tw-translate-x:var(--radix-toast-swipe-move-x);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[swipe\\=move\\]\\:transition-none[data-swipe=move]{transition-property:none}@media (min-width:640px){.sm\\:top-auto{top:auto}.sm\\:right-0{right:calc(var(--spacing)*0)}.sm\\:bottom-0{bottom:calc(var(--spacing)*0)}.sm\\:mt-0{margin-top:calc(var(--spacing)*0)}.sm\\:mt-4{margin-top:calc(var(--spacing)*4)}.sm\\:mt-8{margin-top:calc(var(--spacing)*8)}.sm\\:mb-8{margin-bottom:calc(var(--spacing)*8)}.sm\\:mb-10{margin-bottom:calc(var(--spacing)*10)}.sm\\:mb-12{margin-bottom:calc(var(--spacing)*12)}.sm\\:flex{display:flex}.sm\\:h-13{height:calc(var(--spacing)*13)}.sm\\:h-\\[15rem\\]{height:15rem}.sm\\:w-60{width:calc(var(--spacing)*60)}.sm\\:w-\\[280px\\]{width:280px}.sm\\:w-auto{width:auto}.sm\\:max-w-sm{max-width:var(--container-sm)}.sm\\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.sm\\:flex-col{flex-direction:column}.sm\\:flex-row{flex-direction:row}.sm\\:justify-end{justify-content:flex-end}.sm\\:gap-2\\.5{gap:calc(var(--spacing)*2.5)}.sm\\:gap-6{gap:calc(var(--spacing)*6)}:where(.sm\\:space-y-0>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*0)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*0)*calc(1 - var(--tw-space-y-reverse)))}:where(.sm\\:space-y-8>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*8)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*8)*calc(1 - var(--tw-space-y-reverse)))}:where(.sm\\:space-x-2>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}:where(.sm\\:space-x-4>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*4)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-x-reverse)))}.sm\\:rounded-lg{border-radius:var(--radius-lg)}.sm\\:p-6{padding:calc(var(--spacing)*6)}.sm\\:p-8{padding:calc(var(--spacing)*8)}.sm\\:px-4{padding-inline:calc(var(--spacing)*4)}.sm\\:px-6{padding-inline:calc(var(--spacing)*6)}.sm\\:py-3{padding-block:calc(var(--spacing)*3)}.sm\\:py-12{padding-block:calc(var(--spacing)*12)}.sm\\:pt-6{padding-top:calc(var(--spacing)*6)}.sm\\:pt-8{padding-top:calc(var(--spacing)*8)}.sm\\:text-left{text-align:left}.sm\\:text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.sm\\:text-3xl{font-size:var(--text-3xl);line-height:var(--tw-leading,var(--text-3xl--line-height))}.sm\\:text-base{font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height))}.sm\\:text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.sm\\:text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.sm\\:leading-8{--tw-leading:calc(var(--spacing)*8);line-height:calc(var(--spacing)*8)}}@media (min-width:768px){.md\\:absolute{position:absolute}.md\\:w-\\[var\\(--radix-navigation-menu-viewport-width\\)\\]{width:var(--radix-navigation-menu-viewport-width)}.md\\:w-auto{width:auto}.md\\:max-w-\\[420px\\]{max-width:420px}.md\\:text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.md\\:opacity-0{opacity:0}.md\\:peer-data-\\[variant\\=inset\\]\\:m-2:is(:where(.peer)[data-variant=inset]~*){margin:calc(var(--spacing)*2)}.md\\:peer-data-\\[variant\\=inset\\]\\:ml-0:is(:where(.peer)[data-variant=inset]~*){margin-left:calc(var(--spacing)*0)}.md\\:peer-data-\\[variant\\=inset\\]\\:rounded-xl:is(:where(.peer)[data-variant=inset]~*){border-radius:var(--radius-xl)}.md\\:peer-data-\\[variant\\=inset\\]\\:shadow:is(:where(.peer)[data-variant=inset]~*){--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.md\\:peer-data-\\[state\\=collapsed\\]\\:peer-data-\\[variant\\=inset\\]\\:ml-2:is(:where(.peer)[data-state=collapsed]~*):is(:where(.peer)[data-variant=inset]~*){margin-left:calc(var(--spacing)*2)}}.after\\:md\\:hidden:after{content:var(--tw-content)}@media (min-width:768px){.after\\:md\\:hidden:after{display:none}}@media (min-width:1024px){.lg\\:block{display:block}.lg\\:flex{display:flex}.lg\\:hidden{display:none}.lg\\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.lg\\:p-8{padding:calc(var(--spacing)*8)}.lg\\:px-8{padding-inline:calc(var(--spacing)*8)}.lg\\:text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.lg\\:text-4xl{font-size:var(--text-4xl);line-height:var(--tw-leading,var(--text-4xl--line-height))}}@media (min-width:1280px){.xl\\:w-\\[--sidebar-width\\]{width:--sidebar-width}.xl\\:group-data-\\[collapsible\\=offcanvas\\]\\:right-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\]:is(:where(.group)[data-collapsible=offcanvas] *){right:calc(var(--sidebar-width)*-1)}.xl\\:group-data-\\[collapsible\\=offcanvas\\]\\:left-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\]:is(:where(.group)[data-collapsible=offcanvas] *){left:calc(var(--sidebar-width)*-1)}}@media (prefers-color-scheme:dark){.dark\\:border-destructive{border-color:var(--color-destructive)}}.\\[\\&_\\.recharts-cartesian-axis-tick_text\\]\\:fill-muted-foreground .recharts-cartesian-axis-tick text{fill:var(--color-muted-foreground)}.\\[\\&_\\.recharts-cartesian-grid_line\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border\\/50 .recharts-cartesian-grid line[stroke=\\#ccc]{stroke:color-mix(in srgb,rgb(var(--border))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.\\[\\&_\\.recharts-cartesian-grid_line\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border\\/50 .recharts-cartesian-grid line[stroke=\\#ccc]{stroke:color-mix(in oklab,var(--color-border)50%,transparent)}}.\\[\\&_\\.recharts-curve\\.recharts-tooltip-cursor\\]\\:stroke-border .recharts-curve.recharts-tooltip-cursor{stroke:var(--color-border)}.\\[\\&_\\.recharts-dot\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent .recharts-dot[stroke=\\#fff]{stroke:#0000}.\\[\\&_\\.recharts-layer\\]\\:outline-none .recharts-layer{--tw-outline-style:none;outline-style:none}.\\[\\&_\\.recharts-polar-grid_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border .recharts-polar-grid [stroke=\\#ccc]{stroke:var(--color-border)}.\\[\\&_\\.recharts-radial-bar-background-sector\\]\\:fill-muted .recharts-radial-bar-background-sector,.\\[\\&_\\.recharts-rectangle\\.recharts-tooltip-cursor\\]\\:fill-muted .recharts-rectangle.recharts-tooltip-cursor{fill:var(--color-muted)}.\\[\\&_\\.recharts-reference-line_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border .recharts-reference-line [stroke=\\#ccc]{stroke:var(--color-border)}.\\[\\&_\\.recharts-sector\\]\\:outline-none .recharts-sector{--tw-outline-style:none;outline-style:none}.\\[\\&_\\.recharts-sector\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent .recharts-sector[stroke=\\#fff]{stroke:#0000}.\\[\\&_\\.recharts-surface\\]\\:outline-none .recharts-surface{--tw-outline-style:none;outline-style:none}.\\[\\&_\\[cmdk-group-heading\\]\\]\\:px-2 [cmdk-group-heading]{padding-inline:calc(var(--spacing)*2)}.\\[\\&_\\[cmdk-group-heading\\]\\]\\:py-1\\.5 [cmdk-group-heading]{padding-block:calc(var(--spacing)*1.5)}.\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-xs [cmdk-group-heading]{font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.\\[\\&_\\[cmdk-group-heading\\]\\]\\:font-medium [cmdk-group-heading]{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-muted-foreground [cmdk-group-heading]{color:var(--color-muted-foreground)}.\\[\\&_\\[cmdk-group\\]\\]\\:px-2 [cmdk-group]{padding-inline:calc(var(--spacing)*2)}.\\[\\&_\\[cmdk-group\\]\\:not\\(\\[hidden\\]\\)_\\~\\[cmdk-group\\]\\]\\:pt-0 [cmdk-group]:not([hidden])~[cmdk-group]{padding-top:calc(var(--spacing)*0)}.\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:h-5 [cmdk-input-wrapper] svg{height:calc(var(--spacing)*5)}.\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:w-5 [cmdk-input-wrapper] svg{width:calc(var(--spacing)*5)}.\\[\\&_\\[cmdk-input\\]\\]\\:h-12 [cmdk-input]{height:calc(var(--spacing)*12)}.\\[\\&_\\[cmdk-item\\]\\]\\:px-2 [cmdk-item]{padding-inline:calc(var(--spacing)*2)}.\\[\\&_\\[cmdk-item\\]\\]\\:py-3 [cmdk-item]{padding-block:calc(var(--spacing)*3)}.\\[\\&_\\[cmdk-item\\]_svg\\]\\:h-5 [cmdk-item] svg{height:calc(var(--spacing)*5)}.\\[\\&_\\[cmdk-item\\]_svg\\]\\:w-5 [cmdk-item] svg{width:calc(var(--spacing)*5)}.\\[\\&_p\\]\\:leading-relaxed p{--tw-leading:var(--leading-relaxed);line-height:var(--leading-relaxed)}.\\[\\&_svg\\]\\:pointer-events-none svg{pointer-events:none}.\\[\\&_svg\\]\\:size-4 svg{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.\\[\\&_svg\\]\\:shrink-0 svg{flex-shrink:0}.\\[\\&_tr\\]\\:border-b tr{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.\\[\\&_tr\\:last-child\\]\\:border-0 tr:last-child{border-style:var(--tw-border-style);border-width:0}.\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:bg-accent:has([aria-selected]){background-color:var(--color-accent)}.first\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-l-md:first-child:has([aria-selected]){border-top-left-radius:var(--radius-md);border-bottom-left-radius:var(--radius-md)}.last\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-r-md:last-child:has([aria-selected]){border-top-right-radius:var(--radius-md);border-bottom-right-radius:var(--radius-md)}.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-outside\\)\\]\\:bg-accent\\/50:has([aria-selected].day-outside){background-color:color-mix(in srgb,rgb(var(--accent))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-outside\\)\\]\\:bg-accent\\/50:has([aria-selected].day-outside){background-color:color-mix(in oklab,var(--color-accent)50%,transparent)}}.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-range-end\\)\\]\\:rounded-r-md:has([aria-selected].day-range-end){border-top-right-radius:var(--radius-md);border-bottom-right-radius:var(--radius-md)}.\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0:has([role=checkbox]){padding-right:calc(var(--spacing)*0)}.\\[\\&\\>button\\]\\:hidden>button{display:none}.\\[\\&\\>span\\]\\:line-clamp-1>span{-webkit-line-clamp:1;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.\\[\\&\\>span\\:last-child\\]\\:truncate>span:last-child{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.\\[\\&\\>svg\\]\\:absolute>svg{position:absolute}.\\[\\&\\>svg\\]\\:top-4>svg{top:calc(var(--spacing)*4)}.\\[\\&\\>svg\\]\\:left-4>svg{left:calc(var(--spacing)*4)}.\\[\\&\\>svg\\]\\:size-3\\.5>svg{width:calc(var(--spacing)*3.5);height:calc(var(--spacing)*3.5)}.\\[\\&\\>svg\\]\\:size-4>svg{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.\\[\\&\\>svg\\]\\:h-2\\.5>svg{height:calc(var(--spacing)*2.5)}.\\[\\&\\>svg\\]\\:h-3>svg{height:calc(var(--spacing)*3)}.\\[\\&\\>svg\\]\\:w-2\\.5>svg{width:calc(var(--spacing)*2.5)}.\\[\\&\\>svg\\]\\:w-3>svg{width:calc(var(--spacing)*3)}.\\[\\&\\>svg\\]\\:shrink-0>svg{flex-shrink:0}.\\[\\&\\>svg\\]\\:text-destructive>svg{color:var(--color-destructive)}.\\[\\&\\>svg\\]\\:text-foreground>svg{color:var(--color-foreground)}.\\[\\&\\>svg\\]\\:text-muted-foreground>svg{color:var(--color-muted-foreground)}.\\[\\&\\>svg\\]\\:text-sidebar-accent-foreground>svg{color:var(--color-sidebar-accent-foreground)}.\\[\\&\\>svg\\+div\\]\\:translate-y-\\[-3px\\]>svg+div{--tw-translate-y:-3px;translate:var(--tw-translate-x)var(--tw-translate-y)}.\\[\\&\\>svg\\~\\*\\]\\:pl-7>svg~*{padding-left:calc(var(--spacing)*7)}.\\[\\&\\>tr\\]\\:last\\:border-b-0>tr:last-child{border-bottom-style:var(--tw-border-style);border-bottom-width:0}.\\[\\&\\[data-panel-group-direction\\=vertical\\]\\>div\\]\\:rotate-90[data-panel-group-direction=vertical]>div{rotate:90deg}.\\[\\&\\[data-state\\=open\\]\\>svg\\]\\:rotate-180[data-state=open]>svg{rotate:180deg}[data-side=left] .\\[\\[data-side\\=left\\]_\\&\\]\\:cursor-w-resize{cursor:w-resize}[data-side=left][data-collapsible=offcanvas] .\\[\\[data-side\\=left\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-right-2{right:calc(var(--spacing)*-2)}[data-side=left][data-state=collapsed] .\\[\\[data-side\\=left\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-e-resize,[data-side=right] .\\[\\[data-side\\=right\\]_\\&\\]\\:cursor-e-resize{cursor:e-resize}[data-side=right][data-collapsible=offcanvas] .\\[\\[data-side\\=right\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-left-2{left:calc(var(--spacing)*-2)}[data-side=right][data-state=collapsed] .\\[\\[data-side\\=right\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-w-resize{cursor:w-resize}}:root{--background:248 250 252;--foreground:0 0 0;--card:255 255 255;--card-foreground:30 41 59;--popover:255 255 255;--popover-foreground:30 41 59;--primary:0 97 123;--primary-light:51 140 168;--primary-dark:0 70 89;--primary-foreground:255 255 255;--secondary:0 0 0;--secondary-foreground:71 85 105;--muted:241 245 249;--muted-foreground:100 116 139;--accent:51 140 168;--accent-foreground:0 97 123;--destructive:239 68 68;--destructive-foreground:255 255 255;--border:0 0 0;--input:226 232 240;--ring:0 97 123;--chart-1:20 184 166;--chart-2:14 165 233;--chart-3:30 58 138;--sidebar:255 255 255;--sidebar-foreground:71 85 105;--sidebar-primary:0 97 123;--sidebar-primary-foreground:255 255 255;--sidebar-accent:51 140 168;--sidebar-accent-foreground:0 97 123;--sidebar-border:226 232 240;--sidebar-ring:0 97 123;--shadow-2xs:0 1px 3px 0px #0000000d;--shadow-xs:0 1px 3px 0px #0000000d;--shadow-sm:0 1px 3px 0px #0000001a,0 1px 2px -1px #0000001a;--shadow:0 1px 3px 0px #0000001a,0 1px 2px -1px #0000001a;--shadow-md:0 1px 3px 0px #0000001a,0 2px 4px -1px #0000001a;--shadow-lg:0 1px 3px 0px #0000001a,0 4px 6px -1px #0000001a;--shadow-xl:0 1px 3px 0px #0000001a,0 8px 10px -1px #0000001a;--shadow-2xl:0 1px 3px 0px #00000040}@keyframes accordion-down{0%{height:0}to{height:var(--radix-accordion-content-height)}}@keyframes accordion-up{0%{height:var(--radix-accordion-content-height)}to{height:0}}@keyframes fade-in{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}@keyframes slide-in{0%{opacity:0;transform:translate(-10px)}to{opacity:1;transform:translate(0)}}@keyframes glow{0%,to{box-shadow:0 0 20px #14b8a64d}50%{box-shadow:0 0 30px #14b8a680}}@keyframes float{0%,to{transform:translateY(0)}50%{transform:translateY(-5px)}}@keyframes scroll{0%{transform:translate(0)}to{transform:translate(-50%)}}.animate-scroll{animation:30s linear infinite scroll}.animate-scroll:hover{animation-play-state:paused}.container-responsive{padding-left:1rem;padding-right:1rem}@media (min-width:640px){.container-responsive{padding-left:1.5rem;padding-right:1.5rem}}@media (min-width:1024px){.container-responsive{padding-left:2rem;padding-right:2rem}}.grid-responsive{gap:1rem}@media (min-width:640px){.grid-responsive{gap:1.5rem}}@media (min-width:1024px){.grid-responsive{gap:2rem}}.text-responsive-xs{font-size:.75rem;line-height:1rem}@media (min-width:640px){.text-responsive-xs{font-size:.875rem;line-height:1.25rem}}.text-responsive-sm{font-size:.875rem;line-height:1.25rem}@media (min-width:640px){.text-responsive-sm{font-size:1rem;line-height:1.5rem}}.text-responsive-base{font-size:1rem;line-height:1.5rem}@media (min-width:640px){.text-responsive-base{font-size:1.125rem;line-height:1.75rem}}.text-responsive-lg{font-size:1.125rem;line-height:1.75rem}@media (min-width:640px){.text-responsive-lg{font-size:1.25rem;line-height:1.75rem}}@media (min-width:1024px){.text-responsive-lg{font-size:1.5rem;line-height:2rem}}.text-responsive-xl{font-size:1.25rem;line-height:1.75rem}@media (min-width:640px){.text-responsive-xl{font-size:1.5rem;line-height:2rem}}@media (min-width:1024px){.text-responsive-xl{font-size:1.875rem;line-height:2.25rem}}.text-responsive-2xl{font-size:1.5rem;line-height:2rem}@media (min-width:640px){.text-responsive-2xl{font-size:1.875rem;line-height:2.25rem}}@media (min-width:1024px){.text-responsive-2xl{font-size:2.25rem;line-height:2.5rem}}.text-responsive-3xl{font-size:1.875rem;line-height:2.25rem}@media (min-width:640px){.text-responsive-3xl{font-size:2.25rem;line-height:2.5rem}}@media (min-width:1024px){.text-responsive-3xl{font-size:3rem;line-height:1}}.space-responsive>*+*{margin-top:1rem}@media (min-width:640px){.space-responsive>*+*{margin-top:1.5rem}}@media (min-width:1024px){.space-responsive>*+*{margin-top:2rem}}.p-responsive{padding:1rem}@media (min-width:640px){.p-responsive{padding:1.5rem}}@media (min-width:1024px){.p-responsive{padding:2rem}}.px-responsive{padding-left:1rem;padding-right:1rem}@media (min-width:640px){.px-responsive{padding-left:1.5rem;padding-right:1.5rem}}@media (min-width:1024px){.px-responsive{padding-left:2rem;padding-right:2rem}}.py-responsive{padding-top:1rem;padding-bottom:1rem}@media (min-width:640px){.py-responsive{padding-top:1.5rem;padding-bottom:1.5rem}}@media (min-width:1024px){.py-responsive{padding-top:2rem;padding-bottom:2rem}}.card-responsive{padding:1rem}@media (min-width:640px){.card-responsive{padding:1.5rem}}@media (min-width:1024px){.card-responsive{padding:2rem}}.flex-responsive{flex-direction:column;display:flex}@media (min-width:640px){.flex-responsive{flex-direction:row}}.hidden-mobile{display:none}@media (min-width:640px){.hidden-mobile{display:block}}.mobile-only{display:block}@media (min-width:640px){.mobile-only{display:none}}.btn-responsive{padding:.5rem 1rem}@media (min-width:640px){.btn-responsive{padding:.75rem 1.5rem}}.input-responsive{width:100%;padding:.5rem .75rem}@media (min-width:640px){.input-responsive{padding:.75rem 1rem}}.safe-area-padding{padding-left:env(safe-area-inset-left);padding-right:env(safe-area-inset-right);padding-top:env(safe-area-inset-top);padding-bottom:env(safe-area-inset-bottom)}.table-responsive{margin-left:-1rem;margin-right:-1rem;overflow-x:auto}@media (min-width:640px){.table-responsive{margin-left:0;margin-right:0}}.table-responsive table{min-width:100%}.modal-responsive{width:100%;max-width:32rem;margin-left:1rem;margin-right:1rem}@media (min-width:640px){.modal-responsive{margin-left:auto;margin-right:auto}}.sidebar-responsive{width:100%}@media (min-width:640px){.sidebar-responsive{width:16rem}}@media (min-width:1024px){.sidebar-responsive{width:18rem}}.sidebar-container{width:100%;min-height:100vh;display:flex;overflow:hidden}.sidebar-main-content{flex-direction:column;flex:1;width:100%;min-width:0;display:flex}@media (min-width:1024px) and (max-width:1279px){:root{--sidebar-width-responsive:14rem}}@media (min-width:1280px){:root{--sidebar-width-responsive:16rem}}.form-responsive>*+*{margin-top:1rem}@media (min-width:640px){.form-responsive>*+*{margin-top:1.5rem}}.form-responsive .form-group{flex-direction:column;display:flex}.form-responsive .form-group>*+*{margin-top:.5rem}@media (min-width:640px){.form-responsive .form-group{flex-direction:row;align-items:center}.form-responsive .form-group>*+*{margin-top:0;margin-left:1rem}}.form-responsive label{font-size:.875rem;font-weight:500}@media (min-width:640px){.form-responsive label{flex-shrink:0;width:8rem}}.form-responsive input,.form-responsive select,.form-responsive textarea{width:100%}@media (min-width:640px){.form-responsive input,.form-responsive select,.form-responsive textarea{flex:1}}.nav-responsive{flex-direction:column;display:flex}.nav-responsive>*+*{margin-top:.5rem}@media (min-width:640px){.nav-responsive{flex-direction:row}.nav-responsive>*+*{margin-top:0;margin-left:1rem}}.cards-responsive{grid-template-columns:repeat(1,minmax(0,1fr));gap:1rem;display:grid}@media (min-width:640px){.cards-responsive{grid-template-columns:repeat(2,minmax(0,1fr));gap:1.5rem}}@media (min-width:1024px){.cards-responsive{grid-template-columns:repeat(3,minmax(0,1fr))}}@media (min-width:1280px){.cards-responsive{grid-template-columns:repeat(4,minmax(0,1fr))}}.dashboard-responsive{flex-direction:column;min-height:100vh;display:flex}@media (min-width:1024px){.dashboard-responsive{flex-direction:row}}.dashboard-content{flex:1;padding:1rem;overflow:auto}@media (min-width:640px){.dashboard-content{padding:1.5rem}}@media (min-width:1024px){.dashboard-content{padding:2rem}}.heading-responsive-1{font-size:1.5rem;font-weight:700;line-height:2rem}@media (min-width:640px){.heading-responsive-1{font-size:1.875rem;line-height:2.25rem}}@media (min-width:1024px){.heading-responsive-1{font-size:2.25rem;line-height:2.5rem}}.heading-responsive-2{font-size:1.25rem;font-weight:600;line-height:1.75rem}@media (min-width:640px){.heading-responsive-2{font-size:1.5rem;line-height:2rem}}@media (min-width:1024px){.heading-responsive-2{font-size:1.875rem;line-height:2.25rem}}.heading-responsive-3{font-size:1.125rem;font-weight:500;line-height:1.75rem}@media (min-width:640px){.heading-responsive-3{font-size:1.25rem;line-height:1.75rem}}@media (min-width:1024px){.heading-responsive-3{font-size:1.5rem;line-height:2rem}}.button-group-responsive{flex-direction:column;display:flex}.button-group-responsive>*+*{margin-top:.5rem}@media (min-width:640px){.button-group-responsive{flex-direction:row}.button-group-responsive>*+*{margin-top:0;margin-left:.5rem}}.image-responsive{-o-object-fit:cover;object-fit:cover;width:100%;height:auto}.list-responsive>*+*{margin-top:.5rem}@media (min-width:640px){.list-responsive>*+*{margin-top:.75rem}}.list-item-responsive{flex-direction:column;padding:.75rem;display:flex}@media (min-width:640px){.list-item-responsive{flex-direction:row;justify-content:space-between;align-items:center;padding:1rem}}@media (max-width:639px){.container{padding-left:1rem;padding-right:1rem}.nav-horizontal{flex-direction:column;display:flex}.nav-horizontal>*+*{margin-top:.5rem}.btn-mobile-full{width:100%}.text-mobile-sm{font-size:.875rem;line-height:1.25rem}.complex-layout{display:none}.mobile-layout{display:block}}@media (min-width:641px) and (max-width:1024px){.tablet-grid{grid-template-columns:repeat(2,minmax(0,1fr))}.tablet-sidebar{width:14rem}}@media (min-width:1025px){.desktop-hover:hover{transition:transform .2s;transform:scale(1.05)}.desktop-grid{grid-template-columns:repeat(4,minmax(0,1fr))}}@property --tw-translate-x{syntax:\"*\";inherits:false;initial-value:0}@property --tw-translate-y{syntax:\"*\";inherits:false;initial-value:0}@property --tw-translate-z{syntax:\"*\";inherits:false;initial-value:0}@property --tw-rotate-x{syntax:\"*\";inherits:false}@property --tw-rotate-y{syntax:\"*\";inherits:false}@property --tw-rotate-z{syntax:\"*\";inherits:false}@property --tw-skew-x{syntax:\"*\";inherits:false}@property --tw-skew-y{syntax:\"*\";inherits:false}@property --tw-space-y-reverse{syntax:\"*\";inherits:false;initial-value:0}@property --tw-space-x-reverse{syntax:\"*\";inherits:false;initial-value:0}@property --tw-border-style{syntax:\"*\";inherits:false;initial-value:solid}@property --tw-gradient-position{syntax:\"*\";inherits:false}@property --tw-gradient-from{syntax:\"<color>\";inherits:false;initial-value:#0000}@property --tw-gradient-via{syntax:\"<color>\";inherits:false;initial-value:#0000}@property --tw-gradient-to{syntax:\"<color>\";inherits:false;initial-value:#0000}@property --tw-gradient-stops{syntax:\"*\";inherits:false}@property --tw-gradient-via-stops{syntax:\"*\";inherits:false}@property --tw-gradient-from-position{syntax:\"<length-percentage>\";inherits:false;initial-value:0%}@property --tw-gradient-via-position{syntax:\"<length-percentage>\";inherits:false;initial-value:50%}@property --tw-gradient-to-position{syntax:\"<length-percentage>\";inherits:false;initial-value:100%}@property --tw-leading{syntax:\"*\";inherits:false}@property --tw-font-weight{syntax:\"*\";inherits:false}@property --tw-tracking{syntax:\"*\";inherits:false}@property --tw-ordinal{syntax:\"*\";inherits:false}@property --tw-slashed-zero{syntax:\"*\";inherits:false}@property --tw-numeric-figure{syntax:\"*\";inherits:false}@property --tw-numeric-spacing{syntax:\"*\";inherits:false}@property --tw-numeric-fraction{syntax:\"*\";inherits:false}@property --tw-shadow{syntax:\"*\";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:\"*\";inherits:false}@property --tw-shadow-alpha{syntax:\"<percentage>\";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:\"*\";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:\"*\";inherits:false}@property --tw-inset-shadow-alpha{syntax:\"<percentage>\";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:\"*\";inherits:false}@property --tw-ring-shadow{syntax:\"*\";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:\"*\";inherits:false}@property --tw-inset-ring-shadow{syntax:\"*\";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:\"*\";inherits:false}@property --tw-ring-offset-width{syntax:\"<length>\";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:\"*\";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:\"*\";inherits:false;initial-value:0 0 #0000}@property --tw-outline-style{syntax:\"*\";inherits:false;initial-value:solid}@property --tw-blur{syntax:\"*\";inherits:false}@property --tw-brightness{syntax:\"*\";inherits:false}@property --tw-contrast{syntax:\"*\";inherits:false}@property --tw-grayscale{syntax:\"*\";inherits:false}@property --tw-hue-rotate{syntax:\"*\";inherits:false}@property --tw-invert{syntax:\"*\";inherits:false}@property --tw-opacity{syntax:\"*\";inherits:false}@property --tw-saturate{syntax:\"*\";inherits:false}@property --tw-sepia{syntax:\"*\";inherits:false}@property --tw-drop-shadow{syntax:\"*\";inherits:false}@property --tw-drop-shadow-color{syntax:\"*\";inherits:false}@property --tw-drop-shadow-alpha{syntax:\"<percentage>\";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:\"*\";inherits:false}@property --tw-backdrop-blur{syntax:\"*\";inherits:false}@property --tw-backdrop-brightness{syntax:\"*\";inherits:false}@property --tw-backdrop-contrast{syntax:\"*\";inherits:false}@property --tw-backdrop-grayscale{syntax:\"*\";inherits:false}@property --tw-backdrop-hue-rotate{syntax:\"*\";inherits:false}@property --tw-backdrop-invert{syntax:\"*\";inherits:false}@property --tw-backdrop-opacity{syntax:\"*\";inherits:false}@property --tw-backdrop-saturate{syntax:\"*\";inherits:false}@property --tw-backdrop-sepia{syntax:\"*\";inherits:false}@property --tw-duration{syntax:\"*\";inherits:false}@property --tw-ease{syntax:\"*\";inherits:false}@property --tw-content{syntax:\"*\";inherits:false;initial-value:\"\"}@keyframes spin{to{transform:rotate(360deg)}}@keyframes pulse{50%{opacity:.5}}\n\n", "/* cyrillic-ext */\n@font-face {\n  font-family: 'JetBrains Mono';\n  font-style: normal;\n  font-weight: 100 800;\n  font-display: swap;\n  src: url(/_next/static/media/886030b0b59bc5a7-s.woff2) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'JetBrains Mono';\n  font-style: normal;\n  font-weight: 100 800;\n  font-display: swap;\n  src: url(/_next/static/media/0aa834ed78bf6d07-s.woff2) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek */\n@font-face {\n  font-family: 'JetBrains Mono';\n  font-style: normal;\n  font-weight: 100 800;\n  font-display: swap;\n  src: url(/_next/static/media/67957d42bae0796d-s.woff2) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'JetBrains Mono';\n  font-style: normal;\n  font-weight: 100 800;\n  font-display: swap;\n  src: url(/_next/static/media/f911b923c6adde36-s.woff2) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'JetBrains Mono';\n  font-style: normal;\n  font-weight: 100 800;\n  font-display: swap;\n  src: url(/_next/static/media/939c4f875ee75fbb-s.woff2) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'JetBrains Mono';\n  font-style: normal;\n  font-weight: 100 800;\n  font-display: swap;\n  src: url(/_next/static/media/bb3ef058b751a6ad-s.woff2) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n", "@font-face {\nfont-family: frutiger;\nsrc: url(/_next/static/media/7f0892586759a7e3-s.p.woff) format('woff');\nfont-display: swap;\nfont-weight: 700;\nfont-style: normal;\n}\n", "/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\n@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-space-y-reverse:0;--tw-space-x-reverse:0;--tw-border-style:solid;--tw-gradient-position:initial;--tw-gradient-from:#0000;--tw-gradient-via:#0000;--tw-gradient-to:#0000;--tw-gradient-stops:initial;--tw-gradient-via-stops:initial;--tw-gradient-from-position:0%;--tw-gradient-via-position:50%;--tw-gradient-to-position:100%;--tw-leading:initial;--tw-font-weight:initial;--tw-tracking:initial;--tw-ordinal:initial;--tw-slashed-zero:initial;--tw-numeric-figure:initial;--tw-numeric-spacing:initial;--tw-numeric-fraction:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial;--tw-duration:initial;--tw-ease:initial;--tw-content:\"\"}}}@layer theme{:root,:host{--font-sans:ui-sans-serif,system-ui,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Segoe UI Symbol\",\"Noto Color Emoji\";--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,\"Liberation Mono\",\"Courier New\",monospace;--color-red-50:oklch(97.1% .013 17.38);--color-red-200:oklch(88.5% .062 18.334);--color-red-300:oklch(80.8% .114 19.571);--color-red-400:oklch(70.4% .191 22.216);--color-red-600:oklch(57.7% .245 27.325);--color-red-700:oklch(50.5% .213 27.518);--color-red-900:oklch(39.6% .141 25.723);--color-green-600:oklch(62.7% .194 149.214);--color-green-700:oklch(52.7% .154 150.069);--color-blue-200:oklch(88.2% .059 254.128);--color-blue-300:oklch(80.9% .105 251.813);--color-blue-600:oklch(54.6% .245 262.881);--color-blue-700:oklch(48.8% .243 264.376);--color-slate-50:oklch(98.4% .003 247.858);--color-slate-100:oklch(96.8% .007 247.896);--color-slate-200:oklch(92.9% .013 255.508);--color-slate-500:oklch(55.4% .046 257.417);--color-slate-600:oklch(44.6% .043 257.281);--color-slate-800:oklch(27.9% .041 260.031);--color-slate-900:oklch(20.8% .042 265.755);--color-gray-50:oklch(98.5% .002 247.839);--color-gray-200:oklch(92.8% .006 264.531);--color-gray-400:oklch(70.7% .022 261.325);--color-gray-500:oklch(55.1% .027 264.364);--color-gray-600:oklch(44.6% .03 256.802);--color-gray-700:oklch(37.3% .034 259.733);--color-gray-800:oklch(27.8% .033 256.848);--color-gray-900:oklch(21% .034 264.665);--color-black:#000;--color-white:#fff;--spacing:.25rem;--container-sm:24rem;--container-md:28rem;--container-lg:32rem;--container-4xl:56rem;--container-7xl:80rem;--text-xs:.75rem;--text-xs--line-height:calc(1/.75);--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-base:1rem;--text-base--line-height:calc(1.5/1);--text-lg:1.125rem;--text-lg--line-height:calc(1.75/1.125);--text-xl:1.25rem;--text-xl--line-height:calc(1.75/1.25);--text-2xl:1.5rem;--text-2xl--line-height:calc(2/1.5);--text-3xl:1.875rem;--text-3xl--line-height:calc(2.25/1.875);--text-4xl:2.25rem;--text-4xl--line-height:calc(2.5/2.25);--font-weight-normal:400;--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700;--tracking-tight:-.025em;--tracking-wider:.05em;--tracking-widest:.1em;--leading-tight:1.25;--leading-relaxed:1.625;--radius-sm:calc(var(--radius) - 4px);--radius-md:calc(var(--radius) - 2px);--radius-lg:var(--radius);--radius-xl:.75rem;--radius-2xl:1rem;--radius-3xl:1.5rem;--radius-4xl:2rem;--ease-out:cubic-bezier(0,0,.2,1);--ease-in-out:cubic-bezier(.4,0,.2,1);--animate-spin:spin 1s linear infinite;--animate-pulse:pulse 2s cubic-bezier(.4,0,.6,1)infinite;--blur-sm:8px;--blur-md:12px;--blur-3xl:64px;--aspect-video:16/9;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono);--radius:.625rem;--animate-accordion-down:accordion-down .2s ease-out;--animate-accordion-up:accordion-up .2s ease-out;--color-platyfend-500:#00617b;--color-platyfend-600:#005569;--color-background:rgb(var(--background));--color-foreground:rgb(var(--foreground));--color-card:rgb(var(--card));--color-card-foreground:rgb(var(--card-foreground));--color-popover:rgb(var(--popover));--color-popover-foreground:rgb(var(--popover-foreground));--color-primary:rgb(var(--primary));--color-primary-foreground:rgb(var(--primary-foreground));--color-secondary:rgb(var(--secondary));--color-secondary-foreground:rgb(var(--secondary-foreground));--color-muted:rgb(var(--muted));--color-muted-foreground:rgb(var(--muted-foreground));--color-accent:rgb(var(--accent));--color-accent-foreground:rgb(var(--accent-foreground));--color-destructive:rgb(var(--destructive));--color-destructive-foreground:rgb(var(--destructive-foreground));--color-border:rgb(var(--border));--color-input:rgb(var(--input));--color-ring:rgb(var(--ring));--color-sidebar:rgb(var(--sidebar));--color-sidebar-foreground:rgb(var(--sidebar-foreground));--color-sidebar-accent:rgb(var(--sidebar-accent));--color-sidebar-accent-foreground:rgb(var(--sidebar-accent-foreground));--color-sidebar-border:rgb(var(--sidebar-border));--color-sidebar-ring:rgb(var(--sidebar-ring))}}@layer base{*,:after,:before,::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}html,:host{-webkit-text-size-adjust:100%;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Segoe UI Symbol\",\"Noto Color Emoji\");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,\"Liberation Mono\",\"Courier New\",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::placeholder{opacity:1}@supports (not ((-webkit-appearance:-apple-pay-button))) or (contain-intrinsic-size:1px){::placeholder{color:currentColor}@supports (color:color-mix(in lab, red, red)){::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit{padding-block:0}::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-day-field{padding-block:0}::-webkit-datetime-edit-hour-field{padding-block:0}::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-millisecond-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){appearance:button}::file-selector-button{appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}}@layer components;@layer utilities{.pointer-events-auto{pointer-events:auto}.pointer-events-none{pointer-events:none}.invisible{visibility:hidden}.sr-only{clip:rect(0,0,0,0);white-space:nowrap;border-width:0;width:1px;height:1px;margin:-1px;padding:0;position:absolute;overflow:hidden}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.static{position:static}.inset-0{inset:calc(var(--spacing)*0)}.inset-x-0{inset-inline:calc(var(--spacing)*0)}.inset-y-0{inset-block:calc(var(--spacing)*0)}.-top-12{top:calc(var(--spacing)*-12)}.top-0{top:calc(var(--spacing)*0)}.top-1\\.5{top:calc(var(--spacing)*1.5)}.top-1\\/2{top:50%}.top-1\\/4{top:25%}.top-2{top:calc(var(--spacing)*2)}.top-3\\.5{top:calc(var(--spacing)*3.5)}.top-4{top:calc(var(--spacing)*4)}.top-\\[1px\\]{top:1px}.top-\\[50\\%\\]{top:50%}.top-\\[60\\%\\]{top:60%}.top-full{top:100%}.-right-12{right:calc(var(--spacing)*-12)}.right-0{right:calc(var(--spacing)*0)}.right-1{right:calc(var(--spacing)*1)}.right-1\\.5{right:calc(var(--spacing)*1.5)}.right-1\\/2{right:50%}.right-1\\/4{right:25%}.right-2{right:calc(var(--spacing)*2)}.right-3{right:calc(var(--spacing)*3)}.right-4{right:calc(var(--spacing)*4)}.-bottom-12{bottom:calc(var(--spacing)*-12)}.bottom-0{bottom:calc(var(--spacing)*0)}.bottom-1\\.5{bottom:calc(var(--spacing)*1.5)}.bottom-1\\/4{bottom:25%}.-left-12{left:calc(var(--spacing)*-12)}.left-0{left:calc(var(--spacing)*0)}.left-1{left:calc(var(--spacing)*1)}.left-1\\.5{left:calc(var(--spacing)*1.5)}.left-1\\/2{left:50%}.left-1\\/4{left:25%}.left-2{left:calc(var(--spacing)*2)}.left-\\[50\\%\\]{left:50%}.z-10{z-index:10}.z-20{z-index:20}.z-50{z-index:50}.z-\\[1\\]{z-index:1}.z-\\[100\\]{z-index:100}.container{width:100%}@media (min-width:475px){.container{max-width:475px}}@media (min-width:640px){.container{max-width:640px}}@media (min-width:768px){.container{max-width:768px}}@media (min-width:1024px){.container{max-width:1024px}}@media (min-width:1280px){.container{max-width:1280px}}@media (min-width:1536px){.container{max-width:1536px}}.m-4{margin:calc(var(--spacing)*4)}.-mx-1{margin-inline:calc(var(--spacing)*-1)}.mx-2{margin-inline:calc(var(--spacing)*2)}.mx-3\\.5{margin-inline:calc(var(--spacing)*3.5)}.mx-auto{margin-inline:auto}.my-0\\.5{margin-block:calc(var(--spacing)*.5)}.my-1{margin-block:calc(var(--spacing)*1)}.-mt-4{margin-top:calc(var(--spacing)*-4)}.mt-0\\.5{margin-top:calc(var(--spacing)*.5)}.mt-1{margin-top:calc(var(--spacing)*1)}.mt-1\\.5{margin-top:calc(var(--spacing)*1.5)}.mt-2{margin-top:calc(var(--spacing)*2)}.mt-3{margin-top:calc(var(--spacing)*3)}.mt-4{margin-top:calc(var(--spacing)*4)}.mt-6{margin-top:calc(var(--spacing)*6)}.mt-8{margin-top:calc(var(--spacing)*8)}.mt-24{margin-top:calc(var(--spacing)*24)}.mt-auto{margin-top:auto}.mr-0\\.5{margin-right:calc(var(--spacing)*.5)}.mr-2{margin-right:calc(var(--spacing)*2)}.mr-3{margin-right:calc(var(--spacing)*3)}.mb-1{margin-bottom:calc(var(--spacing)*1)}.mb-2{margin-bottom:calc(var(--spacing)*2)}.mb-3{margin-bottom:calc(var(--spacing)*3)}.mb-4{margin-bottom:calc(var(--spacing)*4)}.mb-6{margin-bottom:calc(var(--spacing)*6)}.mb-8{margin-bottom:calc(var(--spacing)*8)}.-ml-4{margin-left:calc(var(--spacing)*-4)}.ml-0\\.5{margin-left:calc(var(--spacing)*.5)}.ml-1{margin-left:calc(var(--spacing)*1)}.ml-auto{margin-left:auto}.block{display:block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.inline{display:inline}.inline-flex{display:inline-flex}.table{display:table}.aspect-square{aspect-ratio:1}.aspect-video{aspect-ratio:var(--aspect-video)}.size-4{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.h-1\\.5{height:calc(var(--spacing)*1.5)}.h-2{height:calc(var(--spacing)*2)}.h-2\\.5{height:calc(var(--spacing)*2.5)}.h-3{height:calc(var(--spacing)*3)}.h-3\\.5{height:calc(var(--spacing)*3.5)}.h-4{height:calc(var(--spacing)*4)}.h-5{height:calc(var(--spacing)*5)}.h-6{height:calc(var(--spacing)*6)}.h-7{height:calc(var(--spacing)*7)}.h-8{height:calc(var(--spacing)*8)}.h-9{height:calc(var(--spacing)*9)}.h-10{height:calc(var(--spacing)*10)}.h-11{height:calc(var(--spacing)*11)}.h-12{height:calc(var(--spacing)*12)}.h-96{height:calc(var(--spacing)*96)}.h-\\[1px\\]{height:1px}.h-\\[800px\\]{height:800px}.h-\\[var\\(--radix-navigation-menu-viewport-height\\)\\]{height:var(--radix-navigation-menu-viewport-height)}.h-\\[var\\(--radix-select-trigger-height\\)\\]{height:var(--radix-select-trigger-height)}.h-auto{height:auto}.h-full{height:100%}.h-px{height:1px}.h-screen{height:100vh}.h-svh{height:100svh}.max-h-32{max-height:calc(var(--spacing)*32)}.max-h-96{max-height:calc(var(--spacing)*96)}.max-h-\\[300px\\]{max-height:300px}.max-h-screen{max-height:100vh}.min-h-0{min-height:calc(var(--spacing)*0)}.min-h-\\[12rem\\]{min-height:12rem}.min-h-\\[80px\\]{min-height:80px}.min-h-\\[400px\\]{min-height:400px}.min-h-screen{min-height:100vh}.min-h-svh{min-height:100svh}.w-0{width:calc(var(--spacing)*0)}.w-1{width:calc(var(--spacing)*1)}.w-2{width:calc(var(--spacing)*2)}.w-2\\.5{width:calc(var(--spacing)*2.5)}.w-3{width:calc(var(--spacing)*3)}.w-3\\.5{width:calc(var(--spacing)*3.5)}.w-3\\/4{width:75%}.w-4{width:calc(var(--spacing)*4)}.w-5{width:calc(var(--spacing)*5)}.w-7{width:calc(var(--spacing)*7)}.w-8{width:calc(var(--spacing)*8)}.w-9{width:calc(var(--spacing)*9)}.w-10{width:calc(var(--spacing)*10)}.w-11{width:calc(var(--spacing)*11)}.w-12{width:calc(var(--spacing)*12)}.w-48{width:calc(var(--spacing)*48)}.w-56{width:calc(var(--spacing)*56)}.w-64{width:calc(var(--spacing)*64)}.w-72{width:calc(var(--spacing)*72)}.w-96{width:calc(var(--spacing)*96)}.w-\\[--radix-dropdown-menu-trigger-width\\]{width:--radix-dropdown-menu-trigger-width}.w-\\[--sidebar-width-tablet\\]{width:--sidebar-width-tablet}.w-\\[--sidebar-width\\]{width:--sidebar-width}.w-\\[1px\\]{width:1px}.w-\\[100px\\]{width:100px}.w-\\[240px\\]{width:240px}.w-\\[800px\\]{width:800px}.w-auto{width:auto}.w-full{width:100%}.w-max{width:max-content}.w-px{width:1px}.max-w-4xl{max-width:var(--container-4xl)}.max-w-7xl{max-width:var(--container-7xl)}.max-w-96{max-width:calc(var(--spacing)*96)}.max-w-\\[--skeleton-width\\]{max-width:--skeleton-width}.max-w-lg{max-width:var(--container-lg)}.max-w-max{max-width:max-content}.max-w-md{max-width:var(--container-md)}.min-w-0{min-width:calc(var(--spacing)*0)}.min-w-5{min-width:calc(var(--spacing)*5)}.min-w-56{min-width:calc(var(--spacing)*56)}.min-w-\\[8rem\\]{min-width:8rem}.min-w-\\[12rem\\]{min-width:12rem}.min-w-\\[var\\(--radix-select-trigger-width\\)\\]{min-width:var(--radix-select-trigger-width)}.flex-1{flex:1}.flex-shrink-0,.shrink-0{flex-shrink:0}.grow{flex-grow:1}.grow-0{flex-grow:0}.basis-full{flex-basis:100%}.caption-bottom{caption-side:bottom}.border-collapse{border-collapse:collapse}.-translate-x-1\\/2{--tw-translate-x:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-x-px{--tw-translate-x:-1px;translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-x-\\[-50\\%\\]{--tw-translate-x:-50%;translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-x-px{--tw-translate-x:1px;translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-y-1\\/2{--tw-translate-y:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-y-\\[-50\\%\\]{--tw-translate-y:-50%;translate:var(--tw-translate-x)var(--tw-translate-y)}.rotate-45{rotate:45deg}.rotate-90{rotate:90deg}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.animate-pulse{animation:var(--animate-pulse)}.animate-spin{animation:var(--animate-spin)}.cursor-default{cursor:default}.cursor-pointer{cursor:pointer}.touch-none{touch-action:none}.resize{resize:both}.list-none{list-style-type:none}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.flex-col{flex-direction:column}.flex-col-reverse{flex-direction:column-reverse}.flex-row{flex-direction:row}.flex-wrap{flex-wrap:wrap}.items-center{align-items:center}.items-end{align-items:flex-end}.items-start{align-items:flex-start}.items-stretch{align-items:stretch}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.justify-start{justify-content:flex-start}.gap-1{gap:calc(var(--spacing)*1)}.gap-1\\.5{gap:calc(var(--spacing)*1.5)}.gap-2{gap:calc(var(--spacing)*2)}.gap-3{gap:calc(var(--spacing)*3)}.gap-4{gap:calc(var(--spacing)*4)}:where(.space-y-0>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*0)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*0)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-1>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-1\\.5>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*1.5)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*1.5)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-2>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*2)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-3>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*3)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-4>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-6>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*6)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-x-1>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*1)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-2>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-4>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*4)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-x-reverse)))}.truncate{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.overflow-x-hidden{overflow-x:hidden}.overflow-y-auto{overflow-y:auto}.rounded{border-radius:var(--radius)}.rounded-2xl{border-radius:var(--radius-2xl)}.rounded-3xl{border-radius:var(--radius-3xl)}.rounded-4xl{border-radius:var(--radius-4xl)}.rounded-\\[2px\\]{border-radius:2px}.rounded-\\[inherit\\]{border-radius:inherit}.rounded-full{border-radius:3.40282e38px}.rounded-lg{border-radius:var(--radius-lg)}.rounded-md{border-radius:var(--radius-md)}.rounded-sm{border-radius:var(--radius-sm)}.rounded-xl{border-radius:var(--radius-xl)}.rounded-t-\\[10px\\]{border-top-left-radius:10px;border-top-right-radius:10px}.rounded-tl-sm{border-top-left-radius:var(--radius-sm)}.border{border-style:var(--tw-border-style);border-width:1px}.border-0{border-style:var(--tw-border-style);border-width:0}.border-2{border-style:var(--tw-border-style);border-width:2px}.border-\\[1\\.5px\\]{border-style:var(--tw-border-style);border-width:1.5px}.border-y{border-block-style:var(--tw-border-style);border-block-width:1px}.border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.border-r{border-right-style:var(--tw-border-style);border-right-width:1px}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-b-2{border-bottom-style:var(--tw-border-style);border-bottom-width:2px}.border-l{border-left-style:var(--tw-border-style);border-left-width:1px}.border-dashed{--tw-border-style:dashed;border-style:dashed}.border-none{--tw-border-style:none;border-style:none}.border-\\[\\#00617b\\]{border-color:#00617b}.border-\\[--color-border\\]{border-color:--color-border}.border-border\\/50{border-color:color-mix(in srgb,rgb(var(--border))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.border-border\\/50{border-color:color-mix(in oklab,var(--color-border)50%,transparent)}}.border-destructive{border-color:var(--color-destructive)}.border-destructive\\/50{border-color:color-mix(in srgb,rgb(var(--destructive))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.border-destructive\\/50{border-color:color-mix(in oklab,var(--color-destructive)50%,transparent)}}.border-gray-200{border-color:var(--color-gray-200)}.border-input{border-color:var(--color-input)}.border-primary{border-color:var(--color-primary)}.border-red-200{border-color:var(--color-red-200)}.border-sidebar-border{border-color:var(--color-sidebar-border)}.border-slate-100{border-color:var(--color-slate-100)}.border-slate-200{border-color:var(--color-slate-200)}.border-slate-200\\/30{border-color:#e2e8f04d}@supports (color:color-mix(in lab, red, red)){.border-slate-200\\/30{border-color:color-mix(in oklab,var(--color-slate-200)30%,transparent)}}.border-transparent{border-color:#0000}.border-white\\/20{border-color:#fff3}@supports (color:color-mix(in lab, red, red)){.border-white\\/20{border-color:color-mix(in oklab,var(--color-white)20%,transparent)}}.border-t-transparent{border-top-color:#0000}.border-l-transparent{border-left-color:#0000}.bg-\\[--color-bg\\]{background-color:--color-bg}.bg-accent{background-color:var(--color-accent)}.bg-background{background-color:var(--color-background)}.bg-black\\/80{background-color:#000c}@supports (color:color-mix(in lab, red, red)){.bg-black\\/80{background-color:color-mix(in oklab,var(--color-black)80%,transparent)}}.bg-border{background-color:var(--color-border)}.bg-card{background-color:var(--color-card)}.bg-destructive{background-color:var(--color-destructive)}.bg-foreground{background-color:var(--color-foreground)}.bg-gray-50{background-color:var(--color-gray-50)}.bg-muted{background-color:var(--color-muted)}.bg-muted\\/50{background-color:color-mix(in srgb,rgb(var(--muted))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.bg-muted\\/50{background-color:color-mix(in oklab,var(--color-muted)50%,transparent)}}.bg-platyfend-500{background-color:var(--color-platyfend-500)}.bg-popover{background-color:var(--color-popover)}.bg-primary{background-color:var(--color-primary)}.bg-primary\\/5{background-color:color-mix(in srgb,rgb(var(--primary))5%,transparent)}@supports (color:color-mix(in lab, red, red)){.bg-primary\\/5{background-color:color-mix(in oklab,var(--color-primary)5%,transparent)}}.bg-red-50{background-color:var(--color-red-50)}.bg-secondary{background-color:var(--color-secondary)}.bg-secondary\\/5{background-color:color-mix(in srgb,rgb(var(--secondary))5%,transparent)}@supports (color:color-mix(in lab, red, red)){.bg-secondary\\/5{background-color:color-mix(in oklab,var(--color-secondary)5%,transparent)}}.bg-sidebar{background-color:var(--color-sidebar)}.bg-sidebar-border{background-color:var(--color-sidebar-border)}.bg-slate-50{background-color:var(--color-slate-50)}.bg-transparent{background-color:#0000}.bg-white{background-color:var(--color-white)}.bg-white\\/60{background-color:#fff9}@supports (color:color-mix(in lab, red, red)){.bg-white\\/60{background-color:color-mix(in oklab,var(--color-white)60%,transparent)}}.bg-white\\/70{background-color:#ffffffb3}@supports (color:color-mix(in lab, red, red)){.bg-white\\/70{background-color:color-mix(in oklab,var(--color-white)70%,transparent)}}.bg-gradient-to-br{--tw-gradient-position:to bottom right in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}.from-primary\\/5{--tw-gradient-from:color-mix(in srgb,rgb(var(--primary))5%,transparent)}@supports (color:color-mix(in lab, red, red)){.from-primary\\/5{--tw-gradient-from:color-mix(in oklab,var(--color-primary)5%,transparent)}}.from-primary\\/5{--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.from-slate-50{--tw-gradient-from:var(--color-slate-50);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.via-blue-200{--tw-gradient-via:var(--color-blue-200);--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.via-transparent{--tw-gradient-via:transparent;--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-via-stops)}.to-secondary\\/5{--tw-gradient-to:color-mix(in srgb,rgb(var(--secondary))5%,transparent)}@supports (color:color-mix(in lab, red, red)){.to-secondary\\/5{--tw-gradient-to:color-mix(in oklab,var(--color-secondary)5%,transparent)}}.to-secondary\\/5{--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.to-slate-100{--tw-gradient-to:var(--color-slate-100);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.fill-current{fill:currentColor}.p-0{padding:calc(var(--spacing)*0)}.p-1{padding:calc(var(--spacing)*1)}.p-1\\.5{padding:calc(var(--spacing)*1.5)}.p-2{padding:calc(var(--spacing)*2)}.p-3{padding:calc(var(--spacing)*3)}.p-4{padding:calc(var(--spacing)*4)}.p-6{padding:calc(var(--spacing)*6)}.p-8{padding:calc(var(--spacing)*8)}.p-\\[1px\\]{padding:1px}.px-1{padding-inline:calc(var(--spacing)*1)}.px-2{padding-inline:calc(var(--spacing)*2)}.px-2\\.5{padding-inline:calc(var(--spacing)*2.5)}.px-3{padding-inline:calc(var(--spacing)*3)}.px-4{padding-inline:calc(var(--spacing)*4)}.px-5{padding-inline:calc(var(--spacing)*5)}.px-8{padding-inline:calc(var(--spacing)*8)}.py-0\\.5{padding-block:calc(var(--spacing)*.5)}.py-1{padding-block:calc(var(--spacing)*1)}.py-1\\.5{padding-block:calc(var(--spacing)*1.5)}.py-2{padding-block:calc(var(--spacing)*2)}.py-2\\.5{padding-block:calc(var(--spacing)*2.5)}.py-3{padding-block:calc(var(--spacing)*3)}.py-4{padding-block:calc(var(--spacing)*4)}.py-6{padding-block:calc(var(--spacing)*6)}.py-8{padding-block:calc(var(--spacing)*8)}.pt-0{padding-top:calc(var(--spacing)*0)}.pt-1{padding-top:calc(var(--spacing)*1)}.pt-3{padding-top:calc(var(--spacing)*3)}.pt-4{padding-top:calc(var(--spacing)*4)}.pt-6{padding-top:calc(var(--spacing)*6)}.pr-2{padding-right:calc(var(--spacing)*2)}.pr-2\\.5{padding-right:calc(var(--spacing)*2.5)}.pr-8{padding-right:calc(var(--spacing)*8)}.pb-2{padding-bottom:calc(var(--spacing)*2)}.pb-3{padding-bottom:calc(var(--spacing)*3)}.pb-4{padding-bottom:calc(var(--spacing)*4)}.pl-2\\.5{padding-left:calc(var(--spacing)*2.5)}.pl-4{padding-left:calc(var(--spacing)*4)}.pl-8{padding-left:calc(var(--spacing)*8)}.pl-12{padding-left:calc(var(--spacing)*12)}.text-center{text-align:center}.text-left{text-align:left}.align-middle{vertical-align:middle}.font-mono{font-family:var(--font-mono)}.text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.text-base{font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height))}.text-lg{font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height))}.text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.text-xs{font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.text-\\[0\\.8rem\\]{font-size:.8rem}.leading-6{--tw-leading:calc(var(--spacing)*6);line-height:calc(var(--spacing)*6)}.leading-none{--tw-leading:1;line-height:1}.leading-relaxed{--tw-leading:var(--leading-relaxed);line-height:var(--leading-relaxed)}.leading-tight{--tw-leading:var(--leading-tight);line-height:var(--leading-tight)}.font-bold{--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold)}.font-medium{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.font-normal{--tw-font-weight:var(--font-weight-normal);font-weight:var(--font-weight-normal)}.font-semibold{--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.tracking-tight{--tw-tracking:var(--tracking-tight);letter-spacing:var(--tracking-tight)}.tracking-wider{--tw-tracking:var(--tracking-wider);letter-spacing:var(--tracking-wider)}.tracking-widest{--tw-tracking:var(--tracking-widest);letter-spacing:var(--tracking-widest)}.break-words{overflow-wrap:break-word}.break-all{word-break:break-all}.whitespace-nowrap{white-space:nowrap}.text-\\[\\#00617b\\]{color:#00617b}.text-accent-foreground{color:var(--color-accent-foreground)}.text-black{color:var(--color-black)}.text-blue-600{color:var(--color-blue-600)}.text-blue-700{color:var(--color-blue-700)}.text-card-foreground{color:var(--color-card-foreground)}.text-current{color:currentColor}.text-destructive{color:var(--color-destructive)}.text-destructive-foreground{color:var(--color-destructive-foreground)}.text-foreground{color:var(--color-foreground)}.text-foreground\\/50{color:color-mix(in srgb,rgb(var(--foreground))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.text-foreground\\/50{color:color-mix(in oklab,var(--color-foreground)50%,transparent)}}.text-gray-400{color:var(--color-gray-400)}.text-gray-500{color:var(--color-gray-500)}.text-gray-600{color:var(--color-gray-600)}.text-gray-700{color:var(--color-gray-700)}.text-gray-800{color:var(--color-gray-800)}.text-gray-900{color:var(--color-gray-900)}.text-green-600{color:var(--color-green-600)}.text-green-700{color:var(--color-green-700)}.text-muted-foreground{color:var(--color-muted-foreground)}.text-popover-foreground{color:var(--color-popover-foreground)}.text-primary{color:var(--color-primary)}.text-primary-foreground{color:var(--color-primary-foreground)}.text-red-600{color:var(--color-red-600)}.text-red-700{color:var(--color-red-700)}.text-red-900{color:var(--color-red-900)}.text-secondary-foreground{color:var(--color-secondary-foreground)}.text-sidebar-foreground{color:var(--color-sidebar-foreground)}.text-sidebar-foreground\\/70{color:color-mix(in srgb,rgb(var(--sidebar-foreground))70%,transparent)}@supports (color:color-mix(in lab, red, red)){.text-sidebar-foreground\\/70{color:color-mix(in oklab,var(--color-sidebar-foreground)70%,transparent)}}.text-slate-500{color:var(--color-slate-500)}.text-slate-600{color:var(--color-slate-600)}.text-slate-800{color:var(--color-slate-800)}.text-slate-900{color:var(--color-slate-900)}.uppercase{text-transform:uppercase}.tabular-nums{--tw-numeric-spacing:tabular-nums;font-variant-numeric:var(--tw-ordinal,)var(--tw-slashed-zero,)var(--tw-numeric-figure,)var(--tw-numeric-spacing,)var(--tw-numeric-fraction,)}.underline{text-decoration-line:underline}.underline-offset-2{text-underline-offset:2px}.underline-offset-4{text-underline-offset:4px}.opacity-0{opacity:0}.opacity-50{opacity:.5}.opacity-60{opacity:.6}.opacity-70{opacity:.7}.opacity-90{opacity:.9}.shadow-2xl{--tw-shadow:0 25px 50px -12px var(--tw-shadow-color,#00000040);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-border\\)\\)\\]{--tw-shadow:0 0 0 1px var(--tw-shadow-color,hsl(var(--sidebar-border)));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-md{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-none{--tw-shadow:0 0 #0000;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-xl{--tw-shadow:0 20px 25px -5px var(--tw-shadow-color,#0000001a),0 8px 10px -6px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-0{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-2{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-slate-900\\/5{--tw-shadow-color:#0f172b0d}@supports (color:color-mix(in lab, red, red)){.shadow-slate-900\\/5{--tw-shadow-color:color-mix(in oklab,color-mix(in oklab,var(--color-slate-900)5%,transparent)var(--tw-shadow-alpha),transparent)}}.ring-ring{--tw-ring-color:var(--color-ring)}.ring-sidebar-ring{--tw-ring-color:var(--color-sidebar-ring)}.ring-offset-background{--tw-ring-offset-color:var(--color-background)}.outline{outline-style:var(--tw-outline-style);outline-width:1px}.blur-3xl{--tw-blur:blur(var(--blur-3xl));filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.backdrop-blur-md{--tw-backdrop-blur:blur(var(--blur-md));-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.backdrop-blur-sm{--tw-backdrop-blur:blur(var(--blur-sm));-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\\[left\\,right\\,width\\]{transition-property:left,right,width;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\\[margin\\,opa\\]{transition-property:margin,opa;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\\[width\\,height\\,padding\\]{transition-property:width,height,padding;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\\[width\\]{transition-property:width;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-all{transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-opacity{transition-property:opacity;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-transform{transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.delay-1000{transition-delay:1s}.duration-200{--tw-duration:.2s;transition-duration:.2s}.duration-300{--tw-duration:.3s;transition-duration:.3s}.duration-1000{--tw-duration:1s;transition-duration:1s}.ease-in-out{--tw-ease:var(--ease-in-out);transition-timing-function:var(--ease-in-out)}.ease-linear{--tw-ease:linear;transition-timing-function:linear}.ease-out{--tw-ease:var(--ease-out);transition-timing-function:var(--ease-out)}.outline-none{--tw-outline-style:none;outline-style:none}.select-none{-webkit-user-select:none;user-select:none}.group-focus-within\\/menu-item\\:opacity-100:is(:where(.group\\/menu-item):focus-within *){opacity:1}@media (hover:hover){.group-hover\\:opacity-100:is(:where(.group):hover *),.group-hover\\/menu-item\\:opacity-100:is(:where(.group\\/menu-item):hover *){opacity:1}}.group-has-\\[\\[data-sidebar\\=menu-action\\]\\]\\/menu-item\\:pr-8:is(:where(.group\\/menu-item):has([data-sidebar=menu-action]) *){padding-right:calc(var(--spacing)*8)}.group-data-\\[collapsible\\=icon\\]\\:-mt-8:is(:where(.group)[data-collapsible=icon] *){margin-top:calc(var(--spacing)*-8)}.group-data-\\[collapsible\\=icon\\]\\:hidden:is(:where(.group)[data-collapsible=icon] *){display:none}.group-data-\\[collapsible\\=icon\\]\\:\\!size-8:is(:where(.group)[data-collapsible=icon] *){width:calc(var(--spacing)*8)!important;height:calc(var(--spacing)*8)!important}.group-data-\\[collapsible\\=icon\\]\\:w-\\[--sidebar-width-icon\\]:is(:where(.group)[data-collapsible=icon] *){width:--sidebar-width-icon}.group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)_\\+_theme\\(spacing\\.4\\)\\)\\]:is(:where(.group)[data-collapsible=icon] *){width:calc(var(--sidebar-width-icon) + 1rem)}.group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)_\\+_theme\\(spacing\\.4\\)_\\+2px\\)\\]:is(:where(.group)[data-collapsible=icon] *){width:calc(var(--sidebar-width-icon) + 1rem + 2px)}.group-data-\\[collapsible\\=icon\\]\\:overflow-hidden:is(:where(.group)[data-collapsible=icon] *){overflow:hidden}.group-data-\\[collapsible\\=icon\\]\\:\\!p-0:is(:where(.group)[data-collapsible=icon] *){padding:calc(var(--spacing)*0)!important}.group-data-\\[collapsible\\=icon\\]\\:\\!p-2:is(:where(.group)[data-collapsible=icon] *){padding:calc(var(--spacing)*2)!important}.group-data-\\[collapsible\\=icon\\]\\:opacity-0:is(:where(.group)[data-collapsible=icon] *){opacity:0}.group-data-\\[collapsible\\=offcanvas\\]\\:right-\\[calc\\(var\\(--sidebar-width-tablet\\)\\*-1\\)\\]:is(:where(.group)[data-collapsible=offcanvas] *){right:calc(var(--sidebar-width-tablet)*-1)}.group-data-\\[collapsible\\=offcanvas\\]\\:left-\\[calc\\(var\\(--sidebar-width-tablet\\)\\*-1\\)\\]:is(:where(.group)[data-collapsible=offcanvas] *){left:calc(var(--sidebar-width-tablet)*-1)}.group-data-\\[collapsible\\=offcanvas\\]\\:w-0:is(:where(.group)[data-collapsible=offcanvas] *){width:calc(var(--spacing)*0)}.group-data-\\[collapsible\\=offcanvas\\]\\:translate-x-0:is(:where(.group)[data-collapsible=offcanvas] *){--tw-translate-x:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.group-data-\\[side\\=left\\]\\:-right-4:is(:where(.group)[data-side=left] *){right:calc(var(--spacing)*-4)}.group-data-\\[side\\=left\\]\\:border-r:is(:where(.group)[data-side=left] *){border-right-style:var(--tw-border-style);border-right-width:1px}.group-data-\\[side\\=right\\]\\:left-0:is(:where(.group)[data-side=right] *){left:calc(var(--spacing)*0)}.group-data-\\[side\\=right\\]\\:rotate-180:is(:where(.group)[data-side=right] *){rotate:180deg}.group-data-\\[side\\=right\\]\\:border-l:is(:where(.group)[data-side=right] *){border-left-style:var(--tw-border-style);border-left-width:1px}.group-data-\\[state\\=open\\]\\:rotate-180:is(:where(.group)[data-state=open] *){rotate:180deg}.group-data-\\[variant\\=floating\\]\\:rounded-lg:is(:where(.group)[data-variant=floating] *){border-radius:var(--radius-lg)}.group-data-\\[variant\\=floating\\]\\:border:is(:where(.group)[data-variant=floating] *){border-style:var(--tw-border-style);border-width:1px}.group-data-\\[variant\\=floating\\]\\:border-sidebar-border:is(:where(.group)[data-variant=floating] *){border-color:var(--color-sidebar-border)}.group-data-\\[variant\\=floating\\]\\:shadow:is(:where(.group)[data-variant=floating] *){--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.group-\\[\\.destructive\\]\\:border-muted\\/40:is(:where(.group).destructive *){border-color:color-mix(in srgb,rgb(var(--muted))40%,transparent)}@supports (color:color-mix(in lab, red, red)){.group-\\[\\.destructive\\]\\:border-muted\\/40:is(:where(.group).destructive *){border-color:color-mix(in oklab,var(--color-muted)40%,transparent)}}.group-\\[\\.destructive\\]\\:text-red-300:is(:where(.group).destructive *){color:var(--color-red-300)}.group-\\[\\.toast\\]\\:bg-muted:is(:where(.group).toast *){background-color:var(--color-muted)}.group-\\[\\.toast\\]\\:bg-primary:is(:where(.group).toast *){background-color:var(--color-primary)}.group-\\[\\.toast\\]\\:text-muted-foreground:is(:where(.group).toast *){color:var(--color-muted-foreground)}.group-\\[\\.toast\\]\\:text-primary-foreground:is(:where(.group).toast *){color:var(--color-primary-foreground)}.group-\\[\\.toaster\\]\\:border-border:is(:where(.group).toaster *){border-color:var(--color-border)}.group-\\[\\.toaster\\]\\:bg-background:is(:where(.group).toaster *){background-color:var(--color-background)}.group-\\[\\.toaster\\]\\:text-foreground:is(:where(.group).toaster *){color:var(--color-foreground)}.group-\\[\\.toaster\\]\\:shadow-lg:is(:where(.group).toaster *){--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover:hover){.peer-hover\\/menu-button\\:text-sidebar-accent-foreground:is(:where(.peer\\/menu-button):hover~*){color:var(--color-sidebar-accent-foreground)}}.peer-disabled\\:cursor-not-allowed:is(:where(.peer):disabled~*){cursor:not-allowed}.peer-disabled\\:opacity-70:is(:where(.peer):disabled~*){opacity:.7}.peer-data-\\[active\\=true\\]\\/menu-button\\:text-sidebar-accent-foreground:is(:where(.peer\\/menu-button)[data-active=true]~*){color:var(--color-sidebar-accent-foreground)}.peer-data-\\[size\\=default\\]\\/menu-button\\:top-1\\.5:is(:where(.peer\\/menu-button)[data-size=default]~*){top:calc(var(--spacing)*1.5)}.peer-data-\\[size\\=lg\\]\\/menu-button\\:top-2\\.5:is(:where(.peer\\/menu-button)[data-size=lg]~*){top:calc(var(--spacing)*2.5)}.peer-data-\\[size\\=sm\\]\\/menu-button\\:top-1:is(:where(.peer\\/menu-button)[data-size=sm]~*){top:calc(var(--spacing)*1)}.peer-data-\\[variant\\=inset\\]\\:min-h-\\[calc\\(100svh-theme\\(spacing\\.4\\)\\)\\]:is(:where(.peer)[data-variant=inset]~*){min-height:calc(100svh - 1rem)}.file\\:border-0::file-selector-button{border-style:var(--tw-border-style);border-width:0}.file\\:bg-transparent::file-selector-button{background-color:#0000}.file\\:text-sm::file-selector-button{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.file\\:font-medium::file-selector-button{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.file\\:text-foreground::file-selector-button{color:var(--color-foreground)}.placeholder\\:text-muted-foreground::placeholder{color:var(--color-muted-foreground)}.after\\:absolute:after{content:var(--tw-content);position:absolute}.after\\:-inset-2:after{content:var(--tw-content);inset:calc(var(--spacing)*-2)}.after\\:inset-y-0:after{content:var(--tw-content);inset-block:calc(var(--spacing)*0)}.after\\:left-1\\/2:after{content:var(--tw-content);left:50%}.after\\:w-1:after{content:var(--tw-content);width:calc(var(--spacing)*1)}.after\\:w-\\[2px\\]:after{content:var(--tw-content);width:2px}.after\\:-translate-x-1\\/2:after{content:var(--tw-content);--tw-translate-x:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.group-data-\\[collapsible\\=offcanvas\\]\\:after\\:left-full:is(:where(.group)[data-collapsible=offcanvas] *):after{content:var(--tw-content);left:100%}.first\\:rounded-l-md:first-child{border-top-left-radius:var(--radius-md);border-bottom-left-radius:var(--radius-md)}.first\\:border-l:first-child{border-left-style:var(--tw-border-style);border-left-width:1px}.last\\:rounded-r-md:last-child{border-top-right-radius:var(--radius-md);border-bottom-right-radius:var(--radius-md)}.focus-within\\:relative:focus-within{position:relative}.focus-within\\:z-20:focus-within{z-index:20}@media (hover:hover){.hover\\:translate-y-\\[-2px\\]:hover{--tw-translate-y:-2px;translate:var(--tw-translate-x)var(--tw-translate-y)}.hover\\:cursor-pointer:hover{cursor:pointer}.hover\\:border-\\[\\#0052cc\\]\\/20:hover{border-color:oklab(48.0172% -.0332745 -.198382/.2)}.hover\\:border-\\[\\#0078d7\\]\\/20:hover{border-color:oklab(56.9708% -.0527268 -.161904/.2)}.hover\\:border-\\[\\#00617b\\]\\/30:hover{border-color:oklab(45.7853% -.0617035 -.0602073/.3)}.hover\\:border-\\[\\#24292e\\]\\/20:hover{border-color:oklab(27.8058% -.00433138 -.0108552/.2)}.hover\\:border-\\[\\#fc6d26\\]\\/20:hover{border-color:oklab(70.1676% .140013 .130359/.2)}.hover\\:border-blue-300:hover{border-color:var(--color-blue-300)}.hover\\:\\!bg-gray-50:hover{background-color:var(--color-gray-50)!important}.hover\\:bg-\\[\\#f0f6fc\\]:hover{background-color:#f0f6fc}.hover\\:bg-\\[\\#f4f8ff\\]:hover{background-color:#f4f8ff}.hover\\:bg-\\[\\#f6f8fa\\]:hover{background-color:#f6f8fa}.hover\\:bg-\\[\\#fef8f6\\]:hover{background-color:#fef8f6}.hover\\:bg-accent:hover{background-color:var(--color-accent)}.hover\\:bg-destructive\\/80:hover{background-color:color-mix(in srgb,rgb(var(--destructive))80%,transparent)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-destructive\\/80:hover{background-color:color-mix(in oklab,var(--color-destructive)80%,transparent)}}.hover\\:bg-destructive\\/90:hover{background-color:color-mix(in srgb,rgb(var(--destructive))90%,transparent)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-destructive\\/90:hover{background-color:color-mix(in oklab,var(--color-destructive)90%,transparent)}}.hover\\:bg-gray-50:hover{background-color:var(--color-gray-50)}.hover\\:bg-muted:hover{background-color:var(--color-muted)}.hover\\:bg-muted\\/50:hover{background-color:color-mix(in srgb,rgb(var(--muted))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-muted\\/50:hover{background-color:color-mix(in oklab,var(--color-muted)50%,transparent)}}.hover\\:bg-platyfend-600:hover{background-color:var(--color-platyfend-600)}.hover\\:bg-primary:hover{background-color:var(--color-primary)}.hover\\:bg-primary\\/80:hover{background-color:color-mix(in srgb,rgb(var(--primary))80%,transparent)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-primary\\/80:hover{background-color:color-mix(in oklab,var(--color-primary)80%,transparent)}}.hover\\:bg-primary\\/90:hover{background-color:color-mix(in srgb,rgb(var(--primary))90%,transparent)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-primary\\/90:hover{background-color:color-mix(in oklab,var(--color-primary)90%,transparent)}}.hover\\:bg-secondary:hover{background-color:var(--color-secondary)}.hover\\:bg-secondary\\/80:hover{background-color:color-mix(in srgb,rgb(var(--secondary))80%,transparent)}@supports (color:color-mix(in lab, red, red)){.hover\\:bg-secondary\\/80:hover{background-color:color-mix(in oklab,var(--color-secondary)80%,transparent)}}.hover\\:bg-sidebar-accent:hover{background-color:var(--color-sidebar-accent)}.hover\\:bg-white:hover{background-color:var(--color-white)}.hover\\:\\!text-gray-700:hover{color:var(--color-gray-700)!important}.hover\\:\\!text-gray-900:hover{color:var(--color-gray-900)!important}.hover\\:text-\\[\\#0052cc\\]:hover{color:#0052cc}.hover\\:text-\\[\\#0078d7\\]:hover{color:#0078d7}.hover\\:text-\\[\\#24292e\\]:hover{color:#24292e}.hover\\:text-\\[\\#fc6d26\\]:hover{color:#fc6d26}.hover\\:text-accent-foreground:hover{color:var(--color-accent-foreground)}.hover\\:text-blue-700:hover{color:var(--color-blue-700)}.hover\\:text-foreground:hover{color:var(--color-foreground)}.hover\\:text-gray-800:hover{color:var(--color-gray-800)}.hover\\:text-muted-foreground:hover{color:var(--color-muted-foreground)}.hover\\:text-primary-foreground:hover{color:var(--color-primary-foreground)}.hover\\:text-sidebar-accent-foreground:hover{color:var(--color-sidebar-accent-foreground)}.hover\\:text-slate-800:hover{color:var(--color-slate-800)}.hover\\:underline:hover{text-decoration-line:underline}.hover\\:opacity-100:hover{opacity:1}.hover\\:shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-accent\\)\\)\\]:hover{--tw-shadow:0 0 0 1px var(--tw-shadow-color,hsl(var(--sidebar-accent)));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.hover\\:shadow-lg:hover{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.hover\\:shadow-md:hover{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.hover\\:shadow-\\[\\#00617b\\]\\/10:hover{--tw-shadow-color:#00617b1a}@supports (color:color-mix(in lab, red, red)){.hover\\:shadow-\\[\\#00617b\\]\\/10:hover{--tw-shadow-color:color-mix(in oklab,oklab(45.7853% -.0617035 -.0602073/.1) var(--tw-shadow-alpha),transparent)}}.group-data-\\[collapsible\\=offcanvas\\]\\:hover\\:bg-sidebar:is(:where(.group)[data-collapsible=offcanvas] *):hover{background-color:var(--color-sidebar)}.group-\\[\\.destructive\\]\\:hover\\:border-destructive\\/30:is(:where(.group).destructive *):hover{border-color:color-mix(in srgb,rgb(var(--destructive))30%,transparent)}@supports (color:color-mix(in lab, red, red)){.group-\\[\\.destructive\\]\\:hover\\:border-destructive\\/30:is(:where(.group).destructive *):hover{border-color:color-mix(in oklab,var(--color-destructive)30%,transparent)}}.group-\\[\\.destructive\\]\\:hover\\:bg-destructive:is(:where(.group).destructive *):hover{background-color:var(--color-destructive)}.group-\\[\\.destructive\\]\\:hover\\:text-destructive-foreground:is(:where(.group).destructive *):hover{color:var(--color-destructive-foreground)}.group-\\[\\.destructive\\]\\:hover\\:text-red-50:is(:where(.group).destructive *):hover{color:var(--color-red-50)}.hover\\:after\\:bg-sidebar-border:hover:after{content:var(--tw-content);background-color:var(--color-sidebar-border)}}.focus\\:bg-accent:focus{background-color:var(--color-accent)}.focus\\:bg-primary:focus{background-color:var(--color-primary)}.focus\\:text-accent-foreground:focus{color:var(--color-accent-foreground)}.focus\\:text-primary-foreground:focus{color:var(--color-primary-foreground)}.focus\\:opacity-100:focus{opacity:1}.focus\\:ring-2:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\\:ring-ring:focus{--tw-ring-color:var(--color-ring)}.focus\\:ring-offset-2:focus{--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus\\:outline-none:focus{--tw-outline-style:none;outline-style:none}.group-\\[\\.destructive\\]\\:focus\\:ring-destructive:is(:where(.group).destructive *):focus{--tw-ring-color:var(--color-destructive)}.group-\\[\\.destructive\\]\\:focus\\:ring-red-400:is(:where(.group).destructive *):focus{--tw-ring-color:var(--color-red-400)}.group-\\[\\.destructive\\]\\:focus\\:ring-offset-red-600:is(:where(.group).destructive *):focus{--tw-ring-offset-color:var(--color-red-600)}.focus-visible\\:ring-1:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\\:ring-2:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\\:ring-ring:focus-visible{--tw-ring-color:var(--color-ring)}.focus-visible\\:ring-sidebar-ring:focus-visible{--tw-ring-color:var(--color-sidebar-ring)}.focus-visible\\:ring-offset-1:focus-visible{--tw-ring-offset-width:1px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus-visible\\:ring-offset-2:focus-visible{--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus-visible\\:ring-offset-background:focus-visible{--tw-ring-offset-color:var(--color-background)}.focus-visible\\:outline-hidden:focus-visible{--tw-outline-style:none;outline-style:none}@media (forced-colors:active){.focus-visible\\:outline-hidden:focus-visible{outline-offset:2px;outline:2px solid #0000}}.focus-visible\\:outline-none:focus-visible{--tw-outline-style:none;outline-style:none}.active\\:translate-y-\\[0px\\]:active{--tw-translate-y:0px;translate:var(--tw-translate-x)var(--tw-translate-y)}.active\\:bg-sidebar-accent:active{background-color:var(--color-sidebar-accent)}.active\\:text-sidebar-accent-foreground:active{color:var(--color-sidebar-accent-foreground)}.active\\:shadow-sm:active{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.disabled\\:pointer-events-none:disabled{pointer-events:none}.disabled\\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\\:opacity-50:disabled,.has-\\[\\:disabled\\]\\:opacity-50:has(:disabled){opacity:.5}.has-\\[\\[data-variant\\=inset\\]\\]\\:bg-sidebar:has([data-variant=inset]){background-color:var(--color-sidebar)}.aria-disabled\\:pointer-events-none[aria-disabled=true]{pointer-events:none}.aria-disabled\\:opacity-50[aria-disabled=true]{opacity:.5}.aria-selected\\:bg-accent[aria-selected=true]{background-color:var(--color-accent)}.aria-selected\\:bg-accent\\/50[aria-selected=true]{background-color:color-mix(in srgb,rgb(var(--accent))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.aria-selected\\:bg-accent\\/50[aria-selected=true]{background-color:color-mix(in oklab,var(--color-accent)50%,transparent)}}.aria-selected\\:text-accent-foreground[aria-selected=true]{color:var(--color-accent-foreground)}.aria-selected\\:text-muted-foreground[aria-selected=true]{color:var(--color-muted-foreground)}.aria-selected\\:opacity-30[aria-selected=true]{opacity:.3}.aria-selected\\:opacity-100[aria-selected=true]{opacity:1}.data-\\[active\\]\\:bg-accent\\/50[data-active]{background-color:color-mix(in srgb,rgb(var(--accent))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.data-\\[active\\]\\:bg-accent\\/50[data-active]{background-color:color-mix(in oklab,var(--color-accent)50%,transparent)}}.data-\\[active\\=true\\]\\:bg-\\[\\#00617b\\]\\/10[data-active=true]{background-color:oklab(45.7853% -.0617035 -.0602073/.1)}.data-\\[active\\=true\\]\\:bg-sidebar-accent[data-active=true]{background-color:var(--color-sidebar-accent)}.data-\\[active\\=true\\]\\:font-medium[data-active=true]{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.data-\\[active\\=true\\]\\:font-semibold[data-active=true]{--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.data-\\[active\\=true\\]\\:text-\\[\\#00617b\\][data-active=true]{color:#00617b}.data-\\[active\\=true\\]\\:text-sidebar-accent-foreground[data-active=true]{color:var(--color-sidebar-accent-foreground)}.data-\\[disabled\\]\\:pointer-events-none[data-disabled]{pointer-events:none}.data-\\[disabled\\]\\:opacity-50[data-disabled]{opacity:.5}.data-\\[disabled\\=true\\]\\:pointer-events-none[data-disabled=true]{pointer-events:none}.data-\\[disabled\\=true\\]\\:opacity-50[data-disabled=true]{opacity:.5}.data-\\[panel-group-direction\\=vertical\\]\\:h-px[data-panel-group-direction=vertical]{height:1px}.data-\\[panel-group-direction\\=vertical\\]\\:w-full[data-panel-group-direction=vertical]{width:100%}.data-\\[panel-group-direction\\=vertical\\]\\:flex-col[data-panel-group-direction=vertical]{flex-direction:column}.data-\\[panel-group-direction\\=vertical\\]\\:after\\:left-0[data-panel-group-direction=vertical]:after{content:var(--tw-content);left:calc(var(--spacing)*0)}.data-\\[panel-group-direction\\=vertical\\]\\:after\\:h-1[data-panel-group-direction=vertical]:after{content:var(--tw-content);height:calc(var(--spacing)*1)}.data-\\[panel-group-direction\\=vertical\\]\\:after\\:w-full[data-panel-group-direction=vertical]:after{content:var(--tw-content);width:100%}.data-\\[panel-group-direction\\=vertical\\]\\:after\\:translate-x-0[data-panel-group-direction=vertical]:after{content:var(--tw-content);--tw-translate-x:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[panel-group-direction\\=vertical\\]\\:after\\:-translate-y-1\\/2[data-panel-group-direction=vertical]:after{content:var(--tw-content);--tw-translate-y:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[selected\\=\\'true\\'\\]\\:bg-accent[data-selected=true]{background-color:var(--color-accent)}.data-\\[selected\\=true\\]\\:text-accent-foreground[data-selected=true]{color:var(--color-accent-foreground)}.data-\\[side\\=bottom\\]\\:translate-y-1[data-side=bottom]{--tw-translate-y:calc(var(--spacing)*1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[side\\=left\\]\\:-translate-x-1[data-side=left]{--tw-translate-x:calc(var(--spacing)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[side\\=right\\]\\:translate-x-1[data-side=right]{--tw-translate-x:calc(var(--spacing)*1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[side\\=top\\]\\:-translate-y-1[data-side=top]{--tw-translate-y:calc(var(--spacing)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[state\\=active\\]\\:bg-background[data-state=active]{background-color:var(--color-background)}.data-\\[state\\=active\\]\\:text-foreground[data-state=active]{color:var(--color-foreground)}.data-\\[state\\=active\\]\\:shadow-sm[data-state=active]{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.data-\\[state\\=checked\\]\\:translate-x-5[data-state=checked]{--tw-translate-x:calc(var(--spacing)*5);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[state\\=checked\\]\\:bg-primary[data-state=checked]{background-color:var(--color-primary)}.data-\\[state\\=checked\\]\\:text-primary-foreground[data-state=checked]{color:var(--color-primary-foreground)}.data-\\[state\\=closed\\]\\:animate-accordion-up[data-state=closed]{animation:var(--animate-accordion-up)}.data-\\[state\\=closed\\]\\:duration-300[data-state=closed]{--tw-duration:.3s;transition-duration:.3s}.data-\\[state\\=on\\]\\:bg-accent[data-state=on]{background-color:var(--color-accent)}.data-\\[state\\=on\\]\\:text-accent-foreground[data-state=on]{color:var(--color-accent-foreground)}.data-\\[state\\=open\\]\\:animate-accordion-down[data-state=open]{animation:var(--animate-accordion-down)}.data-\\[state\\=open\\]\\:bg-accent[data-state=open]{background-color:var(--color-accent)}.data-\\[state\\=open\\]\\:bg-accent\\/50[data-state=open]{background-color:color-mix(in srgb,rgb(var(--accent))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.data-\\[state\\=open\\]\\:bg-accent\\/50[data-state=open]{background-color:color-mix(in oklab,var(--color-accent)50%,transparent)}}.data-\\[state\\=open\\]\\:bg-secondary[data-state=open]{background-color:var(--color-secondary)}.data-\\[state\\=open\\]\\:bg-sidebar-accent[data-state=open]{background-color:var(--color-sidebar-accent)}.data-\\[state\\=open\\]\\:text-accent-foreground[data-state=open]{color:var(--color-accent-foreground)}.data-\\[state\\=open\\]\\:text-muted-foreground[data-state=open]{color:var(--color-muted-foreground)}.data-\\[state\\=open\\]\\:text-sidebar-accent-foreground[data-state=open]{color:var(--color-sidebar-accent-foreground)}.data-\\[state\\=open\\]\\:opacity-100[data-state=open]{opacity:1}.data-\\[state\\=open\\]\\:duration-500[data-state=open]{--tw-duration:.5s;transition-duration:.5s}@media (hover:hover){.data-\\[state\\=open\\]\\:hover\\:bg-sidebar-accent[data-state=open]:hover{background-color:var(--color-sidebar-accent)}.data-\\[state\\=open\\]\\:hover\\:text-sidebar-accent-foreground[data-state=open]:hover{color:var(--color-sidebar-accent-foreground)}}.data-\\[state\\=selected\\]\\:bg-muted[data-state=selected]{background-color:var(--color-muted)}.data-\\[state\\=unchecked\\]\\:translate-x-0[data-state=unchecked]{--tw-translate-x:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[state\\=unchecked\\]\\:bg-input[data-state=unchecked]{background-color:var(--color-input)}.data-\\[swipe\\=cancel\\]\\:translate-x-0[data-swipe=cancel]{--tw-translate-x:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[swipe\\=end\\]\\:translate-x-\\[var\\(--radix-toast-swipe-end-x\\)\\][data-swipe=end]{--tw-translate-x:var(--radix-toast-swipe-end-x);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[swipe\\=move\\]\\:translate-x-\\[var\\(--radix-toast-swipe-move-x\\)\\][data-swipe=move]{--tw-translate-x:var(--radix-toast-swipe-move-x);translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\\[swipe\\=move\\]\\:transition-none[data-swipe=move]{transition-property:none}@media (min-width:640px){.sm\\:top-auto{top:auto}.sm\\:right-0{right:calc(var(--spacing)*0)}.sm\\:bottom-0{bottom:calc(var(--spacing)*0)}.sm\\:mt-0{margin-top:calc(var(--spacing)*0)}.sm\\:mt-4{margin-top:calc(var(--spacing)*4)}.sm\\:mt-8{margin-top:calc(var(--spacing)*8)}.sm\\:mb-8{margin-bottom:calc(var(--spacing)*8)}.sm\\:mb-10{margin-bottom:calc(var(--spacing)*10)}.sm\\:mb-12{margin-bottom:calc(var(--spacing)*12)}.sm\\:flex{display:flex}.sm\\:h-13{height:calc(var(--spacing)*13)}.sm\\:h-\\[15rem\\]{height:15rem}.sm\\:w-60{width:calc(var(--spacing)*60)}.sm\\:w-\\[280px\\]{width:280px}.sm\\:w-auto{width:auto}.sm\\:max-w-sm{max-width:var(--container-sm)}.sm\\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.sm\\:flex-col{flex-direction:column}.sm\\:flex-row{flex-direction:row}.sm\\:justify-end{justify-content:flex-end}.sm\\:gap-2\\.5{gap:calc(var(--spacing)*2.5)}.sm\\:gap-6{gap:calc(var(--spacing)*6)}:where(.sm\\:space-y-0>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*0)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*0)*calc(1 - var(--tw-space-y-reverse)))}:where(.sm\\:space-y-8>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*8)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*8)*calc(1 - var(--tw-space-y-reverse)))}:where(.sm\\:space-x-2>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}:where(.sm\\:space-x-4>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*4)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-x-reverse)))}.sm\\:rounded-lg{border-radius:var(--radius-lg)}.sm\\:p-6{padding:calc(var(--spacing)*6)}.sm\\:p-8{padding:calc(var(--spacing)*8)}.sm\\:px-4{padding-inline:calc(var(--spacing)*4)}.sm\\:px-6{padding-inline:calc(var(--spacing)*6)}.sm\\:py-3{padding-block:calc(var(--spacing)*3)}.sm\\:py-12{padding-block:calc(var(--spacing)*12)}.sm\\:pt-6{padding-top:calc(var(--spacing)*6)}.sm\\:pt-8{padding-top:calc(var(--spacing)*8)}.sm\\:text-left{text-align:left}.sm\\:text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.sm\\:text-3xl{font-size:var(--text-3xl);line-height:var(--tw-leading,var(--text-3xl--line-height))}.sm\\:text-base{font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height))}.sm\\:text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.sm\\:text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.sm\\:leading-8{--tw-leading:calc(var(--spacing)*8);line-height:calc(var(--spacing)*8)}}@media (min-width:768px){.md\\:absolute{position:absolute}.md\\:w-\\[var\\(--radix-navigation-menu-viewport-width\\)\\]{width:var(--radix-navigation-menu-viewport-width)}.md\\:w-auto{width:auto}.md\\:max-w-\\[420px\\]{max-width:420px}.md\\:text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.md\\:opacity-0{opacity:0}.md\\:peer-data-\\[variant\\=inset\\]\\:m-2:is(:where(.peer)[data-variant=inset]~*){margin:calc(var(--spacing)*2)}.md\\:peer-data-\\[variant\\=inset\\]\\:ml-0:is(:where(.peer)[data-variant=inset]~*){margin-left:calc(var(--spacing)*0)}.md\\:peer-data-\\[variant\\=inset\\]\\:rounded-xl:is(:where(.peer)[data-variant=inset]~*){border-radius:var(--radius-xl)}.md\\:peer-data-\\[variant\\=inset\\]\\:shadow:is(:where(.peer)[data-variant=inset]~*){--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.md\\:peer-data-\\[state\\=collapsed\\]\\:peer-data-\\[variant\\=inset\\]\\:ml-2:is(:where(.peer)[data-state=collapsed]~*):is(:where(.peer)[data-variant=inset]~*){margin-left:calc(var(--spacing)*2)}}.after\\:md\\:hidden:after{content:var(--tw-content)}@media (min-width:768px){.after\\:md\\:hidden:after{display:none}}@media (min-width:1024px){.lg\\:block{display:block}.lg\\:flex{display:flex}.lg\\:hidden{display:none}.lg\\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.lg\\:p-8{padding:calc(var(--spacing)*8)}.lg\\:px-8{padding-inline:calc(var(--spacing)*8)}.lg\\:text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.lg\\:text-4xl{font-size:var(--text-4xl);line-height:var(--tw-leading,var(--text-4xl--line-height))}}@media (min-width:1280px){.xl\\:w-\\[--sidebar-width\\]{width:--sidebar-width}.xl\\:group-data-\\[collapsible\\=offcanvas\\]\\:right-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\]:is(:where(.group)[data-collapsible=offcanvas] *){right:calc(var(--sidebar-width)*-1)}.xl\\:group-data-\\[collapsible\\=offcanvas\\]\\:left-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\]:is(:where(.group)[data-collapsible=offcanvas] *){left:calc(var(--sidebar-width)*-1)}}@media (prefers-color-scheme:dark){.dark\\:border-destructive{border-color:var(--color-destructive)}}.\\[\\&_\\.recharts-cartesian-axis-tick_text\\]\\:fill-muted-foreground .recharts-cartesian-axis-tick text{fill:var(--color-muted-foreground)}.\\[\\&_\\.recharts-cartesian-grid_line\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border\\/50 .recharts-cartesian-grid line[stroke=\\#ccc]{stroke:color-mix(in srgb,rgb(var(--border))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.\\[\\&_\\.recharts-cartesian-grid_line\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border\\/50 .recharts-cartesian-grid line[stroke=\\#ccc]{stroke:color-mix(in oklab,var(--color-border)50%,transparent)}}.\\[\\&_\\.recharts-curve\\.recharts-tooltip-cursor\\]\\:stroke-border .recharts-curve.recharts-tooltip-cursor{stroke:var(--color-border)}.\\[\\&_\\.recharts-dot\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent .recharts-dot[stroke=\\#fff]{stroke:#0000}.\\[\\&_\\.recharts-layer\\]\\:outline-none .recharts-layer{--tw-outline-style:none;outline-style:none}.\\[\\&_\\.recharts-polar-grid_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border .recharts-polar-grid [stroke=\\#ccc]{stroke:var(--color-border)}.\\[\\&_\\.recharts-radial-bar-background-sector\\]\\:fill-muted .recharts-radial-bar-background-sector,.\\[\\&_\\.recharts-rectangle\\.recharts-tooltip-cursor\\]\\:fill-muted .recharts-rectangle.recharts-tooltip-cursor{fill:var(--color-muted)}.\\[\\&_\\.recharts-reference-line_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border .recharts-reference-line [stroke=\\#ccc]{stroke:var(--color-border)}.\\[\\&_\\.recharts-sector\\]\\:outline-none .recharts-sector{--tw-outline-style:none;outline-style:none}.\\[\\&_\\.recharts-sector\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent .recharts-sector[stroke=\\#fff]{stroke:#0000}.\\[\\&_\\.recharts-surface\\]\\:outline-none .recharts-surface{--tw-outline-style:none;outline-style:none}.\\[\\&_\\[cmdk-group-heading\\]\\]\\:px-2 [cmdk-group-heading]{padding-inline:calc(var(--spacing)*2)}.\\[\\&_\\[cmdk-group-heading\\]\\]\\:py-1\\.5 [cmdk-group-heading]{padding-block:calc(var(--spacing)*1.5)}.\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-xs [cmdk-group-heading]{font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.\\[\\&_\\[cmdk-group-heading\\]\\]\\:font-medium [cmdk-group-heading]{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-muted-foreground [cmdk-group-heading]{color:var(--color-muted-foreground)}.\\[\\&_\\[cmdk-group\\]\\]\\:px-2 [cmdk-group]{padding-inline:calc(var(--spacing)*2)}.\\[\\&_\\[cmdk-group\\]\\:not\\(\\[hidden\\]\\)_\\~\\[cmdk-group\\]\\]\\:pt-0 [cmdk-group]:not([hidden])~[cmdk-group]{padding-top:calc(var(--spacing)*0)}.\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:h-5 [cmdk-input-wrapper] svg{height:calc(var(--spacing)*5)}.\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:w-5 [cmdk-input-wrapper] svg{width:calc(var(--spacing)*5)}.\\[\\&_\\[cmdk-input\\]\\]\\:h-12 [cmdk-input]{height:calc(var(--spacing)*12)}.\\[\\&_\\[cmdk-item\\]\\]\\:px-2 [cmdk-item]{padding-inline:calc(var(--spacing)*2)}.\\[\\&_\\[cmdk-item\\]\\]\\:py-3 [cmdk-item]{padding-block:calc(var(--spacing)*3)}.\\[\\&_\\[cmdk-item\\]_svg\\]\\:h-5 [cmdk-item] svg{height:calc(var(--spacing)*5)}.\\[\\&_\\[cmdk-item\\]_svg\\]\\:w-5 [cmdk-item] svg{width:calc(var(--spacing)*5)}.\\[\\&_p\\]\\:leading-relaxed p{--tw-leading:var(--leading-relaxed);line-height:var(--leading-relaxed)}.\\[\\&_svg\\]\\:pointer-events-none svg{pointer-events:none}.\\[\\&_svg\\]\\:size-4 svg{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.\\[\\&_svg\\]\\:shrink-0 svg{flex-shrink:0}.\\[\\&_tr\\]\\:border-b tr{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.\\[\\&_tr\\:last-child\\]\\:border-0 tr:last-child{border-style:var(--tw-border-style);border-width:0}.\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:bg-accent:has([aria-selected]){background-color:var(--color-accent)}.first\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-l-md:first-child:has([aria-selected]){border-top-left-radius:var(--radius-md);border-bottom-left-radius:var(--radius-md)}.last\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-r-md:last-child:has([aria-selected]){border-top-right-radius:var(--radius-md);border-bottom-right-radius:var(--radius-md)}.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-outside\\)\\]\\:bg-accent\\/50:has([aria-selected].day-outside){background-color:color-mix(in srgb,rgb(var(--accent))50%,transparent)}@supports (color:color-mix(in lab, red, red)){.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-outside\\)\\]\\:bg-accent\\/50:has([aria-selected].day-outside){background-color:color-mix(in oklab,var(--color-accent)50%,transparent)}}.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-range-end\\)\\]\\:rounded-r-md:has([aria-selected].day-range-end){border-top-right-radius:var(--radius-md);border-bottom-right-radius:var(--radius-md)}.\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0:has([role=checkbox]){padding-right:calc(var(--spacing)*0)}.\\[\\&\\>button\\]\\:hidden>button{display:none}.\\[\\&\\>span\\]\\:line-clamp-1>span{-webkit-line-clamp:1;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.\\[\\&\\>span\\:last-child\\]\\:truncate>span:last-child{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.\\[\\&\\>svg\\]\\:absolute>svg{position:absolute}.\\[\\&\\>svg\\]\\:top-4>svg{top:calc(var(--spacing)*4)}.\\[\\&\\>svg\\]\\:left-4>svg{left:calc(var(--spacing)*4)}.\\[\\&\\>svg\\]\\:size-3\\.5>svg{width:calc(var(--spacing)*3.5);height:calc(var(--spacing)*3.5)}.\\[\\&\\>svg\\]\\:size-4>svg{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.\\[\\&\\>svg\\]\\:h-2\\.5>svg{height:calc(var(--spacing)*2.5)}.\\[\\&\\>svg\\]\\:h-3>svg{height:calc(var(--spacing)*3)}.\\[\\&\\>svg\\]\\:w-2\\.5>svg{width:calc(var(--spacing)*2.5)}.\\[\\&\\>svg\\]\\:w-3>svg{width:calc(var(--spacing)*3)}.\\[\\&\\>svg\\]\\:shrink-0>svg{flex-shrink:0}.\\[\\&\\>svg\\]\\:text-destructive>svg{color:var(--color-destructive)}.\\[\\&\\>svg\\]\\:text-foreground>svg{color:var(--color-foreground)}.\\[\\&\\>svg\\]\\:text-muted-foreground>svg{color:var(--color-muted-foreground)}.\\[\\&\\>svg\\]\\:text-sidebar-accent-foreground>svg{color:var(--color-sidebar-accent-foreground)}.\\[\\&\\>svg\\+div\\]\\:translate-y-\\[-3px\\]>svg+div{--tw-translate-y:-3px;translate:var(--tw-translate-x)var(--tw-translate-y)}.\\[\\&\\>svg\\~\\*\\]\\:pl-7>svg~*{padding-left:calc(var(--spacing)*7)}.\\[\\&\\>tr\\]\\:last\\:border-b-0>tr:last-child{border-bottom-style:var(--tw-border-style);border-bottom-width:0}.\\[\\&\\[data-panel-group-direction\\=vertical\\]\\>div\\]\\:rotate-90[data-panel-group-direction=vertical]>div{rotate:90deg}.\\[\\&\\[data-state\\=open\\]\\>svg\\]\\:rotate-180[data-state=open]>svg{rotate:180deg}[data-side=left] .\\[\\[data-side\\=left\\]_\\&\\]\\:cursor-w-resize{cursor:w-resize}[data-side=left][data-collapsible=offcanvas] .\\[\\[data-side\\=left\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-right-2{right:calc(var(--spacing)*-2)}[data-side=left][data-state=collapsed] .\\[\\[data-side\\=left\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-e-resize,[data-side=right] .\\[\\[data-side\\=right\\]_\\&\\]\\:cursor-e-resize{cursor:e-resize}[data-side=right][data-collapsible=offcanvas] .\\[\\[data-side\\=right\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-left-2{left:calc(var(--spacing)*-2)}[data-side=right][data-state=collapsed] .\\[\\[data-side\\=right\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-w-resize{cursor:w-resize}}:root{--background:248 250 252;--foreground:0 0 0;--card:255 255 255;--card-foreground:30 41 59;--popover:255 255 255;--popover-foreground:30 41 59;--primary:0 97 123;--primary-light:51 140 168;--primary-dark:0 70 89;--primary-foreground:255 255 255;--secondary:0 0 0;--secondary-foreground:71 85 105;--muted:241 245 249;--muted-foreground:100 116 139;--accent:51 140 168;--accent-foreground:0 97 123;--destructive:239 68 68;--destructive-foreground:255 255 255;--border:0 0 0;--input:226 232 240;--ring:0 97 123;--chart-1:20 184 166;--chart-2:14 165 233;--chart-3:30 58 138;--sidebar:255 255 255;--sidebar-foreground:71 85 105;--sidebar-primary:0 97 123;--sidebar-primary-foreground:255 255 255;--sidebar-accent:51 140 168;--sidebar-accent-foreground:0 97 123;--sidebar-border:226 232 240;--sidebar-ring:0 97 123;--shadow-2xs:0 1px 3px 0px #0000000d;--shadow-xs:0 1px 3px 0px #0000000d;--shadow-sm:0 1px 3px 0px #0000001a,0 1px 2px -1px #0000001a;--shadow:0 1px 3px 0px #0000001a,0 1px 2px -1px #0000001a;--shadow-md:0 1px 3px 0px #0000001a,0 2px 4px -1px #0000001a;--shadow-lg:0 1px 3px 0px #0000001a,0 4px 6px -1px #0000001a;--shadow-xl:0 1px 3px 0px #0000001a,0 8px 10px -1px #0000001a;--shadow-2xl:0 1px 3px 0px #00000040}@keyframes accordion-down{0%{height:0}to{height:var(--radix-accordion-content-height)}}@keyframes accordion-up{0%{height:var(--radix-accordion-content-height)}to{height:0}}@keyframes fade-in{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}@keyframes slide-in{0%{opacity:0;transform:translate(-10px)}to{opacity:1;transform:translate(0)}}@keyframes glow{0%,to{box-shadow:0 0 20px #14b8a64d}50%{box-shadow:0 0 30px #14b8a680}}@keyframes float{0%,to{transform:translateY(0)}50%{transform:translateY(-5px)}}@keyframes scroll{0%{transform:translate(0)}to{transform:translate(-50%)}}.animate-scroll{animation:30s linear infinite scroll}.animate-scroll:hover{animation-play-state:paused}.container-responsive{padding-left:1rem;padding-right:1rem}@media (min-width:640px){.container-responsive{padding-left:1.5rem;padding-right:1.5rem}}@media (min-width:1024px){.container-responsive{padding-left:2rem;padding-right:2rem}}.grid-responsive{gap:1rem}@media (min-width:640px){.grid-responsive{gap:1.5rem}}@media (min-width:1024px){.grid-responsive{gap:2rem}}.text-responsive-xs{font-size:.75rem;line-height:1rem}@media (min-width:640px){.text-responsive-xs{font-size:.875rem;line-height:1.25rem}}.text-responsive-sm{font-size:.875rem;line-height:1.25rem}@media (min-width:640px){.text-responsive-sm{font-size:1rem;line-height:1.5rem}}.text-responsive-base{font-size:1rem;line-height:1.5rem}@media (min-width:640px){.text-responsive-base{font-size:1.125rem;line-height:1.75rem}}.text-responsive-lg{font-size:1.125rem;line-height:1.75rem}@media (min-width:640px){.text-responsive-lg{font-size:1.25rem;line-height:1.75rem}}@media (min-width:1024px){.text-responsive-lg{font-size:1.5rem;line-height:2rem}}.text-responsive-xl{font-size:1.25rem;line-height:1.75rem}@media (min-width:640px){.text-responsive-xl{font-size:1.5rem;line-height:2rem}}@media (min-width:1024px){.text-responsive-xl{font-size:1.875rem;line-height:2.25rem}}.text-responsive-2xl{font-size:1.5rem;line-height:2rem}@media (min-width:640px){.text-responsive-2xl{font-size:1.875rem;line-height:2.25rem}}@media (min-width:1024px){.text-responsive-2xl{font-size:2.25rem;line-height:2.5rem}}.text-responsive-3xl{font-size:1.875rem;line-height:2.25rem}@media (min-width:640px){.text-responsive-3xl{font-size:2.25rem;line-height:2.5rem}}@media (min-width:1024px){.text-responsive-3xl{font-size:3rem;line-height:1}}.space-responsive>*+*{margin-top:1rem}@media (min-width:640px){.space-responsive>*+*{margin-top:1.5rem}}@media (min-width:1024px){.space-responsive>*+*{margin-top:2rem}}.p-responsive{padding:1rem}@media (min-width:640px){.p-responsive{padding:1.5rem}}@media (min-width:1024px){.p-responsive{padding:2rem}}.px-responsive{padding-left:1rem;padding-right:1rem}@media (min-width:640px){.px-responsive{padding-left:1.5rem;padding-right:1.5rem}}@media (min-width:1024px){.px-responsive{padding-left:2rem;padding-right:2rem}}.py-responsive{padding-top:1rem;padding-bottom:1rem}@media (min-width:640px){.py-responsive{padding-top:1.5rem;padding-bottom:1.5rem}}@media (min-width:1024px){.py-responsive{padding-top:2rem;padding-bottom:2rem}}.card-responsive{padding:1rem}@media (min-width:640px){.card-responsive{padding:1.5rem}}@media (min-width:1024px){.card-responsive{padding:2rem}}.flex-responsive{flex-direction:column;display:flex}@media (min-width:640px){.flex-responsive{flex-direction:row}}.hidden-mobile{display:none}@media (min-width:640px){.hidden-mobile{display:block}}.mobile-only{display:block}@media (min-width:640px){.mobile-only{display:none}}.btn-responsive{padding:.5rem 1rem}@media (min-width:640px){.btn-responsive{padding:.75rem 1.5rem}}.input-responsive{width:100%;padding:.5rem .75rem}@media (min-width:640px){.input-responsive{padding:.75rem 1rem}}.safe-area-padding{padding-left:env(safe-area-inset-left);padding-right:env(safe-area-inset-right);padding-top:env(safe-area-inset-top);padding-bottom:env(safe-area-inset-bottom)}.table-responsive{margin-left:-1rem;margin-right:-1rem;overflow-x:auto}@media (min-width:640px){.table-responsive{margin-left:0;margin-right:0}}.table-responsive table{min-width:100%}.modal-responsive{width:100%;max-width:32rem;margin-left:1rem;margin-right:1rem}@media (min-width:640px){.modal-responsive{margin-left:auto;margin-right:auto}}.sidebar-responsive{width:100%}@media (min-width:640px){.sidebar-responsive{width:16rem}}@media (min-width:1024px){.sidebar-responsive{width:18rem}}.sidebar-container{width:100%;min-height:100vh;display:flex;overflow:hidden}.sidebar-main-content{flex-direction:column;flex:1;width:100%;min-width:0;display:flex}@media (min-width:1024px) and (max-width:1279px){:root{--sidebar-width-responsive:14rem}}@media (min-width:1280px){:root{--sidebar-width-responsive:16rem}}.form-responsive>*+*{margin-top:1rem}@media (min-width:640px){.form-responsive>*+*{margin-top:1.5rem}}.form-responsive .form-group{flex-direction:column;display:flex}.form-responsive .form-group>*+*{margin-top:.5rem}@media (min-width:640px){.form-responsive .form-group{flex-direction:row;align-items:center}.form-responsive .form-group>*+*{margin-top:0;margin-left:1rem}}.form-responsive label{font-size:.875rem;font-weight:500}@media (min-width:640px){.form-responsive label{flex-shrink:0;width:8rem}}.form-responsive input,.form-responsive select,.form-responsive textarea{width:100%}@media (min-width:640px){.form-responsive input,.form-responsive select,.form-responsive textarea{flex:1}}.nav-responsive{flex-direction:column;display:flex}.nav-responsive>*+*{margin-top:.5rem}@media (min-width:640px){.nav-responsive{flex-direction:row}.nav-responsive>*+*{margin-top:0;margin-left:1rem}}.cards-responsive{grid-template-columns:repeat(1,minmax(0,1fr));gap:1rem;display:grid}@media (min-width:640px){.cards-responsive{grid-template-columns:repeat(2,minmax(0,1fr));gap:1.5rem}}@media (min-width:1024px){.cards-responsive{grid-template-columns:repeat(3,minmax(0,1fr))}}@media (min-width:1280px){.cards-responsive{grid-template-columns:repeat(4,minmax(0,1fr))}}.dashboard-responsive{flex-direction:column;min-height:100vh;display:flex}@media (min-width:1024px){.dashboard-responsive{flex-direction:row}}.dashboard-content{flex:1;padding:1rem;overflow:auto}@media (min-width:640px){.dashboard-content{padding:1.5rem}}@media (min-width:1024px){.dashboard-content{padding:2rem}}.heading-responsive-1{font-size:1.5rem;font-weight:700;line-height:2rem}@media (min-width:640px){.heading-responsive-1{font-size:1.875rem;line-height:2.25rem}}@media (min-width:1024px){.heading-responsive-1{font-size:2.25rem;line-height:2.5rem}}.heading-responsive-2{font-size:1.25rem;font-weight:600;line-height:1.75rem}@media (min-width:640px){.heading-responsive-2{font-size:1.5rem;line-height:2rem}}@media (min-width:1024px){.heading-responsive-2{font-size:1.875rem;line-height:2.25rem}}.heading-responsive-3{font-size:1.125rem;font-weight:500;line-height:1.75rem}@media (min-width:640px){.heading-responsive-3{font-size:1.25rem;line-height:1.75rem}}@media (min-width:1024px){.heading-responsive-3{font-size:1.5rem;line-height:2rem}}.button-group-responsive{flex-direction:column;display:flex}.button-group-responsive>*+*{margin-top:.5rem}@media (min-width:640px){.button-group-responsive{flex-direction:row}.button-group-responsive>*+*{margin-top:0;margin-left:.5rem}}.image-responsive{object-fit:cover;width:100%;height:auto}.list-responsive>*+*{margin-top:.5rem}@media (min-width:640px){.list-responsive>*+*{margin-top:.75rem}}.list-item-responsive{flex-direction:column;padding:.75rem;display:flex}@media (min-width:640px){.list-item-responsive{flex-direction:row;justify-content:space-between;align-items:center;padding:1rem}}@media (max-width:639px){.container{padding-left:1rem;padding-right:1rem}.nav-horizontal{flex-direction:column;display:flex}.nav-horizontal>*+*{margin-top:.5rem}.btn-mobile-full{width:100%}.text-mobile-sm{font-size:.875rem;line-height:1.25rem}.complex-layout{display:none}.mobile-layout{display:block}}@media (min-width:641px) and (max-width:1024px){.tablet-grid{grid-template-columns:repeat(2,minmax(0,1fr))}.tablet-sidebar{width:14rem}}@media (min-width:1025px){.desktop-hover:hover{transition:transform .2s;transform:scale(1.05)}.desktop-grid{grid-template-columns:repeat(4,minmax(0,1fr))}}@property --tw-translate-x{syntax:\"*\";inherits:false;initial-value:0}@property --tw-translate-y{syntax:\"*\";inherits:false;initial-value:0}@property --tw-translate-z{syntax:\"*\";inherits:false;initial-value:0}@property --tw-rotate-x{syntax:\"*\";inherits:false}@property --tw-rotate-y{syntax:\"*\";inherits:false}@property --tw-rotate-z{syntax:\"*\";inherits:false}@property --tw-skew-x{syntax:\"*\";inherits:false}@property --tw-skew-y{syntax:\"*\";inherits:false}@property --tw-space-y-reverse{syntax:\"*\";inherits:false;initial-value:0}@property --tw-space-x-reverse{syntax:\"*\";inherits:false;initial-value:0}@property --tw-border-style{syntax:\"*\";inherits:false;initial-value:solid}@property --tw-gradient-position{syntax:\"*\";inherits:false}@property --tw-gradient-from{syntax:\"<color>\";inherits:false;initial-value:#0000}@property --tw-gradient-via{syntax:\"<color>\";inherits:false;initial-value:#0000}@property --tw-gradient-to{syntax:\"<color>\";inherits:false;initial-value:#0000}@property --tw-gradient-stops{syntax:\"*\";inherits:false}@property --tw-gradient-via-stops{syntax:\"*\";inherits:false}@property --tw-gradient-from-position{syntax:\"<length-percentage>\";inherits:false;initial-value:0%}@property --tw-gradient-via-position{syntax:\"<length-percentage>\";inherits:false;initial-value:50%}@property --tw-gradient-to-position{syntax:\"<length-percentage>\";inherits:false;initial-value:100%}@property --tw-leading{syntax:\"*\";inherits:false}@property --tw-font-weight{syntax:\"*\";inherits:false}@property --tw-tracking{syntax:\"*\";inherits:false}@property --tw-ordinal{syntax:\"*\";inherits:false}@property --tw-slashed-zero{syntax:\"*\";inherits:false}@property --tw-numeric-figure{syntax:\"*\";inherits:false}@property --tw-numeric-spacing{syntax:\"*\";inherits:false}@property --tw-numeric-fraction{syntax:\"*\";inherits:false}@property --tw-shadow{syntax:\"*\";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:\"*\";inherits:false}@property --tw-shadow-alpha{syntax:\"<percentage>\";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:\"*\";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:\"*\";inherits:false}@property --tw-inset-shadow-alpha{syntax:\"<percentage>\";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:\"*\";inherits:false}@property --tw-ring-shadow{syntax:\"*\";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:\"*\";inherits:false}@property --tw-inset-ring-shadow{syntax:\"*\";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:\"*\";inherits:false}@property --tw-ring-offset-width{syntax:\"<length>\";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:\"*\";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:\"*\";inherits:false;initial-value:0 0 #0000}@property --tw-outline-style{syntax:\"*\";inherits:false;initial-value:solid}@property --tw-blur{syntax:\"*\";inherits:false}@property --tw-brightness{syntax:\"*\";inherits:false}@property --tw-contrast{syntax:\"*\";inherits:false}@property --tw-grayscale{syntax:\"*\";inherits:false}@property --tw-hue-rotate{syntax:\"*\";inherits:false}@property --tw-invert{syntax:\"*\";inherits:false}@property --tw-opacity{syntax:\"*\";inherits:false}@property --tw-saturate{syntax:\"*\";inherits:false}@property --tw-sepia{syntax:\"*\";inherits:false}@property --tw-drop-shadow{syntax:\"*\";inherits:false}@property --tw-drop-shadow-color{syntax:\"*\";inherits:false}@property --tw-drop-shadow-alpha{syntax:\"<percentage>\";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:\"*\";inherits:false}@property --tw-backdrop-blur{syntax:\"*\";inherits:false}@property --tw-backdrop-brightness{syntax:\"*\";inherits:false}@property --tw-backdrop-contrast{syntax:\"*\";inherits:false}@property --tw-backdrop-grayscale{syntax:\"*\";inherits:false}@property --tw-backdrop-hue-rotate{syntax:\"*\";inherits:false}@property --tw-backdrop-invert{syntax:\"*\";inherits:false}@property --tw-backdrop-opacity{syntax:\"*\";inherits:false}@property --tw-backdrop-saturate{syntax:\"*\";inherits:false}@property --tw-backdrop-sepia{syntax:\"*\";inherits:false}@property --tw-duration{syntax:\"*\";inherits:false}@property --tw-ease{syntax:\"*\";inherits:false}@property --tw-content{syntax:\"*\";inherits:false;initial-value:\"\"}@keyframes spin{to{transform:rotate(360deg)}}@keyframes pulse{50%{opacity:.5}}"], "sourceRoot": ""}